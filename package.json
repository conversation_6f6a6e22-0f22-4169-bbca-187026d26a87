{"name": "@moxfive-llc/common", "version": "2.0.27", "repository": "git://github.com/moxfive-llc/common.git", "publishConfig": {"registry": "https://npm.pkg.github.com"}, "description": "", "main": "./build/index.js", "types": "./build/index.d.ts", "files": ["build/**/*"], "scripts": {"clean": "del-cli ./build/*", "build": "npm run clean && tsc", "lint": "eslint **/*.ts"}, "keywords": [], "author": "MOXFIVE", "license": "ISC", "devDependencies": {"@eslint/eslintrc": "3.2.0", "@eslint/js": "9.20.0", "@types/archiver": "6.0.3", "@types/multer": "1.4.12", "@types/node": "22.10.5", "@types/toobusy-js": "0.5.4", "@typescript-eslint/eslint-plugin": "8.19.0", "@typescript-eslint/parser": "8.19.0", "del-cli": "6.0.0", "eslint": "9.17.0", "eslint-plugin-security": "3.0.1", "globals": "15.15.0", "typescript": "5.7.2"}, "dependencies": {"@azure/identity": "4.5.0", "@azure/keyvault-secrets": "4.9.0", "@azure/storage-blob": "12.26.0", "@types/cookie-session": "2.0.49", "@types/express": "5.0.0", "@types/jsonwebtoken": "9.0.7", "archiver": "7.0.1", "cookie-session": "2.1.0", "express": "4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "7.2.1", "jsonwebtoken": "9.0.2", "moment-timezone": "0.5.46", "mongoose": "8.9.6", "multer": "1.4.5-lts.1", "nats": "2.28.2", "redis": "4.7.0", "toobusy-js": "0.5.1"}}