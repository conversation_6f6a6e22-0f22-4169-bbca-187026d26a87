import { Request, Response, NextFunction } from "express";
// import { CustomError } from "../errors/custom-error";
import { CustomError } from "../errors/custom-error";
import { MulterError } from "multer";
import { ConflictErrorCodes } from "../utils/conflict-error-codes";
import { auditLogPublisherWrapper } from "../utils/audit-log-publisher-wrapper";

export const errorHandler = async (
    err: Error,
    req: Request,
    res: Response,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    next: NextFunction
) => {
    if (req.initiatorPermission) {
        req.initiatorPermission.action = req.action || "";
        req.initiatorPermission.unauthorizedAccess = !((!!req.authorizedAccess) || !!(req.actions && req.actions.includes(req.action)));
    }

    if (req.response) {
        req.response.result = "Failure";
        req.response.responseStatusCode = 500;
    }

    if (err instanceof CustomError) {
        const errors = err.serializeErrors();

        if (req.response) {
            if (err.statusCode === 200) {
                req.response.result = "Success";
            }
            req.response.responseStatusCode = err.statusCode;
            req.response.platformResponseStatusCode = errors.code;
            req.response.resultReason = errors.detail;
            req.response.body = JSON.stringify(errors);
            req.response.timestamp = Date.now();
        }

        // Send the error response
        res.status(err.statusCode).send(errors);

        // Publish the data to audit log
        await auditLogPublisherWrapper({
            request: req.request,
            response: req.response,
            initiatorPermission: req.initiatorPermission,
            targets: [],
            modifiedProperties: []
        }, req.service);

        return;
    }

    if (err instanceof MulterError) {
        const fileSize = {
            title: "Resource Value Unacceptable",
            detail: "The file size cannot exceed allowed limit which is of 5 MB.",
            type: "resourceValueUnacceptable",
            code: ConflictErrorCodes.ALLOWED_FILE_SIZE
        };

        if (req.response) {
            req.response.responseStatusCode = 409;
            req.response.platformResponseStatusCode = ConflictErrorCodes.ALLOWED_FILE_SIZE;
            req.response.resultReason = "The file size cannot exceed allowed limit which is of 5 MB.";
            req.response.body = JSON.stringify(fileSize);
            req.response.timestamp = Date.now();
        }

        // Send the error response
        res.status(409).send(fileSize);

        // Publish the data to audit log
        await auditLogPublisherWrapper({
            request: req.request,
            response: req.response,
            initiatorPermission: req.initiatorPermission,
            targets: [],
            modifiedProperties: []
        }, req.service);

        return;
    }

    console.error("Common.ErrorHandler");
    console.error(err);

    const internalServer = {
        "title": "Internal Server Error",
        "detail": "Sorry something went wrong, please try again. If problem persist then write <NAME_EMAIL>.",
        "type": "internalServerError",
        "code": 5002
    };

    if (req.response) {
        req.response.body = JSON.stringify(internalServer);
        req.response.timestamp = Date.now();
    }

    // Send the error response
    res.status(500).send(internalServer);

    // Publish the data to audit log
    await auditLogPublisherWrapper({
        request: req.request,
        response: req.response,
        initiatorPermission: req.initiatorPermission,
        targets: [],
        modifiedProperties: []
    }, req.service);
};
