import { Request, Response, NextFunction } from "express";
import { eventData } from "../interfaces/event-data";
import { requestLogObj } from "../interfaces/request-log-obj";
import { responseLogObj } from "../interfaces/response-log-obj";
import { initiatorPermissionLogObj } from "../interfaces/initiator-permission-log-obj";
import { generateUUID } from "../utils/generate-uuid";
import { auditLogPublisherWrapper } from "../utils/audit-log-publisher-wrapper";
import { APIToActionMapping } from "../utils/api-to-action-mapping";
import { eventServices } from "../utils/event-services";

declare global {
    namespace Express {
        interface Request {
            authorizedAccess?: boolean,
            service: string,
            request: requestLogObj,
            response: responseLogObj,
            initiatorPermission: initiatorPermissionLogObj
        }
    }
}

declare global {
    namespace Express {
        interface Response {
            sendResponse: (data: any, eventData: eventData) => void;
        }
    }
}

export const responseHandler = (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    // Get Action
    const method = req.method;
    const route = req.originalUrl.split("?").shift() as string;
    const actionDetails = APIToActionMapping.find(item => item.method === method && item.route === route);
    if (actionDetails) {
        req.action = actionDetails.action;
    }

    // Get service name
    const serviceName = route.split("/")[2] || "";
    // eslint-disable-next-line security/detect-object-injection
    req.service = eventServices[serviceName] || eventServices["authentication"];

    const request: requestLogObj = {
        requestId: generateUUID(),
        timestamp: Date.now(),
        method: method,
        api: route,
        userId: req.currentUser?.id || "",
        m5pTenantId: req.currentUser?.organizationId || "",
        applicationId: null,
        ip: req.ip ?? "",
        headers: {
            userAgent: req.headers["user-agent"] || "",
            referer: req.headers["referer"] || "",
            origin: req.hostname || "",
            acceptEncoding: typeof req.headers["accept-encoding"] === "string" ? req.headers["accept-encoding"] : (req.headers["accept-encoding"] || []).join(", "),
            xM5PHostname: req.headers["x-m5p-hostname"] as string || "",
            xM5PHostip: req.headers["x-m5p-hostip"] as string || "",
            contentType: req.headers["content-type"] || "",
            contentLength: req.headers["content-length"] || "",
            accept: req.headers["accept"] || ""
        },
        body: "",
        queryParams: {},
        pathParams: {}
    };

    if (req.query && Object.keys(req.query).length) {
        request.queryParams = req.query as { [key: string]: string };
    }

    if (req.params && Object.keys(req.params).length) {
        request.pathParams = req.params as { [key: string]: string };
    }

    const pathParams = Object.keys(req.params);
    if(pathParams.length) {
        pathParams.forEach(param => {
            // eslint-disable-next-line security/detect-object-injection
            request.api = request.api.replace(req.params[param], `:${param}`);
        });
    }

    if (req.body && Object.keys(req.body).length) {
        request.body = JSON.stringify(req.body);
    }

    const response: responseLogObj = {
        timestamp: 0,
        correlation: "",
        correlationId: "",
        result: "",
        resultReason: "",
        body: "",
        responseStatusCode: 0,
        platformResponseStatusCode: 0
    };

    const initiatorPermission: initiatorPermissionLogObj = {
        unauthorizedAccess: true,
        policies: [],
        action: ""
    };

    req.request = request;
    req.response = response;
    req.initiatorPermission = initiatorPermission;

    res.sendResponse = async (data: any, eventData?: eventData): Promise<void> => {
        req.initiatorPermission.action = req.action || "";
        req.initiatorPermission.unauthorizedAccess = !((!!req.authorizedAccess) || !!(req.actions && req.actions.includes(req.action)));
        if (eventData?.correlation) {
            req.response.correlation = eventData.correlation;
        }
        if (eventData?.correlationId) {
            req.response.correlationId = eventData.correlationId;
        }
        req.response.result = "Success";
        req.response.responseStatusCode = res.statusCode;
        req.response.body = JSON.stringify(data);
        req.response.timestamp = Date.now();

        res.send(data);

        // Send the data to audit log service using events
        await auditLogPublisherWrapper({
            request: req.request,
            response: req.response,
            initiatorPermission: req.initiatorPermission,
            targets: eventData && eventData.targets ? eventData.targets : [],
            modifiedProperties: eventData && eventData.modifiedProperties ? eventData.modifiedProperties : []
        }, req.service);
    };
    next();
};
