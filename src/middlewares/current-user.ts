/* eslint-disable max-depth */
/* eslint-disable max-statements */
import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import { UserAccountDisabledError } from "../errors/user-account-disabled-error";
import { UserOrganizationDisabledError } from "../errors/user-organization-disabled-error";
import { Organization, OrganizationDoc } from "../models/organization.model";
import { User, UserDoc } from "../models/user.model";
import { getUserName } from "../utils";
import { Application } from "../models/application.model";
import { LocalUser } from "../models/local-user.model";
import { InvalidApplicationOAuthError } from "../errors/invalid-application-oAuth-error";
import { ApplicationCredentialsExpiredOAuthError } from "../errors/application-crdentials-expired-oAuth-error";
import { InvalidTokenOAuthError } from "../errors/invalid-token-oAuth-error";

// test
interface UserPayload {
    id: string;
    email: string;
    organizationId: string;
    userName?: string;
    organizationName?: string;
    isSystemUser?: boolean;
    deviceId?: string | null;
}

interface ApplicationCredentials {
    applicationId: string;
}

declare global {
    namespace Express {
        interface Request {
            currentUser?: UserPayload;
            loggedInUser?: UserDoc | null
            loggedInUserOrganization?: OrganizationDoc | null
        }
    }
}

export const
    currentUser = async (
        req: Request,
        res: Response,
        next: NextFunction
    ) => {
        let userDetails: UserDoc | null = null;
        const isLocalDevelopment = process.env.LOCAL_ENVIRONMENT === "true";
        let deviceId: string | null = null;
        const authorizationHeader = String(req.headers.authorization);

        // If authorization token is passed
        if (authorizationHeader.startsWith("Bearer ")) {
            const authorizationHeaderSplit = authorizationHeader.split(" ");

            // If authorization header is not in propre format then return
            if ((authorizationHeaderSplit.length !== 2) || (authorizationHeaderSplit[0] !== "Bearer")) {
                return next();
            }

            try {
                const details = jwt.decode(
                    authorizationHeaderSplit[1],
                    { json: true }
                );

                if (req.request) {
                    // If this JWT is of application credentials type then verify the application credentials details
                    if ((details as ApplicationCredentials).applicationId) {
                        try {
                            jwt.verify(
                                authorizationHeaderSplit[1],
                                String(process.env.JWT_SECRET),
                                { algorithms: ["HS256"] }
                            );
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        } catch (err) {
                            throw new InvalidTokenOAuthError();
                        }

                        // Save application id in the request
                        req.request.applicationId = (details as ApplicationCredentials).applicationId;

                        // Fetch application details, if it is not found or deleted then throw an error
                        const applicationDetails = await Application.findById((details as ApplicationCredentials).applicationId).lean().exec();
                        if (!applicationDetails || applicationDetails.isDeleted) {
                            throw new InvalidApplicationOAuthError();
                        }

                        // Fetch user details and save user details in the request
                        userDetails = await User.findById(applicationDetails.user.id).lean().exec();
                        if (userDetails) {
                            req.request.userId = String(userDetails._id);
                            req.request.m5pTenantId = String(userDetails.organizationId);
                        }

                        // If application credentials is not enabled then throw an error
                        if (!applicationDetails.isEnabled) {
                            throw new InvalidApplicationOAuthError();
                        }

                        // If the secret is expired then throw an error
                        if (new Date(applicationDetails.expiresIn) <= new Date()) {
                            throw new ApplicationCredentialsExpiredOAuthError();
                        }
                    } else if ((details as UserPayload).id) { // Super Admin User, Called via Automation
                        jwt.verify(
                            authorizationHeaderSplit[1],
                            String(process.env.JWT_SECRET),
                            { algorithms: ["HS256"] }
                        );

                        // Fetch user details and save user details in the request
                        userDetails = await User.findById((details as UserPayload).id).lean().exec();

                        req.request.userId = (details as UserPayload).id;
                        req.request.m5pTenantId = (details as UserPayload).organizationId;
                    }
                }
            } catch (err) {
                console.error(err);
                next(err);
            }
        } else {
            const token = req.signedCookies.Token1;

            if (!isLocalDevelopment) {
                if (!token) {
                    return next();
                }

                const payloadDetails = jwt.decode(token, { json: true });
                if (payloadDetails && req.request) {
                    req.request.userId = payloadDetails.id;
                    req.request.m5pTenantId = payloadDetails.organizationId;
                }
            }

            try {
                if (isLocalDevelopment) {
                    const { localUserEmail } = req.query;
                    if (localUserEmail) {
                        userDetails = await User.findOne({ email: String(localUserEmail) }).lean().exec();
                    } else {
                        const localUser = await LocalUser.findOne({}).lean().exec();
                        if (!localUser) {
                            return next();
                        }

                        userDetails = await User.findOne({ email: localUser.email }).lean().exec();
                    }
                } else {
                    const userPayload = jwt.verify(
                        token,
                        String(process.env.JWT_SECRET),
                        { algorithms: ["HS256"] }
                    ) as UserPayload;

                    deviceId = userPayload.deviceId ?? null;
                    userDetails = await User.findById(userPayload.id).lean().exec();

                    // TODO: Currently deviceId is found then only check in allowedLoginTokens. After the Phase 3 we will mandatory check the deviceId
                    if (userDetails && deviceId) {
                        // Check whether deviceId is valid or not, if not then return
                        const deviceExists = (userDetails.allowedLoginTokens ?? []).map(String).includes(String(deviceId));
                        if (!deviceExists) {
                            return next();
                        }
                    }
                }
            } catch (err) {
                console.error(err);
            }
        }

        if (userDetails) {
            // If the user is disabled then throw an error
            if (!userDetails?.isEnabled) {
                throw new UserAccountDisabledError();
            }

            // Fetch the username
            const userName = getUserName({
                firstName: userDetails?.firstName as string,
                lastName: userDetails?.lastName as string,
                displayName: userDetails?.displayName as string
            });

            // Fetch the organization details, if it is not found or it is not enabled then throw an error
            const orgDetails = await Organization.findById(userDetails.organizationId).lean().exec();
            if (orgDetails && !orgDetails.isEnabled) {
                throw new UserOrganizationDisabledError();
            }

            // Save details in the currentUser object
            req.currentUser = {
                id: String(userDetails._id),
                email: userDetails.email,
                userName,
                organizationId: String(userDetails.organizationId),
                organizationName: orgDetails?.name,
                isSystemUser: userDetails.email === "<EMAIL>",
                deviceId
            };

            req.loggedInUser = userDetails;
            req.loggedInUserOrganization = orgDetails;
        }

        next();
    };
