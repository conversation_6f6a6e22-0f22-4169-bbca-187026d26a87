import { Request, Response, NextFunction } from "express";
import { InvalidTokenError } from "../errors/invalid-token-error";
import { Agreement } from "../models/agreement.model";
import { checkBypassRoute } from "../utils/check-bypass-route";
import { SignedAgreement } from "../models/signed-agreement.model";
import {
    TermsOfUseNotAcceptedResourceUnauthorizedError
} from "../errors/terms-of-use-not-accepted-resource-unauthorized";

export const requireAuth = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    if (!req.currentUser) {
        throw new InvalidTokenError();
    }

    // Check whether user signed the agreement or not
    const agreementId = req.params.agreementId || "";

    const bypassApisForAgreements = [
        { method: "GET", url: `/v1/users/me` },
        { method: "GET", url: `/v1/users/me/permissions` },
        { method: "GET", url: `/v2/authentication/users/me` },
        { method: "GET", url: `/v2/authentication/users/me/permissions` },
        { method: "GET", url: `/v1/agreements/mine` },
        { method: "GET", url: `/v1/agreements/${agreementId}` },
        { method: "POST", url: `/v1/agreements/sign` },
    ];

    // Check whether we should bypass this route or not
    const isBypassAPI = checkBypassRoute({ method: req.method, url: req.path }, bypassApisForAgreements);

    if (!isBypassAPI) {
        // Fetch all agreements
        const agreements = await Agreement.find({ retired: false, type: "Platform" }).lean().exec();
        if (agreements.length) {
            // Prepare agreementIds
            const agreementIds = agreements.map(agreement => String(agreement._id));

            // Fetch all the agreements that user signed
            const signedAgreements = await SignedAgreement.find({ userId: req.currentUser.id, agreementId: { $in: agreementIds } }).lean().exec();

            // If user has not signed all the agreements then throw an error
            if (signedAgreements.length !== agreements.length) {
                throw new TermsOfUseNotAcceptedResourceUnauthorizedError();
            }
        }

    }

    next();
};
