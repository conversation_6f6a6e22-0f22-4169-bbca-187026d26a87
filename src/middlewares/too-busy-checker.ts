import { Request, Response, NextFunction } from "express";
import toobusy from "toobusy-js";
import { publishErrorLog } from "../utils/publish-error-log";

export const toobusyChecker = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    if (toobusy()) {
        const respBody = {
            "title": "Service Unavailable",
            "detail": "Oops. Something went wrong. Please try again later.",
            "type": "serviceUnavailable",
            "code": 5031
        };
        // TODO: send an email to fixed recipients
        console.info("Server was too busy");
        res.status(503).send(respBody);

        return await publishErrorLog(req, 503, "Server was busy please try again.", JSON.stringify(respBody));
    } else {
        next();
    }
};
