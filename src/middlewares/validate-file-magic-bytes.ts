/* eslint-disable no-await-in-loop */
import { Request, Response, NextFunction, Express } from "express";
import { BasicResourceValueUnacceptableConflictError } from "../errors/basic-resource-value-unacceptable-conflict-error";
import { ConflictErrorCodes } from "../utils/conflict-error-codes";
import fs from "fs/promises";

const allowedMagicBytes: Record<string, Buffer | Buffer[]> = {
    "application/pdf": Buffer.from([0x25, 0x50, 0x44, 0x46, 0x2D]),               // PDF
    "image/jpeg": [Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]), Buffer.from([0xFF, 0xD8, 0xFF, 0xE1])], // JPEG
    "image/png": Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]),               // PNG
    "application/msword": Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]),               // MS Word (.doc)
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": Buffer.from([0x50, 0x4B, 0x03, 0x04]),              // MS Word (.docx)
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": Buffer.from([0x50, 0x4B, 0x03, 0x04]),              // MS Excel (.xlsx)
    "application/vnd.ms-excel": Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]),             // MS Excel (.xls)
    "image/svg+xml": [
        Buffer.from([0x3c, 0x3f, 0x78, 0x6d, 0x6c, 0x20, 0x76, 0x65]),
        Buffer.from([0x3c, 0x00, 0x3f, 0x00, 0x78, 0x00, 0x6d, 0x00, 0x6c, 0x00, 0x20]),
        Buffer.from([0x3c, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6d, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6d, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x4c, 0x6f, 0xa7, 0x94, 0x93, 0x40])
    ], // SVG (buffer from xml)
    // 'csv': No magic bytes for CSV (handled differently)
    "application/x-x509-ca-cert": [
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45,
            0x47, 0x49, 0x4E, 0x20, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20, 0x43,
            0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x20, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x44, 0x53, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x52, 0x45, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D])
    ], // Common start for X.509 certificates (DER format)
    "application/json": Buffer.from([0x7B]), // JSON starts with '{' (0x7B in ASCII)
    "application/pkix-cert": Buffer.from([0x30, 0x82]), // Similar to X.509 certs
    "application/xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ], // "<?xml" (ASCII)
    "application/octet-stream": [
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45,
            0x47, 0x49, 0x4E, 0x20, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20, 0x43,
            0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x20, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x44, 0x53, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x52, 0x45, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D])
    ],
    "text/xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ],
    "application/x-pem-file": [
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45,
            0x47, 0x49, 0x4E, 0x20, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20, 0x43,
            0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x20, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x44, 0x53, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x52, 0x45, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D])
    ],
    "application/x-x509-user-cert": [
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45,
            0x47, 0x49, 0x4E, 0x20, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20, 0x43,
            0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x20, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x44, 0x53, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D]),
        Buffer.from([0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x42, 0x45, 0x47, 0x49, 0x4E, 0x20,
            0x52, 0x45, 0x41, 0x20, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x20, 0x4B, 0x45, 0x59, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D])
    ],
    "application/rss+xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ],
    "application/atom+xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ],
    "application/xslt+xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ],
    "application/mathml+xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ],
    "application/xhtml+xml": [
        Buffer.from([0xEF, 0xBB, 0xBF, 0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20]),
        Buffer.from([0x45, 0x6C, 0x66, 0x46, 0x69, 0x6C, 0x65]),
        Buffer.from([0x4C, 0x6F, 0xA7, 0x94, 0x93, 0x40]),
        Buffer.from([0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x6C, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00]),
        Buffer.from([0x00, 0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x00, 0x3F, 0x00, 0x78, 0x00, 0x6D, 0x00, 0x6C, 0x00, 0x20]),
        Buffer.from([0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20])
    ]
};

// Function to verify magic bytes of any length using fs/promises
export const verifyMagicBytes = async (file: Express.Multer.File, throwError = true) => {
    const filePath = file.path;

    // If the file extension is csv then we won't check for magic bytes as it doesn't have that
    if (file.mimetype === "text/csv") {
        return true;
    } else if (!allowedMagicBytes[String(file.mimetype)]) {
        // If File extension is not in the allowedMagicBytes then throw an error

        if (throwError) {
            console.log("Checking 1st console")
            throw new BasicResourceValueUnacceptableConflictError(
                ConflictErrorCodes.ALLOWED_FILE_TYPES, "Invalid file type."
            );
        } else {
            return false;
        }
    }

    const magicBytes = allowedMagicBytes[String(file.mimetype)];

    let maxBytes = 0;

    // If it is array then find the max length from the array
    if (Array.isArray(magicBytes)) {
        maxBytes = Math.max(...magicBytes.map(element => element.length));
    } else {
        maxBytes = magicBytes.length;
    }

    // eslint-disable-next-line security/detect-non-literal-fs-filename
    const fileHandle = await fs.open(String(filePath), "r");
    const { buffer } = await fileHandle.read({ length: maxBytes, position: 0 });
    await fileHandle.close();

    // Handle multiple possible magic byte signatures for some types (e.g., JPEG)
    const signatures = Array.isArray(magicBytes) ? magicBytes : [magicBytes];

    for (const magic of signatures) {
        // Slice the buffer to compare only the required number of bytes
        const fileBytes = buffer.slice(0, magic.length);
        if (fileBytes.equals(magic)) {
            return true;
        }
    }

    if (throwError) {
        console.log("Checking 2nd console")
        throw new BasicResourceValueUnacceptableConflictError(
            ConflictErrorCodes.ALLOWED_FILE_TYPES, "Invalid file type."
        );
    } else {
        return false;
    }
};

export const validateFileMagicBytes = (fields: string | { name: string, maxCount?: number }[] | { name: string, maxCount?: number }, throwError = true) => {
    return async (req: Request, _: Response, next: NextFunction) => {
        // If it is array that means .fields is used in multer. Means, files in multiple fields will be added with any count
        if (Array.isArray(fields)) {
            // If there are no files then return
            if (!req.files) {
                return next();
            }

            const files = (req.files as {
                [fieldname: string]: Express.Multer.File[];
            });

            // Loop through fields
            for (const field of fields) {
                const fieldFiles = files[String(field.name)] ?? [];

                if (!(fieldFiles instanceof Array)) { // Prevents DoS.
                    return next();
                }

                // Loop through count of that field
                for (let i = 0; i < fieldFiles.length; ++i) {
                    // Verify magic bytes of that file
                    await verifyMagicBytes(files[String(field.name)][+i], throwError);
                }
            }
        } else if (typeof fields === "string") {
            // If it is string means only single file will be uploaded

            // If req.file not found then return
            if (!req.file) {
                return next();
            }
            if (typeof req.files !== 'object' || !Array.isArray(req.files)) {
                return next();
            }
            // Verify magic bytes of that file
            await verifyMagicBytes(req.file, throwError);
        } else if (typeof fields === "object") {
            // If it is object means in the single field files will be uploaded with any count

            // If there are no files then return
            if (!req.files) {
                return next();
            }

            if (typeof req.files !== 'object' || !Array.isArray(req.files)) {
                return next();
            }

            // Loop through count of that field
            for (let i = 0; i < (req.files as Express.Multer.File[]).length; ++i) {
                // Verify magic bytes of that file
                await verifyMagicBytes((req.files as Express.Multer.File[])[+i], throwError);
            }
        }

        next();
    };
};
