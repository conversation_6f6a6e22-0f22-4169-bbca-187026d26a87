import { Request } from "express";
import { Page } from "../models/page.model";
import { Pagination } from "../models/pagination.model";
import { PaginationEntity } from "../enums/pagination-entity";
import { PageResponseObj } from "../interfaces/page-response-obj";

export const getPage = async (req: Request, entity: PaginationEntity): Promise<PageResponseObj | null> => {
    const userId = req.currentUser?.id;
    const skipToken = req.query.skipToken;
    if(!skipToken || !userId) {
        return null;
    }
    const pageLink = `${req.protocol}://${req.get("host")}${req.path}?skipToken=${skipToken}`;
    const pagination = await Pagination.findOne({ entity, pageLinks: pageLink, createdBy: userId }).lean().exec();
    if(!pagination) {
        return null;
    }
    const page = await Page.findById(String(skipToken));
    if(!page) {
        return null;
    }
    return {
        data: JSON.parse(page.rows),
        meta: {
            totalCount: pagination.totalCount,
            rowsPerPage: pagination.rowsPerPage,
            totalPages: pagination.totalPages,
            currentPage: pagination.pageLinks.indexOf(pageLink) + 1,
            firstPageLink: pagination.pageLinks[0],
            lastPageLink: pagination.pageLinks[pagination.pageLinks.length - 1],
            previousPageLink: pagination.pageLinks.indexOf(pageLink) + 1 === 1 ? "" : pagination.pageLinks[pagination.pageLinks.indexOf(pageLink) - 1],
            nextPageLink: pagination.pageLinks.indexOf(pageLink) + 1 === pagination.totalPages ? "" : pagination.pageLinks[pagination.pageLinks.indexOf(pageLink) + 1],
            facets: JSON.parse(pagination.facets),
            appliedFilters: JSON.parse(pagination.appliedFilters),
            additionalAttributes: JSON.parse(pagination?.additionalAttributes ?? {})
        }
    };
};
