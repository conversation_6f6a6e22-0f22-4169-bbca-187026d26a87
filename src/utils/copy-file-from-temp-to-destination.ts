import { TemporaryFile } from "../models/temporary-files.model";
import { BlobServiceClient, ContainerSASPermissions, generateBlobSASQueryParameters, StorageSharedKeyCredential } from "@azure/storage-blob";
import { File } from "../models/files.model";
import { ExternalServerErrorWithMessage } from "../errors/external-server-error-with-message";

export const copyFileFromTempToDestination = async ({
    temporaryContainerName,
    destinationContainerName,
    tempFileIds,
    AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING,
    parentId,
    entity,
    entityId,
}: {
    temporaryContainerName: string,
    destinationContainerName: string,
    tempFileIds: string[],
    AZURE_STORAGE_CONNECTION_STRING: string | undefined,
    parentId: string,
    entity: string,
    entityId: string,
}): Promise<string[]> => {
    try {
        if (!AZURE_STORAGE_CONNECTION_STRING) {
            throw Error("Cannot upload.");
        }

        // Check for temporary fileIds, whether the files exist or not
        const files = await TemporaryFile.find({ _id: { $in: tempFileIds } }).lean().exec();
        if (!files.length) {
            throw Error("There is no file present in temporary storage to copy.");
        }

        const blobServiceClient = BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);

        const sourceContainerClient = blobServiceClient.getContainerClient(temporaryContainerName);
        const destinationContainerClient = blobServiceClient.getContainerClient(destinationContainerName);

        // Generate a container-level SAS token
        const sasToken = generateBlobSASQueryParameters(
            {
                containerName: temporaryContainerName,
                permissions: ContainerSASPermissions.parse("rl"), // Read and list permissions
                startsOn: new Date(),
                expiresOn: new Date(new Date().valueOf() + 600 * 1000), // Valid for 5 mins hour
            },
            blobServiceClient.credential as StorageSharedKeyCredential
        ).toString();

        // Copy all the files from temporary container to its actual container
        const fileCopyStatus = await Promise.allSettled(files.map(async file => {
            const sourceBlobUrlWithSAS = `${sourceContainerClient.url}/${file.fileName}?${sasToken}`;

            const destinationBlockBlobClient = destinationContainerClient.getBlockBlobClient(file.fileName);
            await destinationBlockBlobClient.syncUploadFromURL(sourceBlobUrlWithSAS);
        }));

        console.info("copyFileFromTempToDestination: File Copy Status");
        console.info(JSON.stringify(fileCopyStatus));
        console.info("-----------------------------------------------");

        // Check any file got failed while copying, if yes then revert the copy of all the requested files
        const anyFailed = fileCopyStatus.find(file => file.status === "rejected");
        if (anyFailed) {
            await Promise.all(fileCopyStatus.map(async (fileStatus, index) => {
                if (fileStatus.status === "fulfilled") {
                    // eslint-disable-next-line security/detect-object-injection
                    const blockBlobClient = destinationContainerClient.getBlockBlobClient(files[index].fileName);
                    await blockBlobClient.deleteIfExists();
                }
            }));

            throw new ExternalServerErrorWithMessage("File transfer failed in azure.");
        }

        // Insert the files, to files collection as files are now copied to its actual container
        const newFileIds: string[] = [];
        await Promise.all(files.map(async file => {
            const newFile = await File.build({
                fileName: file.fileName,
                originalFileName: file.originalFileName,
                uploadedBy: file.uploadedBy,
                uploadedOn: file.uploadedOn,
                parent: destinationContainerName,
                parentId,
                entity,
                entityId,
            }).save();

            newFileIds.push(newFile.id);
        }));

        return newFileIds;
    } catch (error) {
        console.error("Common.Util.CopyFileFromTempToDestination");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
