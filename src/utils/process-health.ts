
class ProcessHealth {
    // eslint-disable-next-line no-undef
    private _timeoutTimer: NodeJS.Timeout | undefined;

    heartBeat(): void {
        if(!this._timeoutTimer) {
            throw "Timer not initiated";
        }
        this._timeoutTimer.refresh();
    }

    killAfterMinutes(minutes: number): void {
        if(this._timeoutTimer) {
            throw "Timer already initiated";
        }
        this._timeoutTimer = setTimeout(() => {
            process.exit(0);
        }, minutes * 60 * 1000);
    }
}

export const processHealth = new ProcessHealth();
