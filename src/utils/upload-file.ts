import { BlobServiceClient, ContainerCreateOptions, BlockBlobParallelUploadOptions } from "@azure/storage-blob";
import { generateUUID } from "./generate-uuid";
import { File } from "../models/files.model";
import { unlink } from "fs/promises";
import { UploadFileParams } from "../interfaces/upload-files-params";
import { BasicResourceValueUnacceptableConflictError } from "../errors/basic-resource-value-unacceptable-conflict-error";
import { ConflictErrorCodes } from "./conflict-error-codes";

export const uploadFile = async ({ parent, parentId, entity, entityId, filePath, fileName, userId, publicContainer = false, isCDNEndpointRequired = false, mimeType,
    AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING, byPassSameFileCheck = false }: UploadFileParams): Promise<{ id: string, url?: string }> => {
    try {
        // CDN is not always required, ex. Incident attachments modules don't need any CDN URLs as they are data files.
        if (!AZURE_STORAGE_CONNECTION_STRING || (isCDNEndpointRequired && !process.env.CDN_ENDPOINT)) {
            throw Error("Cannot upload.");
        }

        if (!byPassSameFileCheck) {
            const fileAlreadyExist = await File.findOne({ parent, parentId, entity, entityId, originalFileName: fileName }).lean().exec();
            if (fileAlreadyExist) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.FILE_ALREADY_EXIST,
                    "File with same names already exist."
                );
            }
        }

        const blobServiceClient = BlobServiceClient.fromConnectionString(
            AZURE_STORAGE_CONNECTION_STRING
        );

        const containerClient = blobServiceClient.getContainerClient(parent);
        const options: ContainerCreateOptions = {};
        // All the CDN storage containers should be public.
        if (publicContainer) {
            options.access = "container";
        }

        await containerClient.createIfNotExists(options);
        const blobName = `${generateUUID()}-${fileName}`;
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);

        // To access files through CDN file type must be defined otherwise it will consider application octet stream.
        const httpHeaders: BlockBlobParallelUploadOptions = {};
        if (mimeType) {
            httpHeaders.blobHTTPHeaders = {
                "blobContentType": mimeType
            };
        }

        await blockBlobClient.uploadFile(filePath, httpHeaders);
        const file = File.build({
            fileName: blobName,
            originalFileName: fileName,
            uploadedBy: userId,
            parent,
            parentId,
            entity,
            entityId,
        });
        await file.save();

        return {
            id: file.id,
            url: isCDNEndpointRequired ? `${process.env.CDN_ENDPOINT}/${parent}/${blobName}` : ""
        };
    } catch (error) {
        console.error("Common.Util.UploadFile");
        console.error(error);
        throw Error("Something went wrong.");
    } finally {
        // eslint-disable-next-line security/detect-non-literal-fs-filename
        await unlink(filePath);
    }
};
