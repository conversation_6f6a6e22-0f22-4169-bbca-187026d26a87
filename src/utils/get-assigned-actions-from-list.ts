import { Request } from "express";
import { User } from "../models/user.model";
import { Organization } from "../models/organization.model";
import { Policy } from "../models/policy.model";
import { Action } from "../models/action.model";
import { Incident } from "../models/incident.model";
import { intersectArrays } from "./intersect-arrays";
import { Tracker } from "../models/tracker.model";
import { ApplicationPolicyIdObj } from "../interfaces";
import { IncidentV2 } from "../models/incidentv2.model";

export const getAssignedActionsFromList = async (req: Request, actions: string[]): Promise<string[]> => {
    if(req.currentUser?.isSystemUser) {
        // if(req.ip === process.env.SYSTEM_IP_ADDRESS && req.currentUser?.isSystemUser) {
        req.authorizedAccess = true;
        return actions;
    }

    const incidentId = req.params.incidentId || req.params.projectId || "";
    // Step1 - Check user login status.
    if (!req.currentUser) {
        return [];
    }

    // Step2 - Check user organization status.
    const organization = await Organization.findById(req.currentUser.organizationId).lean().exec();
    if (!organization) {
        return [];
    }

    // Step3 - Check user status.
    const user = await User.findOne({ _id: req.currentUser.id, isEnabled: true }, { policyIds: 1, applicationPolicyIds: 1, _id: 0 }).lean().exec();
    if (!user) {
        return [];
    }

    // Step 4: If incident Id is there in url then check if any application policy assigned
    if (incidentId) {
        // Find incident and if it is not present then return false
        let incident = await Incident.findById(incidentId);
        if (!incident) {
            incident = await IncidentV2.findById(incidentId);
            if (!incident) {
                return [];
            }
        }

        // If any application policy assigned then fetch that policy details and actions of them
        const applicationPolicies = ((user.applicationPolicyIds as ApplicationPolicyIdObj[]) || []).reduce((policyIds: string[], policy: ApplicationPolicyIdObj) => {
            if(policy.applicationType === "Incident" && String(policy.applicationId) === String(incidentId)) {
                policyIds.push(String(policy.policyId));
            }
            return policyIds;
        }, []);

        // If policy assigned then fetch that policy details and actions of it
        if (applicationPolicies.length) {
            const policies = await Policy.find({ _id: applicationPolicies, isEnabled: true, type: "Application" }, { actionIds: 1 }).lean().exec();

            // If no policies found then return blank array
            if (!policies.length) {
                return [];
            }

            // Make actionsIds array
            let actionsIds: string[] = [];
            policies.forEach(policy => {
                actionsIds = actionsIds.concat(policy.actionIds);
            });

            // Check user has that action assigned
            const actionResp = await Action.find({ _id: { $in: Array.from(new Set(actionsIds)) }, name: { $in: actions } }, { name: 1 }).lean().exec();
            if (actionResp && !actionResp.length) {
                return [];
            }

            const actionNames = actionResp.map(actionItem => actionItem.name);
            return intersectArrays(actionNames, actions);
        }
    }

    // Step 4: If tracker Id is there in url then check if any application policy assigned
    if (req.params.trackerId) {
        // Find incident and if it is not present then return false
        const tracker = await Tracker.findById(req.params.trackerId);
        if (!tracker) {
            return [];
        }

        // If any application policy assigned then fetch that policy details and actions of them
        const applicationPolicies = ((user.applicationPolicyIds as ApplicationPolicyIdObj[]) || []).reduce((policyIds: string[], policy: ApplicationPolicyIdObj) => {
            if(policy.applicationType === "Resilience" && String(policy.applicationId) === String(req.params.trackerId)) {
                policyIds.push(String(policy.policyId));
            }
            return policyIds;
        }, []);

        // If policy assigned then fetch that policy details and actions of it
        if (applicationPolicies.length) {
            const policies = await Policy.find({ _id: applicationPolicies, isEnabled: true, type: "Application" }, { actionIds: 1 }).lean().exec();

            // If no policies found then return blank array
            if (!policies.length) {
                return [];
            }

            // Make actionsIds array
            let actionsIds: string[] = [];
            policies.forEach(policy => {
                actionsIds = actionsIds.concat(policy.actionIds);
            });

            // Check user has that action assigned
            const actionResp = await Action.find({ _id: { $in: Array.from(new Set(actionsIds)) }, name: { $in: actions } }, { name: 1 }).lean().exec();
            if (actionResp && !actionResp.length) {
                return [];
            }

            const actionNames = actionResp.map(actionItem => actionItem.name);
            return intersectArrays(actionNames, actions);
        }
    }

    // Step 5 - Check user sending request to right organization if organizationId exists in url parameters.
    if (process.env.MOXFIVE_ID
        && req.params.organizationId
        && process.env.MOXFIVE_ID !== req.currentUser.organizationId
        && req.params.organizationId !== req.currentUser.organizationId) {
        return [];
    }

    // Step 6: If no global policies assigned then return false
    if (!user.policyIds || !user.policyIds.length) {
        return [];
    }

    // Step 7 - Check users global policies' status.
    const policies = await Policy.find({ _id: { $in: user.policyIds }, isEnabled: true, type: "Global" }, { actionIds: 1 }).lean().exec();
    if (!policies || !policies.length) {
        return [];
    }

    // Step 8 - Check action exist with user global policies and return boolean.
    let actionIds: string[] = [];
    policies.forEach(policy => actionIds = [...actionIds, ...policy.actionIds]);
    const actionResp = await Action.find({ _id: { $in: actionIds }, name: { $in: actions } }, { name: 1 }).lean().exec();
    if (actionResp && !actionResp.length) {
        return [];
    }

    const actionNames = actionResp.map(actionItem => actionItem.name);
    return intersectArrays(actionNames, actions);
};
