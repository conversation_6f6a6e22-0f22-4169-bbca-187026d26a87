export const generateSearchKeys = (strArray: string[]) => {
    const keys = new Set();
    const finalStrs = new Set([...strArray]);

    strArray.forEach(str => {
        const splitArray = str.split(" ");
        if (splitArray.length > 1) {
            splitArray.forEach(finalStrs.add, finalStrs);
        }
    });

    [...finalStrs].forEach(str => {
        for (let i = 1; i <= str.length; i++) {
            keys.add(str.substring(0, i));
        }
    });

    return [...keys];
};
