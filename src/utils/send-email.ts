import { natsConnection } from "../libs/nats-connection";
import { SendEmailPublisher } from "../index";

export const sendEmail = async ({ recipients, subject, message, combined, recipientType, serviceName, isHTML = false }: {
    recipients: string[], subject: string, message: string, combined: boolean, recipientType: "email" | "userId",  serviceName: string, isHTML?: boolean
}) => {
    try {
        await new SendEmailPublisher(natsConnection.client, serviceName).publish({
            recipients,
            subject,
            message,
            combined,
            recipientType,
            serviceName,  // Microservice name from which we will call this function,
            isHTML
        });
    } catch (err) {
        console.info("Something went wrong while sending an email", err);
    }
};
