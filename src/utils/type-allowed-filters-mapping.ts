import { ValueTypesEnum } from "../enums/value-types.enum";
import { FilterOperators } from "../enums/filter-operators";
import { filterOperatorsLabelValue } from "./filter-operators-label-value";

export const typeAllowedFiltersMapping = {
    [ValueTypesEnum.STRING]: [filterOperatorsLabelValue[FilterOperators.EQ], filterOperatorsLabelValue[FilterOperators.STARTS_WITH],
        filterOperatorsLabelValue[FilterOperators.CONTAINS], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.TEXTAREA]: [filterOperatorsLabelValue[FilterOperators.STARTS_WITH], filterOperatorsLabelValue[FilterOperators.CONTAINS],
        filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.NUMBER]: [filterOperatorsLabelValue[FilterOperators.GE], filterOperatorsLabelValue[FilterOperators.LE], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.DROPDOWN]: [filterOperatorsLabelValue[FilterOperators.IN], filterOperatorsLabelValue[FilterOperators.NIN], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.DATE]: [filterOperatorsLabelValue[FilterOperators.GE], filterOperatorsLabelValue[FilterOperators.LE], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.BOOLEAN]: [filterOperatorsLabelValue[FilterOperators.EQ], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.IP_ADDRESS]: [filterOperatorsLabelValue[FilterOperators.EQ], filterOperatorsLabelValue[FilterOperators.STARTS_WITH],
        filterOperatorsLabelValue[FilterOperators.ENDS_WITH], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.URL]: [filterOperatorsLabelValue[FilterOperators.EQ], filterOperatorsLabelValue[FilterOperators.STARTS_WITH],
        filterOperatorsLabelValue[FilterOperators.ENDS_WITH], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.EMAIL]: [filterOperatorsLabelValue[FilterOperators.EQ], filterOperatorsLabelValue[FilterOperators.STARTS_WITH],
        filterOperatorsLabelValue[FilterOperators.ENDS_WITH], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.PERCENTAGE]: [filterOperatorsLabelValue[FilterOperators.GE], filterOperatorsLabelValue[FilterOperators.LE],
        filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.CURRENCY]: [filterOperatorsLabelValue[FilterOperators.GE], filterOperatorsLabelValue[FilterOperators.LE], filterOperatorsLabelValue[FilterOperators.IS_BLANK]],
    [ValueTypesEnum.STRING_WITH_EQ]: [filterOperatorsLabelValue[FilterOperators.EQ]]
};
