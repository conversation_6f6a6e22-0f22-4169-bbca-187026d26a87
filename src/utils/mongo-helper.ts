import { unSanitizeURL } from "./";

export class MongoHelperService {
    // For single select flexible field, wrap the object in an array
    static getDefaultWithCond(field: string) {
        return {
            $cond: {
                if: `${field}.id`,
                then: [field],
                else: []
            }
        };
    }

    // It will unsanitize the sanitized field
    static getUnSanitizedFunctionOperator(field: string) {
        return {
            $cond: {
                if: `${field}`,
                then: {
                    $function: {
                        body: unSanitizeURL.toString(),
                        args: [field],
                        lang: "js"
                    }
                },
                else: null
            }
        };
    }

    static getUnSanitizedFunctionOperatorForArray(field: string) {
        const newField = field.split(".");
        const newFieldName = `${newField[newField.length - 1]}tempValue`;

        return {
            $map: {
                input: `${field}`,
                as: `${newFieldName}`,
                in: {
                    "$function": {
                        "body": "function unSanitizeURL(sanitizedString) {\r\n    const hasProtocol = new RegExp(\"^([a-z]+://|//)\", \"i\");\r\n    let actualString = sanitizedString.replace(/\\[:]/, \":\");\r\n    if (hasProtocol.test(actualString)) {\r\n        actualString = actualString.replace(/xx/, \"tt\");\r\n    }\r\n    actualString = actualString.replace(/\\[.]/g, \".\");\r\n    return actualString;\r\n}",
                        "args": [
                            `$$${newFieldName}`
                        ],
                        "lang": "js"
                    }
                }
            }
        };
    }
}
