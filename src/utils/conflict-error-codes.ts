export enum ConflictErrorCodes {
    CAN_NOT_UPDATE_COMPANY_BACKGROUND = 4097,
    APPLICATION_TYPE_CONFLICT = 40911,
    GLOBAL_TYPE_CONFLICT = 40912,
    GL<PERSON>BAL_TYPE_DETACH_CONFLICT = 40913,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_TYPE_ATTACH_CONFLICT = 40914,
    POLICY_NOT_ATTACHED_TO_INCIDENT = 40915,
    ONE_POLICY_PER_INCIDENT_FOR_USER = 40917,
    STATUS_DELETE_IMPACT_EXISTING_ASSETS = 40919,
    ASSET_STATUS_MUST_BE_UNIQUE = 40921,
    INVALID_DATE_RANGE = 40922,
    ONLY_ONE_ATTRIBUTE_CAN_BE_UPDATED = 40923,
    START_END_DATE_INVALID = 40924,
    TRACKER_OBJECTIVE_CONFLICT = 40925,
    POLICY_NOT_ATTACHED_TO_RESILIENCE = 40926,
    ONE_POLICY_PER_RESILIENCE_FOR_USER = 40927,
    ALLOWED_FILE_TYPES = 40928,
    ALLOWED_FILE_SIZE = 40929,
    FILE_ALREADY_EXIST = 40930,
    MAX_UPLOAD_FILES = 40931,
    MAX_DOWNLOAD_COUNT = 40932,
    FEEDBACK_CANNOT_ACCEPTED = 40933,
    FEEDBACK_TYPE_INVALID = 40934,
    FEEDBACK_ALREADY_SUBMITTED = 40935,
    NAME_UNIQUE_FOR_FOCUS_VIEW = 40936,
    FOCUS_VIEW_MAX_LIMIT = 40937,
    INCIDENT_LIMIT_FOR_FOCUS_VIEW = 40938,
    ACTIVE_FOCUS_VIEW_CANNOT_BE_DELETED = 40939,
    INCIDENT_EXIST_FOR_THAT_INCIDENT_SUPPORT = 40940,
    MOX_ID_CAN_NOT_BE_USED_FOR_SAME_FEE_TYPE_IN_OFFERING = 40941,
    PUBLISHED_UNPUBLISHED_DATE_MUST_BE_15_MINUTES_INTERVAL = 40942,
    SELECTED_DATE_CONFLICT_WITH_OTHER_MESSAGE = 40943,
    RESILIENCE_OFFERING_ALREADY_EXIST = 40944,
    RESILIENCE_OFFERING_REVIEW_ALREADY_EXIST = 40945,
    PARTNER_REQUEST_ALREADY_EXIST = 40946,
    OBJECTIVE_ALREADY_EXIST = 40947,
    ACTIVITY_FEED_LOG_ALREADY_EXIST = 40948,
    INVALID_COLOR_PALETTE_COMBINATION = 40949,
    UPDATE_NOT_PUBLISHED = 40950,
    SAME_LAYOUT = 40951,
    ROUTE_NOT_FOUND = 40952,
    USER_EMAIL_ALREADY_REGISTERED = 40953,
    CONNECTION_LIMIT_EXCEEDED = 40954
}
