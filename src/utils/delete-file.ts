import { BlobServiceClient } from "@azure/storage-blob";
import { ResourceNotFoundError } from "../errors/resource-not-found";
import { File } from "../models/files.model";
import { NotFoundCode } from "./not-found-codes";
import { DeleteFileParams } from "../interfaces/delete-file-params";

export const deleteFile = async ({ parent, parentId, entity, entityId, fileId,
    AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING }: DeleteFileParams): Promise<boolean> => {
    try {
        if (!AZURE_STORAGE_CONNECTION_STRING) {
            throw Error("Cannot delete.");
        }

        const file = await File.findOne({ _id: fileId, parent, parentId, entity, entityId }).lean().exec();
        if (!file) {
            throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "File not found.");
        }

        const blobServiceClient = BlobServiceClient.fromConnectionString(
            AZURE_STORAGE_CONNECTION_STRING
        );
        const containerClient = blobServiceClient.getContainerClient(file.parent);
        await containerClient.createIfNotExists();
        const blockBlobClient = containerClient.getBlockBlobClient(file.fileName);
        await blockBlobClient.deleteIfExists();
        await File.deleteOne({ _id: file._id });
        return true;
    } catch (error) {
        console.error("Common.Util.DeleteFile");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
