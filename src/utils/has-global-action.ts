/* eslint-disable max-statements */
import {  Request } from "express";
import { User } from "../models/user.model";
import { Policy } from "../models/policy.model";
import { Action } from "../models/action.model";
// import { Incident } from "../models/incident.model";
import { intersectArrays } from "./intersect-arrays";
import { Tracker } from "../models/tracker.model";
import { checkBypassRoute } from "./check-bypass-route";
import { BypassAPIObj } from "../interfaces/bypass-api-obj";
import { ApplicationPolicyIdObj } from "../interfaces";
import { Service } from "../models/service.model";
import { ServicesEnum } from "../enums/services";
import { IncidentV2 } from "../models/incidentv2.model";

declare global {
    namespace Express {
        interface Request {
            action: string;
            policies: string[];
        }
    }
}

export const hasGlobalAction = async (req: Request, action: string, includePoliciesInRequest = false): Promise<boolean> => {
    req.action = action;
    if(req.currentUser?.isSystemUser) {
    // if(req.ip === process.env.SYSTEM_IP_ADDRESS && req.currentUser?.isSystemUser) {
        req.authorizedAccess = true;
        return true;
    }
    const incidentId = req.params.incidentId || req.params.projectId || "";
    const trackerId = req.params.trackerId || "";
    const globalBypass : BypassAPIObj[] = [
        { method: "GET", url: `/v1/incidents/${incidentId}` },
        { method: "GET", url: `/v2/incidents/${incidentId}` }
    ];
    const byPassAPIs: BypassAPIObj[] = [
        { method: "GET", url: `/v1/incidents/${incidentId}/users` },
        { method: "GET", url: `/v1.1/incidents/${incidentId}/users` },
        { method: "GET", url: `/v1/incidents/${incidentId}/users/filters` },
        { method: "GET", url: `/v1/resiliences/tracker/${trackerId}/users` },
        { method: "GET", url: `/v1.1/resiliences/tracker/${trackerId}/users` },
        { method: "GET", url: `/v1/resiliences/tracker/${trackerId}/users/filters` },
    ];

    req.authorizedAccess = false;

    // Check action is valid or not & if the required authorization is false then directly bypass
    const actionObj = await Action.findOne({ name: action }).lean().exec();
    if(!actionObj) {
        return false;
    }
    if(!actionObj.requireAuthorization) {
        req.authorizedAccess = true;
        return true;
    }

    const service = await Service.findById(actionObj.serviceId).lean().exec();

    // Step1 - Check user login status.
    if (!req.currentUser) {
        return false;
    }

    // Step2 - Check user organization status.
    const organization = req.loggedInUserOrganization;
    if (!organization) {
        return false;
    }

    // Step3 - Check user status.
    const user = req.loggedInUser;
    if (!user) {
        return false;
    }

    const isSuperAdminAssigned = !!user.policyIds.map(String).includes(process.env.SUPER_ADMIN_POLICY_ID || "");
    const isGlobalBypass = checkBypassRoute({ method: req.method, url: req.path }, globalBypass);

    // Step 4: If incident Id is there in url then check if any application policy assigned
    if (incidentId && service?.name === ServicesEnum.INCIDENT) {
        // Find incident and if it is not present then return false
        const incident = await IncidentV2.findById(incidentId);
        if(!incident) {
            return false;
        }

        if (!isSuperAdminAssigned && !isGlobalBypass && incident.published) {
            if (!incident.members.includes(req.currentUser.id)) {
                const users = await User.find({ organizationId: req.currentUser.organizationId }, { _id: 1 }).lean().exec();
                const existSameOrgUser = intersectArrays(incident.members.map(String), users.map(user => String(user._id)));
                if (!existSameOrgUser.length) {
                    return false;
                }

                // check for byPassAPIs
                const isBypassAPI = checkBypassRoute({ method: req.method, url: req.path }, byPassAPIs);
                if (!isBypassAPI) {
                    return false;
                }
            }
        }

        // Fetch application policy assigned
        const applicationPolicies = ((user.applicationPolicyIds as ApplicationPolicyIdObj[]) || []).reduce((policyIds: string[], policy: ApplicationPolicyIdObj) => {
            if(policy.applicationType === "Incident" && String(policy.applicationId) === String(incidentId)) {
                policyIds.push(String(policy.policyId));
            }
            return policyIds;
        }, []);

        // If any application policy assigned then fetch that policy details and actions of them
        if(applicationPolicies.length) {
            const policies = await Policy.find({ _id: applicationPolicies, isEnabled: true, type: "Application" }, { actionIds: 1 }).lean().exec();

            // If no policies found then return false
            if(!policies.length) {
                return false;
            }

            // Make actionsIds array
            let actionsIds: string[] = [];
            policies.forEach(policy => {
                actionsIds = actionsIds.concat(policy.actionIds);
            });

            // Check user has that action assigned
            const hasAction = await Action.findOne({ _id: { $in: Array.from(new Set(actionsIds)) }, name: action }, { _id: 1 }).lean().exec();

            // If includePoliciesInRequest is true then add policy id in req.policies
            if(includePoliciesInRequest && !!hasAction) {
                policies.forEach(policy => {
                    if (policy.actionIds.find(action => action.toString() === hasAction._id.toString())) {
                        req.policies.push(policy._id);
                    }
                });
            }

            req.authorizedAccess = !!hasAction;
            return !!hasAction;

        }
    }

    // Step 5: If tracker Id is there in url then check if any application policy assigned
    if (trackerId && service?.name === ServicesEnum.RESILIENCE) {
        // Find incident and if it is not present then return false
        const tracker = await Tracker.findById(trackerId);
        if(!tracker) {
            return false;
        }

        if (!isSuperAdminAssigned && !isGlobalBypass && !tracker.members.includes(req.currentUser.id)) {
            const users = await User.find({ organizationId: req.currentUser.organizationId }, { _id: 1 }).lean().exec();
            const existSameOrgUser = intersectArrays(tracker.members.map(String), users.map(user => String(user._id)));
            if (!existSameOrgUser.length) {
                return false;
            }

            // check for byPassAPIs
            const isBypassAPI = checkBypassRoute({ method: req.method, url: req.path }, byPassAPIs);
            if (!isBypassAPI) {
                return false;
            }
        }

        // Fetch application policy assigned
        const applicationPolicies = ((user.applicationPolicyIds as ApplicationPolicyIdObj[]) || []).reduce((policyIds: string[], policy: ApplicationPolicyIdObj) => {
            if(policy.applicationType === "Resilience" && String(policy.applicationId) === String(trackerId)) {
                policyIds.push(String(policy.policyId));
            }
            return policyIds;
        }, []);

        // If any application policy assigned then fetch that policy details and actions of them
        if(applicationPolicies.length) {
            const policies = await Policy.find({ _id: applicationPolicies, isEnabled: true, type: "Application" }, { actionIds: 1 }).lean().exec();

            // If no policies found then return false
            if(!policies.length) {
                return false;
            }

            // Make actionsIds array
            let actionsIds: string[] = [];
            policies.forEach(policy => {
                actionsIds = actionsIds.concat(policy.actionIds);
            });

            // Check user has that action assigned
            const hasAction = await Action.findOne({ _id: { $in: Array.from(new Set(actionsIds)) }, name: action }, { _id: 1 }).lean().exec();

            // If includePoliciesInRequest is true then add policy id in req.policies
            if(includePoliciesInRequest && !!hasAction) {
                policies.forEach(policy => {
                    if (policy.actionIds.find(action => action.toString() === hasAction._id.toString())) {
                        req.policies.push(policy._id);
                    }
                });
            }

            req.authorizedAccess = !!hasAction;
            return !!hasAction;
        }
    }

    // Step 6 - Check user sending request to right organization if organizationId exists in url parameters.
    if (process.env.MOXFIVE_ID
        && req.params.organizationId
        && process.env.MOXFIVE_ID !== req.currentUser.organizationId
        && req.params.organizationId !== req.currentUser.organizationId) {
        req.authorizedAccess = false;
        return false;
    }

    // Step 7: If no global policies assigned then return false
    if(!user.policyIds || !user.policyIds.length) {
        req.authorizedAccess = false;
        return false;
    }

    // Step 8 - Check users global policies' status.
    const policies = await Policy.find({ _id: { $in: user.policyIds }, isEnabled: true, type: "Global" }, { actionIds: 1 }).lean().exec();
    if (!policies || !policies.length) {
        req.authorizedAccess = false;
        return false;
    }

    // Step 9 - Check action exist with user global policies and return boolean.
    let actionIds: string[] = [];
    policies.forEach(policy => actionIds = [...actionIds, ...policy.actionIds]);
    const hasAction = await Action.findOne({ _id: { $in: actionIds }, name: action }, { _id: 1 }).lean().exec();
    if(includePoliciesInRequest && !!hasAction) {
        req.policies = [];
        policies.forEach(policy => {
            if (policy.actionIds.find(action => action.toString() === hasAction._id.toString())) {
                req.policies.push(policy._id);
            }
        });
    }
    req.authorizedAccess = !!hasAction;
    return !!hasAction;
};
