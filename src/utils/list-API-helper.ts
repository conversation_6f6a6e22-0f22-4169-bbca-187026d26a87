/* eslint-disable max-statements */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Model } from "mongoose";
import { ValueTypesEnum } from "../enums/value-types.enum";
import { MongoHelperService } from "./mongo-helper";
import { SanitizedTypesEnum } from "../enums/sanitized-types.enum";
import { convertToObjectId, escapeRegExp, isIPAddress, sanitizeEmail, sanitizeIP, sanitizeIPForFilters, sanitizeURI, isValidObjectId, isValidDate, isValidURL } from ".";

export class ListAPIHelper {
    static prepareColumnAndFilterFieldMappingsForModule(path: any) {
        const columnsMapping: { [key: string]: any } = {};
        const filterFieldMapping: { [key: string]: any } = {};

        // If the path is not object or it is an array or path value is not defined then return
        if (typeof path !== "object" || Array.isArray(path) || !path) {
            return { columnsMapping, filterFieldMapping };
        }

        // Loop through all sections of path
        for (const section in path) {
            // @ts-ignore
            if (path[String(section)]) {
                // Check whether section is general section or not means either modifiedAt, modifiedBy or commonBase
                const isGeneralSection = (section === "modifiedAt" || section === "modifiedBy" || section === "commonBase");

                // Generate column mappings for the section
                // If it is general section then if commonBase entry is not there then add it
                if (isGeneralSection) {
                    if (!columnsMapping["commonBase"]) {
                        columnsMapping["commonBase"] = {};
                    }
                    if (!filterFieldMapping["commonBase"]) {
                        filterFieldMapping["commonBase"] = {};
                    }
                } else {
                    // Otherwise add blank entry of that section
                    columnsMapping[String(section)] = {};
                    filterFieldMapping[String(section)] = {};
                }

                // Loop through all fields
                // @ts-ignore
                path[String(section)].forEach(field => {
                    // Get the property path, where is it located in the collection
                    let pathToValue: any = "";
                    let filterFieldPath: any = "";
                    // If it is boolean field
                    if (field.isBooleanField) {
                        if (isGeneralSection) {
                            pathToValue = `$${field.name}`;
                            filterFieldPath = `${field.name}`;
                        } else {
                            pathToValue = `$${field.sectionName ?? section}.${field.name}`;
                            filterFieldPath = `${field.sectionName ?? section}.${field.name}`;
                        }
                    } else if (isGeneralSection && !field.flexibleField) {
                        if ((field.type === ValueTypesEnum.DROPDOWN)) {
                            // If we wanted to keep it same then save whole field as pathToValue
                            if(field.keepSame) {
                                pathToValue = `$${field.name}`;
                            } else if(((!field.required) && (!field.multiple)) || (section === "modifiedBy")) {
                                // If field is not required and it is not multiple OR section is modifiedBy then set it's value as pathToValue
                                pathToValue = { $ifNull: [`$${field.name}.value`, null] };
                            } else {
                                // Else save as it is
                                pathToValue = `$${field.name}`;
                            }
                        } else if (field?.sanitized) {
                            // If the field is sanitized then unsanitize it in the pathToValue
                            pathToValue = field?.multiple
                                ? MongoHelperService.getUnSanitizedFunctionOperatorForArray(`$${field.name}`)
                                : MongoHelperService.getUnSanitizedFunctionOperator(`$${field.name}`);
                        } else {
                            // Else save as it is
                            pathToValue = `$${field.name}`;
                        }

                        // Save filterFieldPath
                        // If type is dropdown then save .id in the filterFieldPath otherwise field as it is
                        if (field.type === ValueTypesEnum.DROPDOWN) {
                            filterFieldPath = `${field.name}.id`;
                        } else if (section !== "modifiedBy") {
                            filterFieldPath = `${field.name}`;
                        }
                    } else if (isGeneralSection && field.flexibleField) {
                        // If it is general section and flexible field
                        if (field.multiple) {
                            pathToValue = `$${field.name}`;
                        } else {
                            pathToValue = MongoHelperService.getDefaultWithCond(`$${field.name}`);
                        }
                        filterFieldPath = `${field.name}.id`;
                    } else if (!field.flexibleField) {
                    // If it is not flexible field and not form general section

                        // If field type is dropdown then
                        if (field.type === ValueTypesEnum.DROPDOWN) {
                            // pathToValue = field.required ? `$${section}.${field.name}` : { $ifNull: [`$${section}.${field.name}.value`, null] };
                            pathToValue = `$${section}.${field.name}`;
                            filterFieldPath = `${section}.${field.name}.id`;
                        } else if (field?.sanitized) {
                            // If the field is sanitized then in pathToValue unsanitize it
                            pathToValue = field?.multiple
                                ? MongoHelperService.getUnSanitizedFunctionOperatorForArray(`$${section}.${field.name}`)
                                : MongoHelperService.getUnSanitizedFunctionOperator(`$${section}.${field.name}`);
                            filterFieldPath = `${section}.${field.name}`;
                        } else {
                            pathToValue = `$${section}.${field.name}`;
                            filterFieldPath = `${section}.${field.name}`;
                        }
                    } else if (field.flexibleField) {
                        // If it is flexible field and not form general section

                        // If it is multiple
                        if (field.multiple) {
                            pathToValue = `$${section}.${field.name}`;
                        } else {
                            // If it is single then wrap the object in the array
                            pathToValue = MongoHelperService.getDefaultWithCond(`$${section}.${field.name}`);
                        }
                        // .id in the filterFieldPath
                        filterFieldPath = `${section}.${field.name}.id`;
                    } else if (field.type === ValueTypesEnum.DROPDOWN) {
                        // If the field type is dropdown

                        // defaultWithCond in pathToValue and .id in the filterFieldPath
                        pathToValue = MongoHelperService.getDefaultWithCond(`$${section}.${field.name}`);
                        filterFieldPath = `${section}.${field.name}.id`;
                    }

                    // Add entry in the columnsMapping and filterFieldMapping
                    columnsMapping[isGeneralSection ? "commonBase" : section][field.name] = pathToValue;
                    filterFieldMapping[isGeneralSection ? "commonBase" : section][field.name] = filterFieldPath;
                });
            }
        }

        // If common base is there then set _id to 0 and id to _id
        if (columnsMapping["commonBase"]) {
            columnsMapping["commonBase"]["_id"] = 0;
            columnsMapping["commonBase"]["id"] = "$_id";
        }

        return { columnsMapping, filterFieldMapping };
    }

    static prepareFacetQueryMappings(path: any) {
        const facetQueryMapping: {
            [key: string]:
            {
                id: string | { [key: string]: string },
                value: string | { [key: string]: string },
                isArray?: boolean,
                field?: string
            }
        } = {};

        // If the path is not object or it is an array or path value is not defined then return
        if (typeof path !== "object" || Array.isArray(path) || !path) {
            return facetQueryMapping;
        }

        // Loop through all sections of path
        for (const section in path) {
            // @ts-ignore
            if (path[String(section)]) {
                // Check whether section is general section or not means either modifiedAt, modifiedBy or commonBase
                const isGeneralSection = (section === "modifiedAt" || section === "modifiedBy" || section === "commonBase");

                // Loop through all fields of the section
                // @ts-ignore
                path[String(section)].forEach(field => {
                    // If the field if filterable
                    if(field.filterable) {
                        // If the field type is boolean
                        if(field.type === ValueTypesEnum.BOOLEAN) {
                            //  save id and value
                            facetQueryMapping[field.name] = isGeneralSection
                                ? {
                                    id: { $toString: `$${field.name}` },
                                    value: { $toString: `$${field.name}` },
                                }
                                : {
                                    id: { $toString: `$${field.sectionName ?? section}.${field.name}` },
                                    value: { $toString: `$${field.sectionName ?? section}.${field.name}` },
                                };
                        } else if(field.type === ValueTypesEnum.DROPDOWN) {
                            // If it is boolean field
                            if(field.isBooleanField) {
                                //  save id and value
                                facetQueryMapping[field.name] = isGeneralSection
                                    ? {
                                        id: { $toString: `$${field.name}` },
                                        value: { $toString: `$${field.name}` },
                                    }
                                    : {
                                        id: { $toString: `$${field.sectionName ?? section}.${field.name}` },
                                        value: { $toString: `$${field.sectionName ?? section}.${field.name}` },
                                    };
                            } else if(field.multiple) {
                                // Set id and value of field's id & value, also set field name and isArray to true
                                facetQueryMapping[field.name] = isGeneralSection
                                    ? {
                                        id: `$${field.name}.id`,
                                        value: `$${field.name}.value`,
                                        field: `$${field.name}`,
                                        isArray: true
                                    }
                                    : {
                                        id: `$${field.sectionName ?? section}.${field.name}.id`,
                                        value: `$${field.sectionName ?? section}.${field.name}.value`,
                                        field: `$${field.sectionName ?? section}.${field.name}`,
                                        isArray: true
                                    };
                            } else {
                                // Save id and value
                                facetQueryMapping[field.name] = isGeneralSection
                                    ? {
                                        id: `$${field.name}.id`,
                                        value: `$${field.name}.value`,
                                    }
                                    : {
                                        id: `$${field.sectionName ?? section}.${field.name}.id`,
                                        value: `$${field.sectionName ?? section}.${field.name}.value`,
                                    };
                            }
                        }
                    }
                });
            }
        }

        return facetQueryMapping;
    }

    static prepareUserColumnsFiltersAndFacetsBasedOnPermissions({ assignedActions, sectionWiseActions, columnsMapping, filterFieldMapping, facets }:
        {
            assignedActions: Set<string>,
            sectionWiseActions: { name: string, action?: string, path: any, actionForFields?: string[] }[],
            columnsMapping: { [key: string]: any },
            filterFieldMapping: { [key: string]: any },
            facets: { [key: string]: string[] },
        }) {
        // Define userColumns, userFilters & userFacets as entry of commonBase only
        let userColumns = { ...columnsMapping["commonBase"] };
        let userFilters = { ...filterFieldMapping["commonBase"] };
        let userFacets: string[] = [...(facets["commonBase"] ?? [])];

        // Loop through all section wise actions
        sectionWiseActions.forEach(item => {
            // For the case where we have permission for certain field in commanbase
            if (item.name === "commonBase" && !assignedActions.has(item?.action ?? "")) {
                item.path.forEach((field: any) => {
                    if (field.checkPermission) {
                        delete userColumns[field.name];
                        delete userFilters[item.name];
                    }
                });
            }

            // If assignedActions has that action then add that section in columns, filters and facets
            if (assignedActions.has(item?.action ?? "")) {
                userColumns = { ...userColumns, ...columnsMapping[item.name] };
                userFilters = { ...userFilters, ...filterFieldMapping[item.name] };
                userFacets = [...userFacets, ...(facets[item.name] ?? [])];
            }
        });
        return { userColumns, userFilters, userFacets };
    }

    static prepareUserSortQuery({ path, sort, userColumns }: { path: any, sort: string, userColumns: any }) {
        // Define atlasSort, sortProcessingStages and regularSort
        let atlasSort: { [key: string]: 1 | -1 } = {};
        const sortProcessingStages: { [key: string]: any } = {};
        let regularSort: { [key: string]: 1 | -1 } = {};

        // Split sort string
        const sortArray = (sort ?? "").split(/\sand\s/).map((str) => str.trim());
        const sortfieldPathMap = new Map<string, string>();
        const multiSelectFields: string[] = [];
        const specialArrayFields = new Set();
        const sortSpecialFieldPathMap = new Map<string, string>();
        let isRegularSortApplied = false;

        // Loop through all sections of path
        // Get the sort field path mapping
        Object.keys(path).forEach(section => {
            // Check whether the section is general section or not
            const isGeneralSection = ["modifiedAt", "modifiedBy", "commonBase"].includes(section);

            // Loop through al fields of that section
            (path[String(section)] ?? "").forEach((field: any) => {
                // If it is specialArray then add it in the specialArrayFields, specially used for moxIds sorting
                if (field.specialArray) specialArrayFields.add(field.name);

                // If the field (is generated after some operatrions / not present in db / isspecial field) so we have to use regular sort.
                // isLookupField like budget and budgetSpent in the offerings, isSpecialFiled like indicator in IOC
                if (field.isLookupField || field.isSpecialFiled) {
                    sortSpecialFieldPathMap.set(field.name, isGeneralSection ? `${field.name}` : `${section}.${field.name}`);
                }

                // If it is multiple then add entry in the multiSelectFields and sortfieldPathMap
                if (field.multiple) {
                    multiSelectFields.push(field.name);
                    sortfieldPathMap.set(field.name, (isGeneralSection || field.ignoreSection) ? `${field.name}` : `${section}.${field.name}`);
                }

                // If the field type is dropdown
                if (field.type === ValueTypesEnum.DROPDOWN && !field.isBooleanField) {
                    // If it is not multiple then add entry in the sortfieldPathMap with .value
                    if (!field.multiple) {
                        sortfieldPathMap.set(field.name, (isGeneralSection || field.ignoreSection) ? `${field.name}.value` : `${section}.${field.name}.value`);
                    }
                    // For category and type fields, which are stored in categories object
                    if (!field.multiple && field.isOfCategories) {
                        sortfieldPathMap.set(field.name, field.name === "category" ? `${section}.categories.value` : `${section}.categories.type.value.value`);
                    }
                    // For special fields,
                    if (!field.multiple && field.isSpecialFiled) {
                        sortfieldPathMap.set(field.name, isGeneralSection ? `${field.name}` : `${section}.${field.name}`);
                    }
                } else {
                    // Otherwise add entry in the sortfieldPathMap

                    sortfieldPathMap.set(field.name, (isGeneralSection || field.ignoreSection) ? `${field.name}` : `${section}.${field.name}`);
                }
            });
        });

        // Loop through all sort
        for (const sort of sortArray) {
            // If wherever the regular sort is applied then we need to move all the atlas sort stages into the regular sort stage(In their respecticve sequence).
            // Fetch sortKey and sortDirection
            const [sortKey, sortDirection] = sort.trim().split(/\s+/);
            // If SortKey is invalid or sortDirection is invalid then continue
            if ((!["asc", "desc"].includes(sortDirection)) || (!Object.keys(userColumns).includes(sortKey))) continue;
            // If multiSelectFields has that key then basically we are adding "<sortKey>Values" fields which will be comma separated values of that dropdown
            if (multiSelectFields.includes(sortKey)) {
                if (!sortProcessingStages["$addFields"]) {
                    // const keyVal = `${sortKey}Values`;
                    // Add entry of <sortKey>Values in addFields stage
                    sortProcessingStages["$addFields"] = {
                        [`${sortKey}Values`]: {
                            "$reduce": {
                                "input": {
                                    // Sort by Value
                                    "$sortArray": {
                                        "input": `$${sortfieldPathMap.get(sortKey)}`,
                                        "sortBy": {
                                            "value": 1
                                        }
                                    }
                                },
                                "initialValue": "",
                                "in": {
                                    "$concat": [
                                        "$$value", {
                                            "$cond": [
                                                {
                                                    "$eq": [
                                                        "$$value", ""
                                                    ]
                                                }, "", ", "
                                            ]
                                        }, `${specialArrayFields.has(sortKey) ? "$$this" : "$$this.value"}`
                                    ]
                                }
                            }
                        }
                    };
                } else {
                    sortProcessingStages["$addFields"] = {
                        ...sortProcessingStages["$addFields"],
                        [`${sortKey}Values`]: {
                            "$reduce": {
                                "input": {
                                    "$sortArray": {
                                        "input": `$${sortfieldPathMap.get(sortKey)}`,
                                        "sortBy": {
                                            "value": 1
                                        }
                                    }
                                },
                                "initialValue": "",
                                "in": {
                                    "$concat": [
                                        "$$value", {
                                            "$cond": [
                                                {
                                                    "$eq": [
                                                        "$$value", ""
                                                    ]
                                                }, "", ", "
                                            ]
                                        }, `${specialArrayFields.has(sortKey) ? "$$this" : "$$this.value"}`
                                    ]
                                }
                            }
                        }
                    };
                }

                // Add entry in the regaularSort and set isRegularSortApplied to true
                regularSort = {
                    ...atlasSort,
                    ...regularSort,
                    [`${sortKey}Values`]: sortDirection === "asc" ? 1 : -1
                };
                isRegularSortApplied = true;
            } else if (sortSpecialFieldPathMap.get(sortKey) || isRegularSortApplied) {
                // If sortSpecialFieldPathMap has sortKey and regularSort applied then add entry in the regaularSort and set isRegularSortApplied to true

                regularSort = {
                    ...atlasSort,
                    ...regularSort,
                    [`${sortfieldPathMap.get(sortKey)}`]: sortDirection === "asc" ? 1 : -1
                };
                isRegularSortApplied = true;
            } else if (!isRegularSortApplied) {
                // If regulat sort is not applied then add entry in the atlasSort

                atlasSort = {
                    ...atlasSort,
                    [`${sortfieldPathMap.get(sortKey)}`]: sortDirection === "asc" ? 1 : -1
                };
            }
            // else {
            //     // Here we will be creating name to type map for ease to fetch field type as if its boolean it will go to regular sort
            //     const nameTypeMap = path.commonBase.reduce((nameType: { [x: string]: any; }, field: { name: string | number; type: any; }) => {
            //         nameType[field.name] = field.type;
            //         return nameType;
            //     }, {});
            //     // Once the multiSelect field found, we should use regularSort for the rest fields else we can levarage the atlas sort
            //     if (nameTypeMap[sortKey] === "Boolean" || Object.keys(regularSort).length) {
            //         regularSort = {
            //             ...regularSort,
            //             [`${sortfieldPathMap.get(sortKey)}`]: sortDirection === "asc" ? 1 : -1
            //         };
            //     } else {
            //         atlasSort = {
            //             ...atlasSort,
            //             [`${sortfieldPathMap.get(sortKey)}`]: sortDirection === "asc" ? 1 : -1
            //         };
            //     }
            // }
        }
        if (isRegularSortApplied) atlasSort = {};   // Clear atlasSort if regular sort is applied

        return { atlasSort: Object.keys(atlasSort).length ? atlasSort : undefined, regularSort, sortProcessingStages };
    }

    static fetchFacetFieldsBySectionsForModule(path: any) {
        const facets: { [key: string]: string[] } = {};

        // If the path is not object or it is an array or path value is not defined then return
        if (typeof path !== "object" || Array.isArray(path) || !path) {
            return facets;
        }

        // Loop through all sections of path
        for (const section in path) {
            // @ts-ignore
            if (path[String(section)]) {
                facets[String(section)] = [];

                // Loop through all fields of the section
                // @ts-ignore
                path[String(section)].forEach(field => {
                    // If the field is filterable && it is flexible field or type is dropdown or boolean && we don't need to ignore this field then add it in the facets
                    if (field.filterable && (field.flexibleField || field.type === ValueTypesEnum.DROPDOWN || field.isBooleanField) && !field.ignore) {
                        facets[String(section)].push(field.name);
                    }
                });
            }
        }

        return facets;
    }

    static prepareFacetsFieldWise({ userFacets = [], facetFilter, facetQueryMapping }: {
        userFacets: string[], facetFilter: any, facetQueryMapping: {
            [key: string]:
            {
                id: string | { [key: string]: string },
                value: string | { [key: string]: string },
                isArray?: boolean,
                field?: string
            }
        }
    }) {
        const facets: any = {};
        // Loop through all user facets (Filters which are dropdown / boolean)
        userFacets.forEach((facet) => {
            // Fetch that field's facet data from facetFilter, rest will be all data other than this field
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [String(facet)]: _, ...rest } = facetFilter;

            let restFilters: any = {};
            const orConditions: any = [];
            // Loop through all keys of rest
            Object.keys(rest).forEach((key) => {
                // If in the rest if it has $or then add it in orConditions
                if (rest[String(key)] && rest[String(key)]["$or"]) {
                    orConditions.push(rest[String(key)]);
                } else {
                    // Otherwise add it in restFilters
                    restFilters = { ...restFilters, ...rest[String(key)] };
                }
            });

            // If there are orConditions then add it in restFilters
            if (orConditions.length) {
                restFilters = {
                    ...restFilters,
                    "$and": orConditions
                };
            }

            // Initialize blank array for that facet (field)
            facets[String(facet)] = [];

            // If there are any restFilters then add that with $match
            if (Object.keys(restFilters).length) {
                facets[String(facet)].push({
                    $match: restFilters
                });
            }

            // Check in the facetQueryMapping that if it is an array and it has field then
            if (facetQueryMapping[String(facet)].isArray && facetQueryMapping[String(facet)].field) {
                // Push new stage $unwind to unwind array data
                facets[String(facet)].push({
                    $unwind: {
                        path: facetQueryMapping[String(facet)].field,
                        preserveNullAndEmptyArrays: true
                    }
                });
            }

            facets[String(facet)] = [...facets[String(facet)], {
                // Add new $group stage with field's id as _id, id value as field's id value pair and it's count
                $group: {
                    _id: facetQueryMapping[String(facet)].id,
                    id: {
                        $first: facetQueryMapping[String(facet)].id,
                    },
                    value: {
                        $first: facetQueryMapping[String(facet)].value,
                    },
                    count: {
                        $sum: 1,
                    },
                },
            },
            // Sort it by value
            {
                $sort: {
                    value: 1,
                },
            },
            // If the value is null then set value as Unspecified
            {
                $project: {
                    value: {
                        $ifNull: ["$value", "Unspecified"],
                    },
                    id: 1,
                    count: 1,
                    _id: 0,
                },
            }];
        });

        return facets;
    }

    static prepareQuickFilters({ facetsWithFields, fields, appliedFilters }: { facetsWithFields: any, fields: any, appliedFilters: any }) {
        Object.keys(facetsWithFields).forEach((facet) => {
            const field = fields.find((f: any) => f.name === facet);
            if (field) {
                // Add count zero for flexible field values which are not being used by any records
                field.values?.forEach((value: any) => {
                    const index = facetsWithFields[String(facet)].findIndex((zero: any) => String(zero.id) === String(value.id));
                    if (index === -1) {
                        facetsWithFields[String(facet)].push({ id: value.id, value: value.value, count: 0 });
                    }
                });

                // Save field values
                field.values = facetsWithFields[String(facet)];

                // If it is a boolean field than true/false both values should be present
                if (field.isBooleanField) {
                    const valuesToBePresent = ["true", "false"];

                    valuesToBePresent.map(value => {
                        const valuePresent = (facetsWithFields[String(facet)] ?? []).find((item: { id: string, count: number, value: string }) => item.value === value);
                        if (!valuePresent) {
                            field.values.push({
                                id: value,
                                count: 0,
                                value
                            });
                        }
                    });
                }

                // If the field is quick filter then we will add new option All with total count
                if (field?.isQuickFilter) {
                    const totalCount = facetsWithFields[String(facet)].reduce((total: number, field: any) => total + field.count, 0);
                    field.values = [{ id: "all", value: "All", count: totalCount }, ...facetsWithFields[String(facet)]];
                    if (!appliedFilters[String(field.name)]) {
                        appliedFilters[String(field.name)] = {
                            operator: "in",
                            value: [{ id: "all", value: "All" }]
                        };
                    }
                }
            }
        });
    }

    static prepareAppliedFiltersWithValues({ appliedFilters, fields }: { appliedFilters: any, fields: any }) {
        Object.keys(appliedFilters).forEach((key) => {
            if (appliedFilters[String(key)].value &&
                appliedFilters[String(key)].value[0] &&
                appliedFilters[String(key)].value[0].id &&
                !appliedFilters[String(key)].value[0].value) {
                const field = fields.find((f: any) => f.name === key);
                if (field) {
                    appliedFilters[String(key)].value.forEach((value: any) => {
                        const appliedValue = field.values?.find((v: any) => String(v.id) === String(value.id));
                        if (appliedValue) {
                            value.value = appliedValue.value;
                        }
                    });
                }
            }
        });
    }

    static prepareDataQuery({ collection, must, mustNot, matchQuery, projection = {},
        atlasSort = { createdAt: 1 }, regularSort, searchIndex, sortProcessingStages, optionalQuery, should }:
        {
            must: any[], mustNot: any[], matchQuery: any, projection: any, atlasSort: { [key: string]: 1 | -1 } | null | undefined, searchIndex: string
            regularSort: { [key: string]: 1 | -1 }, collection: Model<any>, sortProcessingStages: { [key: string]: 1 | -1 }, optionalQuery?: any,  should?: any[]
        }) {
        const compound: any = {};
        // If must provided then add must
        if (must.length) {
            compound["must"] = must;
        }
        // If should is provided then add should and minimumShouldMatch to 1
        if (should?.length) {
            compound["should"] = should;
            compound["minimumShouldMatch"] = 1;
        }
        // If mustNot provided then add mustNot
        if (mustNot.length) {
            compound["mustNot"] = mustNot;
        }

        const basicPipeline: any[] = [];
        const compoundKeysLength = Object.keys(compound).length;

        // If there is any key in the compound then add $search stage with compound, index and sort
        if (compoundKeysLength) {
            basicPipeline.push({
                $search: {
                    index: searchIndex,
                    compound,
                    sort: atlasSort
                }
            });
        // CASE: Atlas sort is there but filter is not provided then we are making filter that everytime returns true as we need to apply sort
        // If atlas sort is provided and there aren't any compound keys (Means no filter) then make filter of _id is not null as filter is required and add sort
        } else if(atlasSort && !compoundKeysLength) {
            basicPipeline.push({
                $search: {
                    index: searchIndex,
                    compound: {
                        mustNot: {
                            equals: {
                                path: "_id",
                                value: null
                            }
                        }
                    },
                    sort: atlasSort
                }
            });
        }

        // If there are any optional query then push optionalQuery
        if (optionalQuery?.length) {
            basicPipeline.push(...optionalQuery);
        }

        // If there is matchQuery then push it with $match stage
        if (matchQuery) {
            basicPipeline.push({
                $match: matchQuery
            });
        }

        // If there are any sortProcessingStages then add it in the pipeline
        if (Object.keys(sortProcessingStages).length) {
            basicPipeline.push(sortProcessingStages);
        }

        // If there are any regularSort then push it with $sort stage
        if (Object.keys(regularSort).length) {
            basicPipeline.push({
                "$sort": {
                    ...regularSort
                }
            });
        }

        // Run aggregate query with basic pipeline and in the $project stage provide projection (user columns)
        return collection.aggregate([
            ...basicPipeline,
            {
                $project: projection
            }
        ]
            // {
            //     collation: {
            //         locale: "en",
            //         numericOrdering: true
            //     }
            // }
        );
    }

    static prepareFacetQuery({ collection, must, mustNot, facetMatchQuery, facetFilter, userFacets, facetQueryMapping, searchIndex, optionalQuery }:
        {
            collection: Model<any>, must: any[], mustNot: any[], facetMatchQuery: any, facetFilter: any, userFacets: string[], searchIndex: string, facetQueryMapping: {
                [key: string]:
                {
                    id: string | { [key: string]: string },
                    value: string | { [key: string]: string },
                    isArray?: boolean,
                    field?: string
                }
            }, optionalQuery?: any
        }) {

        const compound: any = {};
        if (must.length) {
            compound["must"] = must;
        }
        if (mustNot.length) {
            compound["mustNot"] = mustNot;
        }

        // Prepare facet query for each every applicable field
        const facets: any = ListAPIHelper.prepareFacetsFieldWise({ userFacets, facetFilter, facetQueryMapping });

        const basicPipeline: any[] = [];

        if (Object.keys(compound).length) {
            basicPipeline.push({
                $search: {
                    index: searchIndex,
                    compound
                }
            });
        }

        if (optionalQuery?.length) {
            basicPipeline.push(...optionalQuery);
        }

        if (facetMatchQuery) {
            basicPipeline.push({
                $match: facetMatchQuery
            });
        }

        return collection.aggregate([
            ...basicPipeline,
            {
                $facet: facets
            }
        ]
            //  {
            //     collation: {
            //         locale: "en",
            //         numericOrdering: true
            //     }
            // }
        );
    }

    static filterParserMust = ({ filter, fieldMapping, facetFilterMapping, sanitizedFields = [], regexFields = [], enableRuntimeSanitization }:
        {
            filter: string, fieldMapping: { [key: string]: string }, facetFilterMapping: string[],
            sanitizedFields?: {
                type: SanitizedTypesEnum, fields: string[]
            }[], regexFields?: string[], enableRuntimeSanitization?: boolean
        }) => {
        const mustAppliedFilters: any = {};
        const operators = ["in", "startsWith", "endsWith", "contains", "eq", "ge", "le", "between"];
        const mustFilters: any[] = [];
        const mustFiltersForFacets: any[] = [];
        const facetFilter: any = {};
        const matchFilters: any[] = [];
        const facetMatchFilters: any[] = [];
        if (!filter) {
            return { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters };
        }

        // Split by and, if there are not filters then return
        // eslint-disable-next-line security/detect-unsafe-regex
        const filters = filter.split(/(?<=^([^']|'[^']*')*) and /gm);
        if (!filters.length) {
            return { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters };
        }

        // Loop through all filters
        filters.forEach(filtering => {
            // Fetch field, operator and value
            const filter = filtering.trim();
            const firstSpaceIndex = filter.indexOf(" ");
            const secondSpaceIndex = filter.indexOf(" ", firstSpaceIndex + 1);
            const fieldKey = filter.substring(0, firstSpaceIndex);
            const operator = filter.substring(firstSpaceIndex + 1, secondSpaceIndex);
            const rawValue = filter.substring(secondSpaceIndex + 1);
            const value = /"/.test(rawValue)
                ? rawValue.replace(/"/g, "").trim()
                : rawValue.replace(/'/g, "").trim();
            const field = fieldMapping[String(fieldKey)];

            const nullApplied: any[] = [];
            // If field, operator or value anything is not present or invalid return
            if ((operator && !operators.includes(operator)) || !field || !value) {
                return;
            }

            // If operator is "in"
            if (operator === "in") {
                // Replace () and split by ","
                const inValues = value
                    .replace("(", "")
                    .replace(")", "")
                    .split(",")
                    .map(val => {
                        // If valid objectId then convert it to objectId
                        if (isValidObjectId(val)) {
                            return convertToObjectId(val);
                        }
                        // If it is "true" or "false" then convert it to boolean
                        if (val.toLowerCase() === "true" || val.toLowerCase() === "false") {
                            return val.toLowerCase() === "true";
                        }
                        return val;
                    });

                // Set filterValue
                const filterValue = {
                    in: {
                        path: field,
                        value: inValues
                    }
                };

                // If inValues includes all then need to add entry in the mustAppliedFilters
                if (inValues.includes("all")) {
                    mustAppliedFilters[String(fieldKey)] = {
                        operator, value: [
                            ...inValues.map(value => ({ id: String(value) })),
                            ...nullApplied
                        ]
                    };
                    return;
                }
                let isFacetFilterAlreadySet = false;
                // If inValues includes null
                if (inValues.includes("null")) {
                    // Remove entry of null from inValues
                    const nullIndex = inValues.indexOf("null");
                    inValues.splice(nullIndex, 1);

                    // Prepare matchValue that can match null in any case and add entry in matchFilters
                    const matchValue = {
                        $or: [
                            { [String(field)]: { $in: [...inValues, null] } },
                            { [String(field)]: [] },
                            { [String(field)]: { $exists: false } },
                            { [String(field)]: "" }
                        ]
                    };
                    matchFilters.push(matchValue);

                    // If facetFilterMapping does not include fieldKey then add matchValue in facetMatchFilters otherwise add entry in the facetFilter
                    // Here, dropdown filters will be there in facetFilterMapping and others will not be there
                    if (!facetFilterMapping.includes(fieldKey)) {
                        facetMatchFilters.push(matchValue);
                    } else {
                        facetFilter[String(fieldKey)] = matchValue;
                        isFacetFilterAlreadySet = true;
                    }

                    // Add entry in the nullApplied
                    nullApplied.push({ id: null, value: "unspecified" });
                } else {
                    // Add entry in the mustFilters
                    mustFilters.push(filterValue);

                    // If facetFilterMapping does not include fieldKey then add fiterValue in mustFiltersForFacets
                    if (!facetFilterMapping.includes(fieldKey)) {
                        mustFiltersForFacets.push(filterValue);
                    }
                }

                // If facetFilterMapping includes fieldKey and isFacetFilterAlreadySet is not set then add entry in facetFilter
                if (facetFilterMapping.includes(fieldKey) && !isFacetFilterAlreadySet) {
                    facetFilter[String(fieldKey)] = { [field]: { $in: inValues } };
                }

                // Add entry in the mustAppliedFilters and return
                mustAppliedFilters[String(fieldKey)] = {
                    operator, value: [
                        ...inValues.map(value => ({ id: String(value) })),
                        ...nullApplied
                    ]
                };
                return;
            }
            // Add entry in the mustAppliedFilters
            mustAppliedFilters[String(fieldKey)] = { operator, value: [{ id: String(value), value: value }] };

            // If operator is startsWith, endsWith or contains
            if (["startsWith", "endsWith", "contains"].includes(operator)) {
                // If Regex field includes that fieldKey
                if (regexFields?.includes(fieldKey)) {
                    // Set filterValue, add entry in the mustFilters and mustFiltersForFacets then return
                    const filterValue = {
                        regex: {
                            path: field,
                            query: `${["endsWith", "contains"].includes(operator) ? ".*" : ""}${value}${["startsWith", "contains"].includes(operator) ? ".*" : ""}`,
                            allowAnalyzedField: true
                        }
                    };
                    mustFilters.push(filterValue);
                    mustFiltersForFacets.push(filterValue);
                    return;
                }

                // If operator is startsWith or endsWith and enableRuntimeSanitization is true then sanitize the value
                let newValue = value;
                if (operator === "startsWith" || operator === "endsWith") {
                    if (enableRuntimeSanitization) {
                        if (isIPAddress(value)) {
                            newValue = operator === "startsWith" ? sanitizeIPForFilters(value) : sanitizeIP(value);
                        } else if (isValidURL(value)) {
                            newValue = sanitizeURI(value);

                        }
                    } else{
                        // Loop through all sanitizedFields and if fieldKey is present then sanitize it
                        sanitizedFields?.forEach(item => {
                            if (item.type === SanitizedTypesEnum.URL && item.fields.includes(fieldKey)) {
                                newValue = sanitizeURI(value);
                            } else if (item.type === SanitizedTypesEnum.IP && item.fields.includes(fieldKey)) {
                                newValue = operator === "startsWith" ? sanitizeIPForFilters(value) : sanitizeIP(value);
                            } else if (item.type === SanitizedTypesEnum.EMAIL && item.fields.includes(fieldKey)) {
                                newValue = sanitizeEmail(value);
                            }
                        });
                    }

                    // Prepare matchValue and push it to matchFilters
                    const matchValue = {
                        [field]: { $regex: operator === "startsWith" ? `^${escapeRegExp(newValue)}` : `${escapeRegExp(newValue)}$`, $options: "i" }
                    };

                    matchFilters.push(matchValue);

                    // If facetFilterMapping does not include fieldKey it then add entry in facetMatchFilters
                    if (!facetFilterMapping.includes(fieldKey)) {
                        facetMatchFilters.push(matchValue);
                    }
                }

                // Prepare filterValue, add entry in mustFilters & mustFiltersForFacets then return
                const filterValue = {
                    autocomplete: {
                        query: newValue,
                        path: field,
                        tokenOrder: "sequential"
                    }
                };
                mustFilters.push(filterValue);
                mustFiltersForFacets.push(filterValue);
                return;
            }

            // If operator is "eq" and it is objectId or number or date then
            if (operator === "eq" && (isValidObjectId(value) || !isNaN(Number(value)) || isValidDate(value))) {
                // Prepare filterValue, add entry in mustFilters & mustFiltersForFacets then return
                const filterValue = {
                    equals: {
                        path: field,
                        value: value
                    }
                };
                mustFilters.push(filterValue);
                mustFiltersForFacets.push(filterValue);
                return;
            }

            // If operator is "eq"
            if (operator === "eq") {
                let newValue: any = value;

                // If facetFilterMapping has that field that means it's a boolean field as eq is not for dropdown. So, transoform value to boolean
                if(facetFilterMapping.includes(fieldKey)) {
                    newValue = value === "true";
                } else if (enableRuntimeSanitization) {
                    // If enableRuntimeSanitization is true then sanitize the value

                    if (isIPAddress(value)) {
                        newValue = sanitizeIP(value);
                    } else if (isValidURL(value)) {
                        newValue = sanitizeURI(value);

                    }
                } else {
                    // Loop through all sanitizedFields and if fieldKey is present then sanitize it
                    sanitizedFields?.forEach(item => {
                        if (item.type === SanitizedTypesEnum.URL && item.fields.includes(fieldKey)) {
                            newValue = sanitizeURI(value);
                        } else if (item.type === SanitizedTypesEnum.IP && item.fields.includes(fieldKey)) {
                            newValue = sanitizeIP(value);
                        } else if (item.type === SanitizedTypesEnum.EMAIL && item.fields.includes(fieldKey)) {
                            newValue = sanitizeEmail(value);
                        }
                    });
                }

                // Prepare filterValue, add entry in mustFilters & mustFiltersForFacets then return
                const filterValue = {
                    equals: {
                        path: field,
                        value: newValue
                    }
                };
                mustFilters.push(filterValue);
                mustFiltersForFacets.push(filterValue);
                return;
            }

            // If operator is "ge" or "le" & value is either number or date then
            if (["ge", "le"].includes(operator) && (!isNaN(Number(value)) || isValidDate(value))) {
                // Prepare rangeOperator, filterValue then add entry in mustFilters & mustFiltersForFacets and then return
                const rangeOperator = operator === "ge" ? "gte" : "lte";
                const filterValue = {
                    range: {
                        path: field,
                        [rangeOperator]: isValidDate(value) && isNaN(Number(value)) ? new Date(value) : Number(value)
                    }
                };
                mustFilters.push(filterValue);
                mustFiltersForFacets.push(filterValue);
                return;
            }

            // If the operator is "between" then relace () and split by ,
            if (operator === "between") {
                const dates = value
                    .replace("(", "")
                    .replace(")", "")
                    .split(",");

                // Add entry in the mustAppliedFilters
                mustAppliedFilters[String(fieldKey)] = {
                    operator, value: [
                        ...dates.map(value => ({ id: String(value), value: String(value) }))
                    ]
                };

                // Filter will only be applied when startDate and endDate are specified
                if (dates.length === 2 && dates.every(isValidDate) && (new Date(dates[0]) < new Date(dates[1]))) {
                    // Prepare filterValue, add entry in mustFilters & mustFiltersForFacets then return
                    const filterValue = {
                        range: {
                            path: field,
                            gte: new Date(dates[0]),
                            lte: new Date(dates[1])
                        }
                    };
                    mustFilters.push(filterValue);
                    mustFiltersForFacets.push(filterValue);
                    return;
                }

            }
        });
        return { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters };
    };

    static filterParserMustNot = ({ filter, fieldMapping, facetFilterMapping, sanitizedFields = [], enableRuntimeSanitization }:
        {
            filter: string, fieldMapping: { [key: string]: string }, facetFilterMapping: string[],
            sanitizedFields?: {
                type: SanitizedTypesEnum, fields: string[]
            }[], enableRuntimeSanitization?: boolean
        }) => {
        const mustNotAppliedFilters: any = {};
        const operators = ["nin", "ne"];
        const mustNotFilters: any[] = [];
        const mustNotFiltersForFacets: any[] = [];
        const matchNotFilters: any[] = [];
        const facetMatchNotFilters: any[] = [];
        if (!filter) {
            return { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters };
        }
        // Split by and, if there are not filters then return

        // eslint-disable-next-line security/detect-unsafe-regex
        const filters = filter.split(/(?<=^([^']|'[^']*')*) and /gm);
        if (!filters.length) {
            return { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters };
        }

        // Loop through all filters
        filters.forEach(filtering => {
            // Fetch field, operator and value
            const filter = filtering.trim();
            const firstSpaceIndex = filter.indexOf(" ");
            const secondSpaceIndex = filter.indexOf(" ", firstSpaceIndex + 1);
            const fieldKey = filter.substring(0, firstSpaceIndex);
            const operator = filter.substring(firstSpaceIndex + 1, secondSpaceIndex);
            const rawValue = filter.substring(secondSpaceIndex + 1);
            const value = /"/.test(rawValue)
                ? rawValue.replace(/"/g, "").trim()
                : rawValue.replace(/'/g, "").trim();
            const field = fieldMapping[String(fieldKey)];
            const nullApplied: any[] = [];

            // If field, operator or value anything is not present or invalid return
            if ((operator && !operators.includes(operator)) || !field || !value) {
                return;
            }

            // If the operator is nin
            if (operator === "nin") {
                // Replace () with blank string and split by ,
                const inValues = value
                    .replace("(", "")
                    .replace(")", "")
                    .split(",")
                    .map(val => {
                        // If it is valid object id then convert it to objectId
                        if (isValidObjectId(val)) {
                            return convertToObjectId(val);
                        }
                        // If it is true or false then convert it to boolean
                        if (val.toLowerCase() === "true" || val.toLowerCase() === "false") {
                            return val.toLowerCase() === "true";
                        }
                        return val;
                    });

                // Prepare filterValue
                const filterValue = {
                    in: {
                        path: field,
                        value: inValues
                    }
                };

                // If inValues includes null
                if (inValues.includes("null")) {
                    // Remove null from inValues
                    const nullIndex = inValues.indexOf("null");
                    inValues.splice(nullIndex, 1);

                    // Prepare match value to match not in null and Add entry in matchNotFilters
                    const matchValue = {
                        $and: [
                            { [String(field)]: { $nin: [...inValues, null] } },
                            { [String(fieldKey)]: { $ne: [] } },
                            { [String(field)]: { $exists: true } }
                        ]
                    };

                    matchNotFilters.push(matchValue);

                    // If facetFilterMapping does not have that key then add entry to facetMatchNotFilters
                    if (!facetFilterMapping.includes(fieldKey)) {
                        facetMatchNotFilters.push(matchValue);
                    }

                    // Add entry in nullApplied
                    nullApplied.push({ id: null, value: "unspecified" });
                } else {
                    // Add entry in mustNotFilters
                    mustNotFilters.push(filterValue);

                    // If facetFilterMapping does not have that field then add it to mustNotFiltersForFacets
                    if (!facetFilterMapping.includes(fieldKey)) {
                        mustNotFiltersForFacets.push(filterValue);
                    }
                }

                // Add entry in mustNotAppliedFilters and return
                mustNotAppliedFilters[String(fieldKey)] = {
                    operator, value: [
                        ...inValues.map(value => ({ id: String(value) })),
                        ...nullApplied
                    ]
                };
                return;
            }

            // Add entry in the mustAppliedFilters
            mustNotAppliedFilters[String(fieldKey)] = { operator, value: [{ id: String(value), value: value }] };

            // If operator is "ne" & value is either objectid, number or date
            if (operator === "ne" && (isValidObjectId(value) || !isNaN(Number(value)) || isValidDate(value))) {
                // Prepare filterValue and add entry in the mustNotFilters, mustNotFiltersForFacets then return
                const filterValue = {
                    equals: {
                        path: field,
                        value: value
                    }
                };
                mustNotFilters.push(filterValue);
                mustNotFiltersForFacets.push(filterValue);
                return;
            }

            // If operator is "ne", so now value will be of type string
            if (operator === "ne") {
                // If enableRuntimeSanitization is true then sanitize the value
                let newValue = value;
                if (enableRuntimeSanitization) {
                    if (isIPAddress(value)) {
                        newValue = sanitizeIP(value);
                    } else if (isValidURL(value)) {
                        newValue = sanitizeURI(value);

                    }
                } else {
                    // Loop through all sanitizedFields and if fieldKey is present then sanitize it
                    sanitizedFields?.forEach(item => {
                        if (item.type === SanitizedTypesEnum.URL && item.fields.includes(fieldKey)) {
                            newValue = sanitizeURI(value);
                        } else if (item.type === SanitizedTypesEnum.IP && item.fields.includes(fieldKey)) {
                            newValue = sanitizeIP(value);
                        } else if (item.type === SanitizedTypesEnum.EMAIL && item.fields.includes(fieldKey)) {
                            newValue = sanitizeEmail(value);
                        }
                    });
                }

                // Prepare filterValue and add entry in the mustNotFilters, mustNotFiltersForFacets then return
                const filterValue = {
                    text: {
                        path: field,
                        query: newValue
                    }
                };
                mustNotFilters.push(filterValue);
                mustNotFiltersForFacets.push(filterValue);
                return;
            }
        });
        return { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters };
    };

    static filterParserBlank = ({ filter, fieldMapping }: { filter: string, fieldMapping: { [key: string]: string } }) => {
        const operators = ["isBlank"];
        const blankFilters: any[] = [];
        const blankAppliedFilters: any = {};
        if (!filter) {
            return { blankFilters, blankAppliedFilters };
        }
        // Split by and, if there are not filters then return
        // eslint-disable-next-line security/detect-unsafe-regex
        const filters =  filter.split(/(?<=^([^']|'[^']*')*) and /gm);

        // Loop through all filters
        filters.forEach(filtering => {
            // Fetch field, operator
            const filter = filtering.trim();
            const [fieldKey, operator] = filter.split(" ");
            const field = fieldMapping[String(fieldKey)];
            // If field, operator is not present or invalid return
            if ((operator && !operators.includes(operator)) || !field) {
                return;
            }

            // Add entry in blankFilters and blank applied filters
            blankFilters.push({
                $or: [
                    {
                        [field]: { $exists: false }
                    },
                    {
                        [field]: null
                    },
                    {
                        [field]: ""
                    },
                    {
                        [field]: []
                    }
                ]
            });
            blankAppliedFilters[String(fieldKey)] = { operator, value: [] };
        });
        return { blankFilters, blankAppliedFilters };
    };

}
