import { BlobServiceClient, ContainerCreateOptions, BlockBlobParallelUploadOptions } from "@azure/storage-blob";
import { generateUUID } from "./generate-uuid";
import { unlink } from "fs/promises";
import { TemporaryFile } from "../models/temporary-files.model";
import { UploadTemporaryFileParams } from "../interfaces/upload-temporary-files-params";

export const uploadTemporaryFile = async ({ parent, filePath, fileName, userId,
    AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING, mimeType }: UploadTemporaryFileParams): Promise<{ id: string, url?: string }> => {
    try {
        if (!AZURE_STORAGE_CONNECTION_STRING) {
            throw Error("Cannot upload.");
        }

        const blobServiceClient = BlobServiceClient.fromConnectionString(
            AZURE_STORAGE_CONNECTION_STRING
        );

        const containerClient = blobServiceClient.getContainerClient(parent);
        const options: ContainerCreateOptions = {};

        await containerClient.createIfNotExists(options);
        const blobName = `${generateUUID()}-${fileName}`;
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);

        // To access files through CDN file type must be defined otherwise it will consider application octet stream.
        const httpHeaders: BlockBlobParallelUploadOptions = {};
        if (mimeType) {
            httpHeaders.blobHTTPHeaders = {
                "blobContentType": mimeType
            };
        }

        await blockBlobClient.uploadFile(filePath, httpHeaders);
        const file = TemporaryFile.build({
            fileName: blobName,
            originalFileName: fileName,
            uploadedBy: userId,
            parent,
        });
        await file.save();

        return { id: file.id };
    } catch (error) {
        console.error("Common.Util.UploadTemporaryFile");
        console.error(error);
        throw Error("Something went wrong.");
    } finally {
        // eslint-disable-next-line security/detect-non-literal-fs-filename
        await unlink(filePath);
    }
};
