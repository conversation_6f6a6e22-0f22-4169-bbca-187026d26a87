import { isValidMongoObjectId } from "./index";

export const everyElementMongoId = (valueIds: string[]) => {
    return valueIds.every(valueId => {
        return isValidMongoObjectId(valueId);
    });
};

export const urlValidation = (value: string) => {
    /* eslint-disable-next-line */
    const urlValidation = /^$|^((ftp|http|https):\/\/)?(www\.)?([\w$+!*'(),#%{}|\\^~[\]`<>-]+(\.[\w$+!*'(),#%{}|\\^~[\]`<>-]+)+)(\/[\w$+!*'(),#%{}|\\^~[\]`<>.-]*)*(\?[\w$+!*'(),#%{}|\\^~[\]`<>-]+=[^&=]+(&[\w$+!*'(),#%{}|\\^~[\]`<>-]+=[^&=]+)*)?$/im;
    return urlValidation.test(value);
};
