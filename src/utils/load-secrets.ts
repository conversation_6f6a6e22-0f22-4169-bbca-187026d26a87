import { SecretClient }  from "@azure/keyvault-secrets";
import { ClientSecretCredential } from "@azure/identity";
import { Secrets } from "../enums/secrets";

const envVariableNamesMapping: {[key: string]: string} = {
    "encryption-key-secret": "ENCRYPTION_KEY",
    "moxfive-id-secret": "MOXFIVE_ID",
    "azure-storage-connection-string-secret": "AZURE_STORAGE_CONNECTION_STRING",
    "client-state-secret": "CLIENT_STATE",
    "cookie-secret-secret": "COOKIE_SECRET",
    "email-connection-string-secret": "EMAIL_CONNECTION_STRING",
    "enterprise-object-id-secret": "ENTERPRISE_OBJECT_ID",
    "harvest-auth-token-secret": "HARVEST_AUTH_TOKEN",
    "harvest-client-id-secret": "HARVEST_CLIENT_ID",
    "jwt-secret": "JWT_SECRET",
    "mongo-uri-secret": "MON<PERSON><PERSON>_URI",
    "response-encryption-key-secret": "RESPONSE_ENCRYPTION_KEY",
    "super-admin-policy-id-secret": "SUPER_ADMIN_P<PERSON>ICY_ID",
    "webhook-access-token-secret": "WEBHOOK_ACCESS_TOKEN",
    "cdn-endpoint-secret": "CDN_ENDPOINT",
    "azure-cdn-storage-connection-string-secret": "AZURE_CDN_STORAGE_CONNECTION_STRING",
    "system-client-id-secret": "SYSTEM_CLIENT_ID",
    "system-client-secret-secret": "SYSTEM_CLIENT_SECRET",
    "system-ip-address-secret": "SYSTEM_IP_ADDRESS",
    "system-user-id-secret": "SYSTEM_USER_ID",
    "SNOWFLAKE-USERNAME": "SNOWFLAKE_USER_NAME",
    "SNOWFLAKE-READER-PASSWORD": "SNOWFLAKE_READER_PASSWORD",
    "SNOWFLAKE-READER-USERNAME": "SNOWFLAKE_READER_USER_NAME",
    "SNOWFLAKE-ROLE": "SNOWFLAKE_ROLE",
    "SNOWFLAKE-PASSWORD": "SNOWFLAKE_PASSWORD",
    "SNOWFLAKE-ACCOUNT-NAME": "SNOWFLAKE_ACCOUNT_NAME",
    "SNOWFLAKE-AUTHORIZATION-TOKEN": "SNOWFLAKE_AUTHORIZATION_TOKEN",
    "azure-assets-storage-connection-string-secret": "AZURE_ASSETS_STORAGE_CONNECTION_STRING",
    "jwt-refresh-token-secret": "JWT_REFRESH_TOKEN",
    "auth0-domain": "AUTH0_DOMAIN",
    "auth0-web-app-client-id": "AUTH0_WEB_APP_CLIENT_ID",
    "auth0-web-app-client-secret": "AUTH0_WEB_APP_CLIENT_SECRET",
    "auth0-spa-app-client-id": "AUTH0_SPA_APP_CLIENT_ID",
    "auth0-spa-app-client-secret": "AUTH0_SPA_APP_CLIENT_SECRET",
    "nats-token-secret": "NATS_TOKEN",
    "auth0-webhook-token-secret": "AUTH0_WEBHOOK_TOKEN",
    "recaptcha-v2-secret-key-secret": "RECAPTCHA_V2_SECRET_KEY",
    "recaptcha-v3-secret-key-secret": "RECAPTCHA_V3_SECRET_KEY",
};

export const loadSecrets = async (secrets: Secrets[]): Promise<void> => {
    try {
        const credential = new ClientSecretCredential(String(process.env.TENANT_ID), String(process.env.CLIENT_ID), String(process.env.CLIENT_SECRET));

        const url = `https://${String(process.env.KEY_VAULT_NAME)}.vault.azure.net`;

        const client = new SecretClient(url, credential);
        await setSecrets(client, new Set(secrets));
    } catch (error) {
        console.info(error);
    }
};

const setSecrets = async (client: SecretClient, secrets: Set<Secrets>): Promise<void> => {
    const [secretName] = secrets;
    try {
        if(!secretName) {
            return;
        }
        secrets.delete(secretName);
        // eslint-disable-next-line security/detect-object-injection
        if (!envVariableNamesMapping[secretName]) {
            return setSecrets(client, secrets);
        }
        const secret = await client.getSecret(secretName);
        if(!secret) {
            return setSecrets(client, secrets);
        }
        // eslint-disable-next-line security/detect-object-injection
        process.env[envVariableNamesMapping[secretName]] = secret.value;
        return setSecrets(client, secrets);
    } catch (error) {
        console.info(error);
        secrets.delete(secretName);
        return setSecrets(client, secrets);
    }
};
