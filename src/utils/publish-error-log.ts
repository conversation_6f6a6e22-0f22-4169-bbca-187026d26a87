import { Request } from "express";
import { initiatorPermission<PERSON>ogObj } from "../interfaces/initiator-permission-log-obj";
import { requestLogObj } from "../interfaces/request-log-obj";
import { responseLogObj } from "../interfaces/response-log-obj";
import { auditLogPublisherWrapper } from "./audit-log-publisher-wrapper";
import { eventServices } from "./event-services";
import { generateUUID } from "./generate-uuid";

export const publishErrorLog = async (req: Request, errorCode: number, errorMsg: string, respBody: string) => {
    const route = req.originalUrl.split("?").shift() as string;
    const serviceName = route.split("/")[2] || "";
    // eslint-disable-next-line security/detect-object-injection
    const service = eventServices[serviceName] || eventServices["authentication"];

    const request: requestLogObj = {
        requestId: generateUUID(),
        timestamp: Date.now(),
        method: req.method,
        api: route,
        userId: "",
        applicationId: "",
        m5pTenantId: "",
        ip: req.ip ?? "",
        headers: {
            userAgent: req.headers["user-agent"] || "",
            referer: req.headers["referer"] || "",
            origin: req.hostname || "",
            acceptEncoding: typeof req.headers["accept-encoding"] === "string" ? req.headers["accept-encoding"] : (req.headers["accept-encoding"] || []).join(", "),
            xM5PHostname: req.headers["x-m5p-hostname"] as string || "",
            xM5PHostip: req.headers["x-m5p-hostip"] as string || "",
            contentType: req.headers["content-type"] || "",
            contentLength: req.headers["content-length"] || "",
            accept: req.headers["accept"] || ""
        },
        body: "",
        queryParams: {},
        pathParams: {}
    };

    if (req.query && Object.keys(req.query).length) {
        request.queryParams = req.query as { [key: string]: string };
    }

    if (req.params && Object.keys(req.params).length) {
        request.pathParams = req.params as { [key: string]: string };
    }

    if (req.body && Object.keys(req.body).length) {
        request.body = JSON.stringify(req.body);
    }

    const response: responseLogObj = {
        timestamp: Date.now(),
        correlation: "",
        correlationId: "",
        result: "Failure",
        resultReason: errorMsg || "",
        body: respBody || "",
        responseStatusCode: errorCode || 0,
        platformResponseStatusCode: 0
    };

    const initiatorPermission: initiatorPermissionLogObj = {
        unauthorizedAccess: true,
        policies: [],
        action: ""
    };

    await auditLogPublisherWrapper({
        request: request,
        response: response,
        initiatorPermission: initiatorPermission,
        targets: [],
        modifiedProperties: []
    }, service);
};
