import { BlobServiceClient } from "@azure/storage-blob";
import { ResourceNotFoundError } from "../errors/resource-not-found";
import { File, FileDoc } from "../models/files.model";
import { NotFoundCode } from "./not-found-codes";
import { DownloadFileParams } from "../interfaces/download-file-params";

// eslint-disable-next-line max-len
export const downloadFile = async ({ parent, parentId, entity, entityId, fileId, AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING }: DownloadFileParams): Promise<{ filePath: string; file: FileDoc; }> => {
    try {
        if (!AZURE_STORAGE_CONNECTION_STRING) {
            throw Error("Cannot download.");
        }

        const file = await File.findOne({ _id: fileId, parent, parentId, entity, entityId });
        if (!file) {
            throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "File not found.");
        }

        const blobServiceClient = BlobServiceClient.fromConnectionString(
            AZURE_STORAGE_CONNECTION_STRING
        );
        const filePath = `/uploads/${Date.now()}-${file.fileName}`;
        const containerClient = blobServiceClient.getContainerClient(file.parent);
        await containerClient.createIfNotExists();
        const blockBlobClient = containerClient.getBlockBlobClient(file.fileName);
        await blockBlobClient.downloadToFile(filePath);
        return { filePath, file };
    } catch (error) {
        console.error("Common.Util.DownloadFile");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
