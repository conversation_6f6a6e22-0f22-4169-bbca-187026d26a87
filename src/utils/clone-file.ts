import { File } from "../models/files.model";
import { CloneFileParams } from "../interfaces/clone-file-params";
import { downloadFile } from "./download-file";
import { fetchMimeTypeFromFileName } from "./index";
import { uploadFile } from "./upload-file";

export const cloneFile = async ({ source, destination, userId, publicContainer = false, isCDNEndpointRequired = false,
    AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING }: CloneFileParams): Promise<{ id: string, url?: string }> => {
    try {
        // CDN is not always required, ex. Incident attachments modules don't need any CDN URLs as they are data files.
        if (!AZURE_STORAGE_CONNECTION_STRING || (isCDNEndpointRequired && !process.env.CDN_ENDPOINT)) {
            throw Error("Cannot clone.");
        }

        // Fetch the parent file
        const parentFile = await File.findOne({
            parent: source.parent,
            parentId: source.parentId,
            entity: source.entity,
            entityId: source.entityId
        }).lean().exec();

        if (!parentFile) {
            throw Error("Parent file does not exist");
        }

        // Download the file using source information
        const { filePath } = await downloadFile({
            parent: source.parent,
            parentId: source.parentId,
            entity: source.entity,
            entityId: source.entityId,
            fileId: String(parentFile._id),
            AZURE_STORAGE_CONNECTION_STRING
        });

        // Fetch the mimeType
        const mimeType = fetchMimeTypeFromFileName(parentFile.fileName);

        // Upload the file using destination information
        return await uploadFile({
            parent: destination.parent,
            parentId: destination.parentId,
            entity: destination.entity,
            entityId: destination.entityId,
            filePath,
            fileName: parentFile.originalFileName,
            userId,
            publicContainer,
            isCDNEndpointRequired,
            AZURE_STORAGE_CONNECTION_STRING,
            mimeType
        });
    } catch (error) {
        console.error("Common.Util.DownloadFile");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
