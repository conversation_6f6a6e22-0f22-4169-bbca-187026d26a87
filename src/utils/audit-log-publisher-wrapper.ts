import { AuditLogPublisher } from "../events/publishers/audit-log-publisher";
import { auditLogPublisherObj } from "../interfaces/audit-log-publisher-obj";
import { natsConnection } from "../libs/nats-connection";
import { Policy } from "../models/policy.model";
import { User } from "../models/user.model";

export const auditLogPublisherWrapper = async (data: auditLogPublisherObj, service: string) => {
    const userId = data.request.userId;

    if (userId) {
        // Fetch user's attached policies
        const userDetails = await User.findOne({ _id: userId },
            { policyIds: 1, applicationPolicyIds: 1 }
        ).lean().exec();
        const policyDetails = await Policy.find(
            { _id: { $in: [...(userDetails?.policyIds || []), ...(userDetails?.applicationPolicyIds.map(policy => policy.policyId) || [])] } },
            { _id: 1 }
        ).lean().exec();

        data.initiatorPermission.policies = policyDetails.map(policy => policy._id);
    }

    await new AuditLogPublisher(natsConnection.client, service).publish(data);
};
