/* eslint-disable no-useless-escape */
/* eslint-disable max-len */
import mongoose, { Types } from "mongoose";

const ObjectId = Types.ObjectId;

export * from "../libs/logger";

export const ipV46Regex =
    // eslint-disable-next-line security/detect-unsafe-regex
    /(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-fA-F\d]{1,4}:){7}(?:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,2}|:)|(?:[a-fA-F\d]{1,4}:){4}(?:(?::[a-fA-F\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,3}|:)|(?:[a-fA-F\d]{1,4}:){3}(?:(?::[a-fA-F\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,4}|:)|(?:[a-fA-F\d]{1,4}:){2}(?:(?::[a-fA-F\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,5}|:)|(?:[a-fA-F\d]{1,4}:){1}(?:(?::[a-fA-F\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,6}|:)|(?::(?:(?::[a-fA-F\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,7}|:)))(?:%[0-9a-zA-Z]{1,})?$)/gm;

export const urlRegex = /^$|^((ftp|http|https):\/\/)?(www\.)?([\w$+!*'(),#%{}|\\^~[\]`<>-]+(\.[\w$+!*'(),#%{}|\\^~[\]`<>-]+)+)(\/[\w$+!*'(),#%{}|\\^~[\]`<>.-]*)*(\?[\w$+!*'(),#%{}|\\^~[\]`<>-]+=[^&=]+(&[\w$+!*'(),#%{}|\\^~[\]`<>-]+=[^&=]+)*)?$/im;

export const isValidMongoObjectId = (id: string) => {
    return mongoose.isValidObjectId(id);
};

export const isIPAddress = (value: string) => ipV46Regex.test(value || "");

const hasProtocol = new RegExp("^([a-z]+://|//)", "i");

export const restrictSpacesAfterPeriod = (number: number, spaces = 2) => {
    const splitNumber = String(number).split(".");
    if (splitNumber.length > 2) {
        return false;
    }

    if (splitNumber.length === 2 && splitNumber[1].length > +spaces) {
        return false;
    }

    return true;
};

export const getUserName = ({ firstName, lastName, displayName }: { firstName: string | null, lastName: string | null, displayName: string }) => {
    if (!firstName && !lastName) {
        return displayName;
    }
    let name = "";
    if (firstName) {
        name += firstName;
        if (lastName) {
            name += " ";
        }
    }
    if (lastName) {
        name += lastName;
    }
    return name;
};

const fileExtensionsToMimeTypes: Record<string, string> = {
    //   File Extension   MIME Type
    "abs": "audio/x-mpeg",
    "ai": "application/postscript",
    "aif": "audio/x-aiff",
    "aifc": "audio/x-aiff",
    "aiff": "audio/x-aiff",
    "aim": "application/x-aim",
    "art": "image/x-jg",
    "asf": "video/x-ms-asf",
    "asx": "video/x-ms-asf",
    "au": "audio/basic",
    "avi": "video/x-msvideo",
    "avx": "video/x-rad-screenplay",
    "bcpio": "application/x-bcpio",
    "bin": "application/octet-stream",
    "bmp": "image/bmp",
    "body": "text/html",
    "cdf": "application/x-cdf",
    "cer": "application/pkix-cert",
    "class": "application/java",
    "cpio": "application/x-cpio",
    "csh": "application/x-csh",
    "css": "text/css",
    "dib": "image/bmp",
    "doc": "application/msword",
    "dtd": "application/xml-dtd",
    "dv": "video/x-dv",
    "dvi": "application/x-dvi",
    "eot": "application/vnd.ms-fontobject",
    "eps": "application/postscript",
    "etx": "text/x-setext",
    "exe": "application/octet-stream",
    "gif": "image/gif",
    "gtar": "application/x-gtar",
    "gz": "application/x-gzip",
    "hdf": "application/x-hdf",
    "hqx": "application/mac-binhex40",
    "htc": "text/x-component",
    "htm": "text/html",
    "html": "text/html",
    "ief": "image/ief",
    "jad": "text/vnd.sun.j2me.app-descriptor",
    "jar": "application/java-archive",
    "java": "text/x-java-source",
    "jnlp": "application/x-java-jnlp-file",
    "jpe": "image/jpeg",
    "jpeg": "image/jpeg",
    "jpg": "image/jpeg",
    "js": "application/javascript",
    "jsf": "text/plain",
    "json": "application/json",
    "jspf": "text/plain",
    "kar": "audio/midi",
    "latex": "application/x-latex",
    "m3u": "audio/x-mpegurl",
    "mac": "image/x-macpaint",
    "man": "text/troff",
    "mathml": "application/mathml+xml",
    "me": "text/troff",
    "mid": "audio/midi",
    "midi": "audio/midi",
    "mif": "application/x-mif",
    "mov": "video/quicktime",
    "movie": "video/x-sgi-movie",
    "mp1": "audio/mpeg",
    "mp2": "audio/mpeg",
    "mp3": "audio/mpeg",
    "mp4": "video/mp4",
    "mpa": "audio/mpeg",
    "mpe": "video/mpeg",
    "mpeg": "video/mpeg",
    "mpega": "audio/x-mpeg",
    "mpg": "video/mpeg",
    "mpv2": "video/mpeg2",
    "ms": "application/x-wais-source",
    "nc": "application/x-netcdf",
    "oda": "application/oda",
    "odb": "application/vnd.oasis.opendocument.database",
    "odc": "application/vnd.oasis.opendocument.chart",
    "odf": "application/vnd.oasis.opendocument.formula",
    "odg": "application/vnd.oasis.opendocument.graphics",
    "odi": "application/vnd.oasis.opendocument.image",
    "odm": "application/vnd.oasis.opendocument.text-master",
    "odp": "application/vnd.oasis.opendocument.presentation",
    "ods": "application/vnd.oasis.opendocument.spreadsheet",
    "odt": "application/vnd.oasis.opendocument.text",
    "otg": "application/vnd.oasis.opendocument.graphics-template",
    "oth": "application/vnd.oasis.opendocument.text-web",
    "otp": "application/vnd.oasis.opendocument.presentation-template",
    "ots": "application/vnd.oasis.opendocument.spreadsheet-template",
    "ott": "application/vnd.oasis.opendocument.text-template",
    "ogx": "application/ogg",
    "ogv": "video/ogg",
    "oga": "audio/ogg",
    "ogg": "audio/ogg",
    "otf": "application/x-font-opentype",
    "spx": "audio/ogg",
    "flac": "audio/flac",
    "anx": "application/annodex",
    "axa": "audio/annodex",
    "axv": "video/annodex",
    "xspf": "application/xspf+xml",
    "pbm": "image/x-portable-bitmap",
    "pct": "image/pict",
    "pdf": "application/pdf",
    "pgm": "image/x-portable-graymap",
    "pic": "image/pict",
    "pict": "image/pict",
    "pls": "audio/x-scpls",
    "png": "image/png",
    "pnm": "image/x-portable-anymap",
    "pnt": "image/x-macpaint",
    "ppm": "image/x-portable-pixmap",
    "ppt": "application/vnd.ms-powerpoint",
    "pps": "application/vnd.ms-powerpoint",
    "ps": "application/postscript",
    "psd": "image/vnd.adobe.photoshop",
    "qt": "video/quicktime",
    "qti": "image/x-quicktime",
    "qtif": "image/x-quicktime",
    "ras": "image/x-cmu-raster",
    "rdf": "application/rdf+xml",
    "rgb": "image/x-rgb",
    "rm": "application/vnd.rn-realmedia",
    "roff": "text/troff",
    "rtf": "application/rtf",
    "rtx": "text/richtext",
    "sfnt": "application/font-sfnt",
    "sh": "application/x-sh",
    "shar": "application/x-shar",
    "sit": "application/x-stuffit",
    "snd": "audio/basic",
    "src": "application/x-wais-source",
    "sv4cpio": "application/x-sv4cpio",
    "sv4crc": "application/x-sv4crc",
    "svg": "image/svg+xml",
    "svgz": "image/svg+xml",
    "swf": "application/x-shockwave-flash",
    "t": "text/troff",
    "tar": "application/x-tar",
    "tcl": "application/x-tcl",
    "tex": "application/x-tex",
    "texi": "application/x-texinfo",
    "texinfo": "application/x-texinfo",
    "tif": "image/tiff",
    "tiff": "image/tiff",
    "tr": "text/troff",
    "tsv": "text/tab-separated-values",
    "ttf": "application/x-font-ttf",
    "txt": "text/plain",
    "ulw": "audio/basic",
    "ustar": "application/x-ustar",
    "vxml": "application/voicexml+xml",
    "xbm": "image/x-xbitmap",
    "xht": "application/xhtml+xml",
    "xhtml": "application/xhtml+xml",
    "xls": "application/vnd.ms-excel",
    "xml": "application/xml",
    "xpm": "image/x-xpixmap",
    "xsl": "application/xml",
    "xslt": "application/xslt+xml",
    "xul": "application/vnd.mozilla.xul+xml",
    "xwd": "image/x-xwindowdump",
    "vsd": "application/vnd.visio",
    "wav": "audio/x-wav",
    "wbmp": "image/vnd.wap.wbmp",
    "wml": "text/vnd.wap.wml",
    "wmlc": "application/vnd.wap.wmlc",
    "wmls": "text/vnd.wap.wmlsc",
    "wmlscriptc": "application/vnd.wap.wmlscriptc",
    "wmv": "video/x-ms-wmv",
    "woff": "application/font-woff",
    "woff2": "application/font-woff2",
    "wrl": "model/vrml",
    "wspolicy": "application/wspolicy+xml",
    "z": "application/x-compress",
    "zip": "application/zip"
};

export const fetchMimeTypeFromFileName = (fileName: string) => {
    const extension = fileName.split(".").pop();
    return fileExtensionsToMimeTypes[extension ?? ""] ?? "";
};

export function unSanitizeURL(sanitizedString: string) {
    const hasProtocol = new RegExp("^([a-z]+://|//)", "i");
    let actualString = sanitizedString.replace(/\[:]/, ":");
    if (hasProtocol.test(actualString)) {
        actualString = actualString.replace(/xx/, "tt");
    }

    actualString = actualString.replace(/\[.]/g, ".");
    return actualString;
}

export function convertToObjectId(id: string) {
    return new mongoose.Types.ObjectId(id);
}

export const escapeRegExp = (s: string) => {
    return s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

export const sanitizeEmail = (email: string) => {
    return email.replace(/\./g, "[.]");
};

export const sanitizeIP = (ipAddress: string) => {
    return ipAddress.replace(/\.(?=[^\.]+$)/, "[.]");
};

export const sanitizeIPForFilters = (ipAddress: string) => {
    // eslint-disable-next-line security/detect-unsafe-regex
    return ipAddress.replace(/^((?:\d{1,3}\.){2}\d{1,3})\./, "$1[.]");
};

const doesURIContainsProtocol = (str: string) => {
    return hasProtocol.test(str);
};

export const sanitizeURI = (URIString: string) => {
    let sanitizedString = URIString;
    if (doesURIContainsProtocol(sanitizedString)) {
        sanitizedString = sanitizedString.replace(/tt/, "xx");
        sanitizedString = sanitizedString.replace(/\:/, "[:]");
    }

    sanitizedString = sanitizedString.replace(/\./g, "[.]");
    return sanitizedString;
};

export const isValidObjectId = (id: string) => {
    if (ObjectId.isValid(id)) {
        if ((String)(new ObjectId(id)) === id) {
            return true;
        }
        return false;
    }
    return false;
};

export const isValidDate = (str: string) => {
    if (str.includes(" ")) return false;
    const date = new Date(str);
    return date.toString() !== "Invalid Date";
};

export const isValidURL = (value: string) => urlRegex.test(value || "");

export const validExtensions: Record<string, string[]> = {
    "image/jpeg": ["jpg", "jpeg", "jfif", "pjpeg", "pjp"],
    "image/png": ["png"],
    "image/svg+xml": ["svg"],
    "application/pdf": ["pdf"],
    "application/msword": ["doc"],
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ["docx"],
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": ["xlsx"],
    "application/vnd.ms-excel": ["xls"],
    "text/csv": ["csv"],
    "application/x-x509-ca-cert": ["pem"],
    "application/json": ["json"],
    "application/pkix-cert": ["cer"],
    "application/xml": ["xml"],
    "application/octet-stream": ["pem"],
    "text/xml": ["xml"],
    "application/x-pem-file": ["pem"],
    "application/x-x509-user-cert": ["pem"],
    "application/rss+xml": ["xml"],
    "application/atom+xml": ["xml"],
    "application/xslt+xml": ["xml"],
    "application/mathml+xml": ["xml"],
    "application/xhtml+xml": ["xml"]
};
