import { File } from "../models/files.model";
import { FileType } from "../interfaces/file-type";

export const listFiles = async (parent: string, parentId: string, entity: string, entityId: string): Promise<FileType[]> => {
    try {
        const files: FileType[] = [];
        const filesData = await File.find({ parent, parentId, entity, entityId }, { originalFileName: 1, uploadedBy: 1, uploadedOn: 1 }).lean().exec();
        filesData.forEach(file => {
            files.push({
                id: file._id as string,
                originalFileName: file.originalFileName,
                uploadedBy: file.uploadedBy,
                uploadedOn: file.uploadedOn
            });
        });
        return files;
    } catch (error) {
        console.error("Common.Util.ListFiles");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
