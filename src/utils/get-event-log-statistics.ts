import { EventTypeEnum } from "../enums/event-type";
import { EventMessageLogs } from "../models/event-message-logs.model";

export const getEventLogStatistics = async () => {
    const totalProduced = await EventMessageLogs.countDocuments({ type: EventTypeEnum.PRODUCER }).lean().exec();
    const totalConsumed = await EventMessageLogs.countDocuments({ type: EventTypeEnum.CONSUMER }).lean().exec();
    const failedProduced = await EventMessageLogs.countDocuments({ type: EventTypeEnum.PRODUCER, succeeded: false }).lean().exec();
    const failedConsumed = await EventMessageLogs.countDocuments({ type: EventTypeEnum.CONSUMER, succeeded: false }).lean().exec();

    return {
        "Total Produced": totalProduced || 0,
        "Total Consumed": totalConsumed || 0,
        "Failed Produced": failedProduced || 0,
        "Failed Consumed": failedConsumed || 0
    };
};
