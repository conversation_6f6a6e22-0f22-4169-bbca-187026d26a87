import { User } from "../models/user.model";
import { Policy } from "../models/policy.model";
import { Action } from "../models/action.model";
import { IUserPermissions } from "../interfaces";

export const getUserPermissions = async (userId: string) => {
    const permissions: IUserPermissions = {
        superAdminAssigned: false,
        global: [],
        application: {
            incidents: [],
            resiliences: []
        }
    };

    // Fetch user, if user not found or not enabled then return blank permissions
    const user = await User.findById(userId).select("policyIds applicationPolicyIds isEnabled").lean().exec();
    if (!user || !user.isEnabled) {
        return permissions;
    }

    // Check whether the super admin is assigned or not
    permissions.superAdminAssigned = !!user.policyIds?.map(String).includes(process.env.SUPER_ADMIN_POLICY_ID || "");

    // Step 3: If any global policies assigned then fetch actions of that policies
    if(user.policyIds && user.policyIds.length) {
        const policies = await Policy.find({ _id: { $in: user.policyIds }, isEnabled: true, type: "Global" }, { actionIds: 1, _id: 0 }).lean().exec();

        if (policies && policies.length) {
            let actionIds: string[] = [];
            policies.forEach(policy => actionIds = [...actionIds, ...policy.actionIds]);
            const actions = await Action.find({ _id: { $in: actionIds } }, { name: 1, _id: 0 }).lean().exec();
            const actionsResp = new Set(
                actions.map(action => action.name)
            );
            permissions.global = [...actionsResp];
        }
    }

    // Step 4: If user has any application policies assigned then fetch actions of that
    if(user.applicationPolicyIds && user.applicationPolicyIds.length) {
        // Fetch policyIds of application policy assigned
        const policyIds: string[] = user.applicationPolicyIds.map(policy => String(policy.policyId));

        if(policyIds.length) {
            // Fetch details of policies
            const policiyActionsMap = new Map();
            const policies = await Policy.find({ _id: { $in: policyIds }, isEnabled: true, type: "Application" }, { actionIds: 1, _id: 1 }).lean().exec();

            if (policies && policies.length) {
                // Loop through all policies and fetch actions of that policy and create map of policy and actions
                for(const policy of policies) {
                    // eslint-disable-next-line no-await-in-loop
                    const actions = await Action.find({ _id: { $in: policy.actionIds } }, { name: 1, _id: 0 }).lean().exec();
                    const actionNames = actions.map(action => action.name);
                    policiyActionsMap.set(String(policy._id), actionNames);
                }

                const applicationPolicyMap: Map<string, {type: string, actions: string[]}> = new Map();
                // Loop through all application policyIds
                user.applicationPolicyIds.forEach(policy => {
                    // Fetch actions of that policy
                    const actions = policiyActionsMap.get(String(policy.policyId));
                    if(actions && actions.length) {
                        // Add entry in the applicationPolicyMap
                        const applicationPolicy = applicationPolicyMap.get(String(policy.applicationId));
                        if(applicationPolicy) {
                            applicationPolicyMap.set(String(policy.applicationId), {
                                type: policy.applicationType,
                                actions: [...applicationPolicy.actions, ...actions]
                            });
                        } else {
                            applicationPolicyMap.set(String(policy.applicationId), {
                                type: policy.applicationType,
                                actions
                            });
                        }
                    }
                });

                // Loop through applicationPolicyMap and add actions in permissions object
                for (const [id, value] of applicationPolicyMap.entries()) {
                    const actions = Array.from(new Set(value.actions));

                    // eslint-disable-next-line max-depth
                    if(value.type === "Incident") {
                        permissions.application.incidents.push({
                            id,
                            actions
                        });
                    } else if(value.type === "Resilience") {
                        permissions.application.resiliences.push({
                            id,
                            actions
                        });
                    }
                }
            }
        }
    }

    return permissions;
};
