import { BlobServiceClient, BlockBlobClient } from "@azure/storage-blob";
import { File, FileDoc } from "../models/files.model";
import { ResourceNotFoundError } from "../errors/resource-not-found";
import { NotFoundCode } from "./not-found-codes";
import { createZip } from "./create-zip";
import { exec } from "child_process";
import { promisify } from "util";
import { createFolderIfNotExist } from "./create-folder-if-not-exist";
import { DownloadFilesParams } from "../interfaces/download-files-params";

const execAsync = promisify(exec);

export const downloadFiles = async ({ parent, parentId, entity, entityId, fileIds }: DownloadFilesParams): Promise<{ filePath: string; fileName: string; }> => {
    try {
        const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING;
        if (!AZURE_STORAGE_CONNECTION_STRING) {
            throw Error("Cannot download.");
        }

        const files: FileDoc[] = [];
        const fileBlobClients: { filePath: string, blobClient: BlockBlobClient }[] = [];
        const fileDetails = await File.find({ _id: { $in: fileIds }, parent, parentId, entity, entityId }).lean().exec();
        fileIds.forEach(fileId => {
            const fileDetail = fileDetails.find(detail => String(detail._id) === String(fileId));
            if (fileDetail) {
                files.push(fileDetail as FileDoc);
            }
        });

        if (!files.length) {
            throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "Files not found.");
        }
        const parentName = files[0].parent;
        const entityName = `${files[0].entity}-${Date.now()}`;
        const zipFileName = `m5p-${entityName}-${Date.now()}.zip`;
        const zipFilePath = `/uploads/zip/${zipFileName}`;

        // Create folder if not exist
        await createFolderIfNotExist(`/uploads/zip`);
        await createFolderIfNotExist(`/uploads/${entityName}`);

        const blobServiceClient = BlobServiceClient.fromConnectionString(
            AZURE_STORAGE_CONNECTION_STRING
        );
        const containerClient = blobServiceClient.getContainerClient(parentName);
        await containerClient.createIfNotExists();

        files.forEach(file => {
            const filePath = `/uploads/${entityName}/${file.originalFileName}`;
            const blockBlobClient = containerClient.getBlockBlobClient(file.fileName);
            fileBlobClients.push({
                filePath,
                blobClient: blockBlobClient
            });
        });

        // Download files parellely
        await Promise.all(fileBlobClients.map(async fileBlobClient => await fileBlobClient.blobClient.downloadToFile(fileBlobClient.filePath)));
        // Create zip using downloaded files
        await createZip(`/uploads/${entityName}`, zipFilePath);
        // Delete downloaded files
        execAsync(`rm -rf /uploads/${entityName}`);

        return { filePath: zipFilePath, fileName: zipFileName };
    } catch (error) {
        console.error("Common.Util.DownloadFiles");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
