import { typeAllowedFiltersMapping } from "./type-allowed-filters-mapping";
import { ValueTypesEnum } from "../enums/value-types.enum";
import { FilterFields } from "../interfaces";
import { filterOperatorsLabelValue } from "./filter-operators-label-value";
import { FilterOperators } from "../enums/filter-operators";

export const fetchSectionFilterFields = (path: any) => {
    const fields :FilterFields[] = [];

    if(Array.isArray(path)) {
        // Loop through all fields of that section
        path.forEach((field: any) => {
            if(field.filterable && field.type) {
                const isFlexibleField = Boolean(field.flexibleField);
                fields.push({
                    name: field.name,
                    displayName: field.displayName,
                    isFlexibleField: isFlexibleField,
                    key: isFlexibleField ? field.key : null,
                    // As of now, we will allow between filter for selected date fields,
                    // once we will allow this operator for every date field then we should remove this condition and modify typeAllowedFiltersMapping object.
                    allowedFilters: (field.type === ValueTypesEnum.DATE && field.isBetweenFilterAllowed)
                        ? [...(typeAllowedFiltersMapping[field.type as ValueTypesEnum] ?? []), filterOperatorsLabelValue[FilterOperators.BETWEEN]]
                        : (typeAllowedFiltersMapping[field.type as ValueTypesEnum] ?? []),
                    type: field.type,
                    assetStatusField: field.assetStatusField,
                    ownerField: field.ownerField,
                    milestoneField: field.milestoneField,
                    categoryType: field.categoryType,
                    ignoreSanitize: field.ignoreSanitize,
                    trackerObjectives: field.trackerObjectives,
                    trackerClients: field.trackerClients,
                    organizationFlexibleField: field.organizationFlexibleField,
                    isQuickFilter: field.isQuickFilter,
                    isBooleanField: field.isBooleanField,
                    moxIds: field.moxIds,
                    engagedByMoxfive: field.engagedByMoxfive
                });
            }
        });
    }
    return fields;
};
