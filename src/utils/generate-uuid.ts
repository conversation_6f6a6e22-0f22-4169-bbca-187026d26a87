const getRandomKeys = (length = 0, timeChunk = ""): string => {
    let result = "";
    const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
    const charactersLength = characters.length;
    for (let i = 0; i < length - timeChunk.length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result + timeChunk;
};

export const generateUUID = (): string => {
    const keysSequence = [8, 4, 4, 4, 12];
    const currentTime = Date.now().toString();
    const timeChunks = [
        currentTime.substring(0, 4),
        currentTime.substring(4, 6),
        currentTime.substring(6, 8),
        currentTime.substring(8, 10),
        currentTime.substring(10, 13)
    ];
    const keysToJoin: string[] = [];
    keysSequence.forEach((numberOfKeys, i) => {
        // eslint-disable-next-line security/detect-object-injection
        keysToJoin.push(getRandomKeys(numberOfKeys, timeChunks[i]));
    });
    return keysToJoin.join("-");
};
