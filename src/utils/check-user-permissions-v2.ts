/* eslint-disable no-param-reassign */
import { IUserPermissions } from "../interfaces";
import { getUserPermissions } from "./get-user-permissions";
import { Tracker, TrackerDoc } from "../models/tracker.model";
import { IncidentV2, IncidentV2Doc } from "../models/incidentv2.model";

export const checkUserPermissionsV2 =  async ({ userId, permissions = null, incidentId, trackerId, actions, incident = null, tracker = null }: {
    userId: string,
    permissions?: IUserPermissions | null,
    incidentId?: string,
    incident?: IncidentV2Doc | null
    tracker?: TrackerDoc | null
    trackerId?: string,
    actions: string[]
}) => {
    // If there are no permissions passed then first fetch the permissions
    if(!permissions) {
        permissions = await getUserPermissions(userId);
    }

    let checkGlobalActions = true;

    // Prepare incidentId and trackerId
    incidentId = incident ? String(incident._id) : incidentId;
    trackerId = tracker ? String(tracker._id) : trackerId;

    const response: {
        permissions: IUserPermissions,
        assignedActions: string[]
    } = {
        permissions,
        assignedActions: []
    };

    if(incidentId) {
        // If incident is not passed then fetch the incident
        if(!incident) {
            incident = await IncidentV2.findById(incidentId).lean().exec();
            if(!incident) {
                return response;
            }
        }

        // Check whether user has super admin assigned or user is member of that tracker then only continue
        if(!permissions.superAdminAssigned && !incident.members.map(String).includes(userId)) {
            return response;
        }

        // Check if that incident has separate permissions, if true then check actions from that list otherwise from global action list
        const incidentActionsRecord = permissions.application.incidents.find(record => String(record.id) === (incidentId));
        if(incidentActionsRecord) {
            response.assignedActions = actions.filter(action => incidentActionsRecord.actions.includes(action));
        } else {
            checkGlobalActions = true;
        }
    } else if(trackerId) {
        // If tracker is not passed then fetch the tracker
        if(!tracker) {
            tracker = await Tracker.findById(trackerId).lean().exec();
            if(!tracker) {
                return response;
            }
        }

        // Check whether user has super admin assigned or user is member of that tracker then only continue
        if(!permissions.superAdminAssigned && !tracker.members.map(String).includes(userId)) {
            return response;
        }

        // Check if that tracker has separate permissions, if true then check actions from that list otherwise from global action list
        const trackerActionsRecord = permissions.application.resiliences.find(record => String(record.id) === (incidentId));
        if(trackerActionsRecord) {
            response.assignedActions = actions.filter(action => trackerActionsRecord.actions.includes(action));
        } else {
            checkGlobalActions = true;
        }
    }

    // If we need to check it from the global action list and check from it
    if(checkGlobalActions) {
        response.assignedActions = actions.filter(action => permissions?.global.includes(action));
    }

    return response;
};
