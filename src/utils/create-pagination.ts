import { Request } from "express";
import { PageAtt<PERSON>, Page } from "../models/page.model";
import { PaginationAttrs, Pagination } from "../models/pagination.model";
import { PaginationEntity } from "../enums/pagination-entity";
import { PageResponseObj } from "../interfaces/page-response-obj";
import { FilterFields } from "../interfaces/filter-fields";

export const createPagination = async (req: Request,
    entity: PaginationEntity,
    facets: FilterFields[],
    appliedFilters: { [key: string]: any },
    data: any[],
    additionalAttributes?: { [key: string]: any }): Promise<PageResponseObj | null> => {
    try {
        const userId = req.currentUser?.id;
        if(!userId) {
            return null;
        }
        const rowsPerPage = req.query.limit && !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 100;
        const totalCount = data.length;
        const totalPages = Math.ceil(totalCount / rowsPerPage);
        const pages: PageAttrs[] = [];
        if([0, 1].includes(totalPages)) {
            return {
                data,
                meta: {
                    totalCount,
                    rowsPerPage,
                    totalPages,
                    currentPage: 1,
                    firstPageLink: "",
                    lastPageLink: "",
                    previousPageLink: "",
                    nextPageLink: "",
                    facets,
                    appliedFilters,
                    additionalAttributes: additionalAttributes ?? {}
                }
            };
        }
        for (let i = 1; i <= totalPages; i++) {
            const pageData = data.slice((i - 1) * rowsPerPage, i * rowsPerPage);
            const page = { rows: JSON.stringify(pageData), count: pageData.length };
            pages.push(page);
        }
        const pagesResult = await Page.insertMany(pages, { ordered: true, rawResult: true });
        const pagination: PaginationAttrs = {
            entity,
            totalCount,
            rowsPerPage,
            totalPages,
            facets: JSON.stringify(facets),
            appliedFilters: JSON.stringify(appliedFilters),
            additionalAttributes: JSON.stringify(additionalAttributes ?? {}),
            pageLinks: [],
            createdBy: userId
        };
        for(const key in pagesResult.insertedIds) {
            if(pagesResult.insertedIds.hasOwnProperty(key) && !isNaN(Number(key))) {
                // eslint-disable-next-line security/detect-object-injection
                pagination.pageLinks[Number(key)] = `${req.protocol}://${req.get("host")}${req.path}?skipToken=${pagesResult.insertedIds[key]}`;
            }
        }
        const paginationDoc = Pagination.build(pagination);
        await paginationDoc.save();
        return {
            data: data.slice(0, rowsPerPage),
            meta: {
                totalCount,
                rowsPerPage,
                totalPages,
                currentPage: 1,
                firstPageLink: pagination.pageLinks[0],
                lastPageLink: pagination.pageLinks[totalPages - 1],
                previousPageLink: "",
                nextPageLink: pagination.pageLinks[1],
                facets,
                appliedFilters,
                additionalAttributes: additionalAttributes ?? {}
            }
        };
    } catch (err) {
        console.error("Error in createPagination", err);
        return null;
    }
};
