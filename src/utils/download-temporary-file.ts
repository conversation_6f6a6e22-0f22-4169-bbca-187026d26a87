import { BlobServiceClient } from "@azure/storage-blob";
import { ResourceNotFoundError } from "../errors/resource-not-found";
import { NotFoundCode } from "./not-found-codes";
import { TemporaryFile, TemporaryFileDoc } from "../models/temporary-files.model";

export const downloadTemporaryFile = async ({ parent, fileId, AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING }: {
    parent: string, fileId: string, AZURE_STORAGE_CONNECTION_STRING? : string
}): Promise<{ filePath: string; file: TemporaryFileDoc; }> => {
    try {
        if (!AZURE_STORAGE_CONNECTION_STRING) {
            throw Error("Cannot download.");
        }

        const file = await TemporaryFile.findOne({ _id: fileId, parent });
        if (!file) {
            throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "File not found.");
        }

        const blobServiceClient = BlobServiceClient.fromConnectionString(
            AZURE_STORAGE_CONNECTION_STRING
        );
        const filePath = `/uploads/${Date.now()}-${file.fileName}`;
        const containerClient = blobServiceClient.getContainerClient(file.parent);
        await containerClient.createIfNotExists();
        const blockBlobClient = containerClient.getBlockBlobClient(file.fileName);
        await blockBlobClient.downloadToFile(filePath);
        return { filePath, file };
    } catch (error) {
        console.error("Common.Util.DownloadFile");
        console.error(error);
        throw Error("Something went wrong.");
    }
};
