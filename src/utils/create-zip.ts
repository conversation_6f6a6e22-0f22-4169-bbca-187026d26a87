import archiver from "archiver";
import fs from "fs";

export const createZip = (sourceDir: string, outPath: string) => {
    const archive = archiver("zip", { zlib: { level: 9 } });
    // eslint-disable-next-line security/detect-non-literal-fs-filename
    const stream = fs.createWriteStream(outPath);

    return new Promise((resolve, reject) => {
        archive
            .directory(sourceDir, false)
            .on("error", err => reject(err))
            .pipe(stream);

        stream.on("close", () => resolve(true));
        archive.finalize();
    });
};
