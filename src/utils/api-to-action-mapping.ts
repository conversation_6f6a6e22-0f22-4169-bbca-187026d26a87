export const APIToActionMapping: {
    method: "GET" | "POST" | "PUT" | "DELETE",
    route: string,
    action: string,
}[] = [
    {
        method: "GET",
        route: "/v1/agreements/:agreementId",
        action: "GetAgreementDetails"
    },
    {
        method: "GET",
        route: "/v1/agreements/mine",
        action: "ListSignedAgreementsByUser"
    },
    {
        method: "POST",
        route: "/v1/agreements/sign",
        action: "SignAgreement"
    },
    {
        method: "GET",
        route: "/v1/am/summary",
        action: "GetAccessManagementSummary"
    },
    {
        method: "POST",
        route: "/v1/organizations/fields",
        action: "AddOrganizationFlexibleField"
    },
    {
        method: "POST",
        route: "/v1/organizations/:organizationId/members",
        action: "AddMemberToOrganization"
    },
    {
        method: "POST",
        route: "/v1/organizations/:organizationId/members/emails",
        action: "AddMemberToOrganizationUsingEmail"
    },
    {
        method: "POST",
        route: "/v1/users/fields",
        action: "AddUserFlexibleField"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/changeMembership",
        action: "ChangeOrganizationMembership"
    },
    {
        method: "POST",
        route: "/v1/organizations",
        action: "CreateOrganization"
    },
    {
        method: "GET",
        route: "/v1/organizations/facets",
        action: "ListOrganizationFacets"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/users/facets",
        action: "ListOrganizationUserFacets"
    },
    {
        method: "GET",
        route: "/v1/users/facets",
        action: "ListUserFacets"
    },
    {
        method: "GET",
        route: "/v1/users/ad",
        action: "GetUsersOfActiveDirectory"
    },
    {
        method: "GET",
        route: "/v1/organizations",
        action: "ListOrganizations"
    },
    {
        method: "GET",
        route: "/v1/users",
        action: "ListUsers"
    },
    {
        method: "GET",
        route: "/v1/location/:searchType",
        action: "GetLocations"
    },
    {
        method: "POST",
        route: "/v1/users/membersOf",
        action: "GetUserMembershipDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/fields",
        action: "ListOrganizationFlexibleFields"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/users/:userId",
        action: "GetOrganizationUserDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/types",
        action: "ListOrganizationTypes"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/users",
        action: "ListOrganizationUsers"
    },
    {
        method: "GET",
        route: "/v1/authentication/dashboards/widgets/:widgetId/organizationsByTypes",
        action: "WidgetOrganizationsByTypesDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId",
        action: "GetOrganizationDetail"
    },
    {
        method: "GET",
        route: "/v1/users/:userId",
        action: "GetUserDetail"
    },
    {
        method: "GET",
        route: "/v1/users/fields",
        action: "ListUserFlexibleFields"
    },
    {
        method: "GET",
        route: "/v1/users/me/permissions",
        action: "GetUserPermissions"
    },
    {
        method: "GET",
        route: "/v1/users/me",
        action: "GetUserProfile"
    },
    {
        method: "POST",
        route: "/v1/users/invite",
        action: "InviteUser"
    },
    {
        method: "GET",
        route: "/v1/logout",
        action: "LogOut"
    },
    {
        method: "GET",
        route: "/v1/login",
        action: "LogIn"
    },
    {
        method: "POST",
        route: "/v1/authentication/groupupdate/callback",
        action: "AzureGroupsUpdatesWebhook"
    },
    {
        method: "GET",
        route: "/v1/redirect",
        action: "AzureAppWebRedirectURI"
    },
    {
        method: "GET",
        route: "/v1/refreshToken",
        action: "RefreshToken"
    },
    {
        method: "DELETE",
        route: "/v1/organizations/:organizationId/members",
        action: "RemoveMembersOfOrganization"
    },
    {
        method: "DELETE",
        route: "/v1/organizations/:organizationId/owners",
        action: "RemoveOwnersOfOrganization"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId",
        action: "UpdateOrganization"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/users/:userId",
        action: "UpdateUserProfileOfAnOrganization"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/users/:userId/email",
        action: "UpdateUserEmailOfAnOrganization"
    },
    {
        method: "PUT",
        route: "/v1/organizations/status",
        action: "UpdateOrganizationStatus"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/users/status",
        action: "UpdateUsersStatusOfAnOrganization"
    },
    {
        method: "PUT",
        route: "/v1/users/:userId",
        action: "UpdateUserDetails"
    },
    {
        method: "PUT",
        route: "/v1/users/:userId/email",
        action: "UpdateUserEmail"
    },
    {
        method: "PUT",
        route: "/v1/users/status",
        action: "UpdateUsersStatus"
    },
    {
        method: "POST",
        route: "/v1/authentication/userupdate/callback",
        action: "AzureUsersUpdatesWebhook"
    },
    {
        method: "POST",
        route: "/v1/verifyCode",
        action: "GetAccessToken"
    },
    {
        method: "PUT",
        route: "/v1/authorization/entities/:entityId/policies/attach",
        action: "AttachPoliciesToPlatformEntity"
    },
    {
        method: "PUT",
        route: "/v1/authorization/policies/:policyId/entities/attach",
        action: "AttachPolicyToPlatformEntities"
    },
    {
        method: "PUT",
        route: "/v1/authorization/policies/:policyId/incidents/:incidentId/users/attach",
        action: "AttachPolicyToIncidentUsers"
    },
    {
        method: "PUT",
        route: "/v1/authorization/policies/:policyId/trackers/:trackerId/users/attach",
        action: "AttachPolicyToResilienceUsers"
    },
    {
        method: "POST",
        route: "/v1/authorization/policies",
        action: "CreatePolicy"
    },
    {
        method: "DELETE",
        route: "/v1/authorization/policies/:policyId",
        action: "DeletePolicy"
    },
    {
        method: "PUT",
        route: "/v1/authorization/entities/:entityId/policies/detach",
        action: "DetachPoliciesFromPlatformEntity"
    },
    {
        method: "PUT",
        route: "/v1/authorization/policies/:policyId/entities/detach",
        action: "DetachPolicyFromPlatformEntity"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/:policyId/entities/:policyType/facets",
        action: "ListPlatformEntitiesForPolicyFacets"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/facets",
        action: "ListPolicyFacets"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/services/:serviceId/actions",
        action: "ListServiceActions"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/:policyId/services/:serviceId/actions",
        action: "ListServiceActionsForPolicy"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies",
        action: "ListPolicies"
    },
    {
        method: "GET",
        route: "/v1/authorization/entities/:entityId/policies",
        action: "ListPoliciesForPlatformEntity"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/:policyId",
        action: "GetPolicyDetail"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/services",
        action: "ListServices"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/:policyId/services",
        action: "ListServicesForPolicy"
    },
    {
        method: "GET",
        route: "/v1/authorization/policies/:policyId/entities",
        action: "ListPlatformEntitiesForPolicy"
    },
    {
        method: "PUT",
        route: "/v1/authorization/policies/toggle",
        action: "UpdatePoliciesStatus"
    },
    {
        method: "PUT",
        route: "/v1/authorization/policies/:policyId",
        action: "UpdatePolicy"
    },
    {
        method: "PUT",
        route: "/v1.1/authorization/policies/incidents/:incidentId/users/attach",
        action: "AttachPolicyToIncidentUsers"
    },
    {
        method: "PUT",
        route: "/v1.1/authorization/policies/trackers/:trackerId/users/attach",
        action: "AttachPolicyToResilienceUsers"
    },
    {
        method: "PUT",
        route: "/v1/dashboards/:dashboardId/widgets",
        action: "AddDashboardWidgets"
    },
    {
        method: "PUT",
        route: "/v1/dashboards/projects/:projectId/widgets",
        action: "AddProjectDashboardWidgets"
    },
    {
        method: "GET",
        route: "/v1/dashboard",
        action: "GetPlatformDashboardDetails"
    },
    {
        method: "GET",
        route: "/v1/dashboards/projects/:projectId",
        action: "GetProjectDashboardDetails"
    },
    {
        method: "GET",
        route: "/v1/dashboard/widgets",
        action: "ListDashboardWidgets"
    },
    {
        method: "GET",
        route: "/v1/dashboards/projects/:projectId/widgets",
        action: "ListProjectDashboardWidgets"
    },
    {
        method: "DELETE",
        route: "/v1/dashboards/:dashboardId/widgets/:widgetId",
        action: "RemoveDashboardWidget"
    },
    {
        method: "DELETE",
        route: "/v1/dashboards/projects/:projectId/widgets/:widgetId",
        action: "RemoveProjectDashboardWidget"
    },
    {
        method: "POST",
        route: "/v1/incidents/fields",
        action: "AddIncidentFlexibleField"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/assets/status",
        action: "AddAssetStatus"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/assets/:assetId/comments",
        action: "CreateAssetComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/:assetId/comments",
        action: "ListAssetComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/assets/:assetId/allComments/:commentId",
        action: "RemoveAssetAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/assets/:assetId/comments/:commentId",
        action: "RemoveAssetComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/allComments/:commentId",
        action: "UpdateAssetAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/comments/:commentId",
        action: "UpdateAssetComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/assets",
        action: "CreateAsset"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/export",
        action: "ExportAssets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/facets",
        action: "ListAssetFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/:assetId/dataPreservation",
        action: "GetAssetDataPreservationDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/:assetId/dates",
        action: "GetAssetDatesDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/:assetId/forensics",
        action: "GetAssetForensicDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/:assetId/host",
        action: "GetAssetHostDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/:assetId/recovery",
        action: "GetAssetRecoveryDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/statusLibrary",
        action: "GetAssetStatusLibrary"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/assets/import/csv",
        action: "ImportAssets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets/statuses",
        action: "ListAssetStatuses"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/assets",
        action: "ListAssets"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/assets/:assetId",
        action: "RemoveAsset"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/assets/status/:statusId",
        action: "RemoveAssetStatusFromLibrary"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets",
        action: "RemoveAssets"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/dataPreservation",
        action: "UpdateAssetDataPreservationDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/dates",
        action: "UpdateAssetDatesDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/forensics/compromisedStatus",
        action: "UpdateAssetForensicsCompromisedStatus"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/forensics",
        action: "UpdateAssetForensicsDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/host",
        action: "UpdateAssetHostDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/recovery",
        action: "UpdateAssetRecoveryDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/:assetId/recovery/status",
        action: "UpdateAssetRecoveryStatus"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/status/:statusId",
        action: "UpdateAssetStatusOfLibrary"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets/statuses",
        action: "UpdateAssetStatuses"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/assets-attribute",
        action: "UpdateAssets"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/comments",
        action: "CreateInvoiceComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/comments",
        action: "ListInvoiceComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/allComments/:commentId",
        action: "RemoveInvoiceAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/comments/:commentId",
        action: "RemoveInvoiceComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/allComments/:commentId",
        action: "UpdateInvoiceAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/comments/:commentId",
        action: "UpdateInvoiceComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/invoices",
        action: "CreateInvoice"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/export",
        action: "ExportClaimInvoices"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/facets",
        action: "ListClaimInvoiceFacets"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/file/:fileId",
        action: "RemoveClaimInvoiceAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/file/:fileId/download",
        action: "DownloadClaimInvoiceAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/download/files",
        action: "DownloadClaimInvoiceAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/files",
        action: "ListClaimInvoiceAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/upload/files",
        action: "UploadClaimInvoiceAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/carrierUpdates",
        action: "GetClaimInvoiceCarrierUpdatesDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/general",
        action: "GetClaimInvoiceGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/moxfiveAnalysis",
        action: "GetClaimInvoiceMOXFIVEAnalysisDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/status",
        action: "GetClaimInvoiceStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/invoices",
        action: "ListClaimInvoices"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices",
        action: "RemoveClaimInvoices"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/carrierUpdates",
        action: "UpdateClaimInvoiceCarrierUpdatesDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/general",
        action: "UpdateClaimInvoiceGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/moxfiveAnalysis",
        action: "UpdateClaimInvoiceMOXFIVEAnalysisDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/invoices/:invoiceId/status",
        action: "UpdateClaimInvoiceStatusDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments",
        action: "CreateRFIComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments",
        action: "ListRFIComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/allComments/:commentId",
        action: "RemoveRFIAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments/:commentId",
        action: "RemoveRFIComment"
    }, {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/allComments/:commentId",
        action: "UpdateRFIAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments/:commentId",
        action: "UpdateRFIComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/requestForInformations",
        action: "CreateRFI"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/export",
        action: "ExportRequestForInformations"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/facets",
        action: "ListClaimRequestForInformationFacets"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/file/:fileId",
        action: "RemoveClaimRFIAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/file/:fileId/download",
        action: "DownloadClaimRFIAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/download/files",
        action: "DownloadClaimRFIAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/files",
        action: "ListClaimRFIAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/upload/files",
        action: "UploadClaimRFIAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/general",
        action: "GetClaimRequestForInformationGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/insuredResponse",
        action: "GetClaimRequestForInformationInsuredResponseDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/requestForInformations",
        action: "ListClaimRequestForInformations"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/requestForInformations",
        action: "RemoveClaimRequestForInformation"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/general",
        action: "UpdateClaimRequestForInformationGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/insuredResponse",
        action: "UpdateClaimRequestForInformationInsuredResponseDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/comments",
        action: "CreateSupplementalComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/comments",
        action: "ListSupplementalComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/allComments/:commentId",
        action: "RemoveSupplementalAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/comments/:commentId",
        action: "RemoveSupplementalComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/allComments/:commentId",
        action: "UpdateSupplementalAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/comments/:commentId",
        action: "UpdateSupplementalComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/supplementals",
        action: "CreateSupplementalData"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/export",
        action: "ExportClaimSupplementals"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/facets",
        action: "ListClaimSupplementalFacets"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/file/:fileId",
        action: "RemoveClaimSupplementalDataAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/file/:fileId/download",
        action: "DownloadClaimSupplementalDataAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/download/files",
        action: "DownloadClaimSupplementalDataAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/files",
        action: "ListClaimSupplementalDataAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/upload/files",
        action: "UploadClaimSupplementalDataAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/analysis",
        action: "GetClaimSupplementalAnalysisDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/general",
        action: "GetClaimSupplementalGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/supplementals",
        action: "ListClaimSupplementals"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/supplementals",
        action: "RemoveClaimSupplementals"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/analysis",
        action: "UpdateClaimSupplementalAnalysisDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/supplementals/:supplementalId/general",
        action: "UpdateClaimSupplementalGeneralDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/comments",
        action: "CreateTimelineComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/comments",
        action: "ListTimelineComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/allComments/:commentId",
        action: "RemoveTimelineAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/comments/:commentId",
        action: "RemoveTimelineComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/allComments/:commentId",
        action: "UpdateTimelineAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/comments/:commentId",
        action: "UpdateTimelineComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/timeline",
        action: "CreateTimeline"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId",
        action: "RemoveClaimTimeline"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/timeline/export",
        action: "ExportClaimTimelineEntries"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/timeline/facets",
        action: "ListClaimTimelineFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/general",
        action: "GetClaimTimelineGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/vendorDetails",
        action: "GetClaimTimelineVendorDetailsDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/claims/timeline/import/csv",
        action: "ImportClaimTimelineEntries"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/claims/timeline",
        action: "ListClaimTimelines"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/general",
        action: "UpdateClaimTimelineGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/claims/timeline/:timelineId/vendorDetails",
        action: "UpdateClaimTimelineVendorDetails"
    },
    {
        method: "POST",
        route: "/v1/incidents",
        action: "CreateIncidentOpportunity"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/carrierUpdates",
        action: "WidgetCarrierUpdatesDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/totalClaimCostbyCategory",
        action: "WidgetTotalClaimCostsByCategory"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/totalClaimCostbyVendor",
        action: "WidgetTotalClaimCostsByVendorDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/compromisedSystems",
        action: "WidgetCompromisedSystemsDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/compromisedSystemsbyStatus",
        action: "WidgetCompromisedSystemsByStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/incidentEconomics",
        action: "WidgetIncidentEconomicsDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/incidentProgress",
        action: "WidgetIncidentProgressDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/invoicesbyStatus",
        action: "WidgetInvoicesByStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/invoicesStatusbyVendor",
        action: "WidgetInvoicesStatusByVendorDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/milestonesbyCategory",
        action: "WidgetMilestonesByCategoryDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/milestonesbyProgress",
        action: "WidgetMilestonesByProgressDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/moxfiveAnalysis",
        action: "WidgetMOXFIVEAnalysisDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/systembyRecoveryStage",
        action: "WidgetSystemsByRecoveryStageDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/systemsGroupedbyApplication",
        action: "WidgetSystemsGroupedByApplicationDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/systemsGroupedbyTier",
        action: "WidgetSystemsGroupedByTierDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/tasksbyOrganizations",
        action: "WidgetTasksByOrganizationDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/tasksbyUsers",
        action: "WidgetTasksByUsersDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/threatActorNegotiations",
        action: "WidgetThreatActorNegotiationDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/workstreamsbyProgress",
        action: "WidgetWorkstreamsByProgressDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/workstreamsFinancials",
        action: "WidgetIncidentsByStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/projects/:projectId/widgets/:widgetId/workstreamsBudgetUtilization",
        action: "WidgetWorkstreamsBudgetUtilization"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId",
        action: "RemoveIncident"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/insurance/general",
        action: "DetachInsuranceCarrier"
    },
    {
        method: "GET",
        route: "/v1/incidents/opportunities/export",
        action: "ExportIncidentOpportunities"
    },
    {
        method: "GET",
        route: "/v1/incidents/export",
        action: "ExportIncidents"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/export",
        action: "ExportIncidentsForOrganization"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/users/:userId/export",
        action: "ExportIncidentsForOrganizationUser"
    },
    {
        method: "GET",
        route: "/v1/incidents/facets",
        action: "ListIncidentFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/draftOrPartial/facets",
        action: "ListIncidentFacets"
    },
    {
        method: "POST",
        route: "/v1/incidents/views/focus",
        action: "CreateFocusView"
    },
    {
        method: "GET",
        route: "/v1/incidents/views/focus/active",
        action: "GetActiveFocusView"
    },
    {
        method: "GET",
        route: "/v1/incidents/views/focus",
        action: "ListFocusViews"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/views/focus/:focusViewId",
        action: "RemoveFocusView"
    },
    {
        method: "PUT",
        route: "/v1/incidents/views/focus/toggle",
        action: "ToggleFocusView"
    },
    {
        method: "PUT",
        route: "/v1/incidents/views/focus/:focusViewId",
        action: "UpdateFocusView"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/widgets/:widgetId/incidentsbyStatus",
        action: "WidgetIncidentsByStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/companyBackground",
        action: "GetCompanyBackground"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/overview/general/economics",
        action: "GetEconomicsOverview"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/overview/general/economics/workstreams",
        action: "GetEconomicsWorkStreams"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/environment/activeDirectory",
        action: "GetEnvironmentActiveDirectoryDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/environment/backups",
        action: "GetEnvironmentBackupsDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/environment/email",
        action: "GetEnvironmentEmailDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/environment/general",
        action: "GetEnvironmentGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/environment/solutions",
        action: "GetEnvironmentSolutionsDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/incident/businessEmailCompromise",
        action: "GetIncidentBusinessEmailCompromiseDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId",
        action: "GetIncidentDetails"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/incident/extortion",
        action: "GetIncidentExtortionDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/fields",
        action: "ListIncidentFlexibleFields"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/incident/general",
        action: "GetIncidentGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/modules",
        action: "GetIncidentModules"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/incident/ransomware",
        action: "GetIncidentRansomewareDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/insurance/claim",
        action: "GetInsuranceClaimDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/insurance/general",
        action: "GetInsuranceGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/insurance/policy",
        action: "GetInsurancePolicyDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/fields/byKeys",
        action: "ListIncidentFlexibleFieldsByKeys"
    },
    {
        method: "GET",
        route: "/v1/incidents/dashboards/widgets/:widgetId/myTasksbyIncident",
        action: "WidgetTasksByIncidentDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/ioc/:iocId/comments",
        action: "CreateIOCComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/ioc/:iocId/comments",
        action: "ListIOCComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/ioc/:iocId/allComments/:commentId",
        action: "RemoveIOCAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/ioc/:iocId/comments/:commentId",
        action: "RemoveIOCComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/ioc/:iocId/allComments/:commentId",
        action: "UpdateIOCAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/ioc/:iocId/comments/:commentId",
        action: "UpdateIOCComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/ioc",
        action: "CreateIOC"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/ioc/export",
        action: "ExportIOC"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/ioc/facets",
        action: "ListIOCFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/ioc/:iocId/indicator",
        action: "GetIOCIndicatorDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/ioc/:iocId/remediationAndContainment",
        action: "GetIOCRemediationAndContainmentDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/ioc/import/csv",
        action: "ImportIOC"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/ioc",
        action: "ListIOC"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/ioc",
        action: "RemoveIOC"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/ioc/:iocId/indicator",
        action: "UpdateIOCIndicatorDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/ioc/:iocId/remediationAndContainment/actionsTaken",
        action: "UpdateIOCRemediationAndContainmentActionsTakenDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/ioc/:iocId/remediationAndContainment",
        action: "UpdateIOCRemediationAndContainmentDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/opportunities",
        action: "ListIncidentOpportunities"
    },
    {
        method: "GET",
        route: "/v1/incidents",
        action: "ListIncidents"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId",
        action: "ListIncidentsForOrganization"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/users/:userId",
        action: "ListIncidentsForOrganizationUser"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights",
        action: "CreateMOXFIVECarrierInsight"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights/export",
        action: "ExportMOXFIVECarrierInsights"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights/facets",
        action: "ListMOXFIVECarrierInsightFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/moxfiveInsights/overview/generate",
        action: "GenerateMOXFIVEInsightsOverview"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights/:carrierInsightId",
        action: "GetMOXFIVECarrierInsight"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/moxfiveInsights/overview",
        action: "GetMOXFIVEInsightOverview"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights",
        action: "ListMOXFIVECarrierInsights"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights",
        action: "RemoveMOXFIVECarrierInsights"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/moxfiveInsights/overview",
        action: "RemoveMOXFIVEInsightOverview"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/moxfiveInsights/moxfiveCarrierInsights/:carrierInsightId",
        action: "UpdateMOXFIVECarrierInsight"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/moxfiveInsights/overview",
        action: "UpdateMOXFIVEInsightOverview"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/moxfiveInsights/overview/status",
        action: "UpdateMOXFIVEInsightOverviewStatus"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments",
        action: "CreateMilestoneComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments",
        action: "ListMilestoneComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/allComments/:commentId",
        action: "RemoveMilestoneAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments/:commentId",
        action: "RemoveMilestoneComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/allComments/:commentId",
        action: "UpdateMilestoneAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments/:commentId",
        action: "UpdateMilestoneComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/milestones",
        action: "CreateMilestone"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/export",
        action: "ExportProjectMilestones"
    }, {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/facets",
        action: "ListProjectMilestoneFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/general",
        action: "GetProjectMilestoneGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/status",
        action: "GetProjectMilestoneStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/milestones",
        action: "ListProjectMilestones"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/milestones",
        action: "RemoveProjectMilestones"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/general",
        action: "UpdateProjectMilestoneGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/milestones/:milestoneId/status",
        action: "UpdateProjectMilestoneStatusDetails"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments",
        action: "CreateStatusUpdateComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments",
        action: "ListStatusUpdateComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/allComments/:commentId",
        action: "RemoveStatusUpdateAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments/:commentId",
        action: "RemoveStatusUpdateComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/allComments/:commentId",
        action: "UpdateStatusUpdateAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments/:commentId",
        action: "UpdateStatusUpdateComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates",
        action: "CreateStatusUpdate"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/inReview/export",
        action: "ExportInReviewProjectStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/pendingForReview/export",
        action: "ExportPendingForReviewProjectStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/published/export",
        action: "ExportPublishedProjectStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/facets",
        action: "ListProjectStatusUpdateFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/pendingForReview/count",
        action: "ListPendingForReviewProjectStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/general",
        action: "GetProjectStatusUpdateGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/status",
        action: "GetProjectStatusUpdateStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/inReview",
        action: "ListInReviewProjectStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/pendingForReview",
        action: "ListPendingForReviewProjectStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/published",
        action: "ListPublishedProjectStatusUpdates"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates",
        action: "RemoveProjectStatusUpdates"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/general",
        action: "UpdateProjectStatusUpdateGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/status",
        action: "UpdateProjectStatusUpdateStatusDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/statusUpdates/reviewStatus",
        action: "UpdatePendingForReviewStatusOfProject"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/comments",
        action: "CreateTaskComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/comments",
        action: "ListTaskComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/allComments/:commentId",
        action: "RemoveTaskAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/comments/:commentId",
        action: "RemoveTaskComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/allComments/:commentId",
        action: "UpdateTaskAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/comments/:commentId",
        action: "UpdateTaskComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/projectStatus/tasks",
        action: "CreateTask"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/export",
        action: "ExportProjectTasks"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/facets",
        action: "ListProjectTaskFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/general",
        action: "GetProjectTaskGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/status",
        action: "GetProjectTaskStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/projectStatus/tasks",
        action: "ListProjectTasks"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/tasks",
        action: "RemoveProjectTasks"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/general",
        action: "UpdateProjectTaskGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/projectStatus/tasks/:taskId/status",
        action: "UpdateProjectTaskStatusDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId",
        action: "PublishIncident"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/comments",
        action: "CreateIncidentTimelineComment"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/comments",
        action: "ListIncidentTimelineComments"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/allComments/:commentId",
        action: "RemoveIncidentTimelineAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/comments/:commentId",
        action: "RemoveIncidentTimelineComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/allComments/:commentId",
        action: "UpdateIncidentTimelineAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/comments/:commentId",
        action: "UpdateIncidentTimelineComment"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/timeline",
        action: "CreateIncidentTimelineEntry"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/timeline/:timelineId",
        action: "RemoveIncidentTimeline"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/export",
        action: "ExportIncidentTimelineEntries"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/facets",
        action: "ListTimelineFacets"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/file/:fileId",
        action: "RemoveTimelineNegotiationAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/file/:fileId/download",
        action: "DownloadTimelineNegotiationAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/download/files",
        action: "DownloadTimelineNegotiationAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/files",
        action: "ListTimelineNegotiationAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/upload/files",
        action: "UploadTimelineNegotiationAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/forensic",
        action: "GetTimelineForensicDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/general",
        action: "GetTimelineGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/negotiation",
        action: "GetTimelineNegotiationDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/timeline/import/csv",
        action: "ImportTimelineEntries"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/timeline",
        action: "ListTimelines"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/forensic",
        action: "UpdateTimelineForensicDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/general",
        action: "UpdateTimelineGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/timeline/:timelineId/negotiation",
        action: "UpdateTimelineNegotiationDetails"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/companyBackground",
        action: "UpdateCompanyBackgroundDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/environment/activeDirectory",
        action: "UpdateEnvironmentActiveDirectoryDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/environment/backups",
        action: "UpdateEnvironmentBackupsDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/environment/email",
        action: "UpdateEnvironmentEmailDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/environment/general",
        action: "UpdateEnvironmentGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/environment/solutions",
        action: "UpdateEnvironmentSolutionsDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/incident/businessEmailCompromise",
        action: "UpdateIncidentBusinessEmailCompromiseDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/incident/extortion",
        action: "UpdateIncidentExtortionDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/incident/general",
        action: "UpdateIncidentGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/modules",
        action: "UpdateIncidentModules"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/moxID",
        action: "PublishIncidentDeal"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/progressPercentage",
        action: "UpdateIncidentProgressPercentage"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/incident/ransomware",
        action: "UpdateIncidentRansomwareDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/status",
        action: "UpdateIncidentStatus"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/insurance/claim",
        action: "UpdateInsuranceClaimDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/insurance/general",
        action: "UpdateInsuranceGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/insurance/policy",
        action: "UpdateInsurancePolicyDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/users",
        action: "AddIncidentMember"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/users/facets",
        action: "ListIncidentUserFacets"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/users",
        action: "ListIncidentUsers"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/users",
        action: "RemoveIncidentUsers"
    },
    {
        method: "GET",
        route: "/v1.1/incidents/:incidentId/users",
        action: "ListIncidentUsers"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/opportunity/validate",
        action: "ValidateIncidentOpportunity"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/workstreamProgresses",
        action: "ListWorkstreamProgresses"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/workstreamProgresses",
        action: "UpdateWorkstreamProgresses"
    },
    {
        method: "POST",
        route: "/v1/resiliences/fields",
        action: "AddResilienceFlexibleField"
    },
    {
        method: "GET",
        route: "/v1/resiliences/dashboards/widgets/:widgetId/businessResilienceObjectives",
        action: "WidgetResilienceObjectivesByStatusDetail"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/membershiptype",
        action: "ListTrackersForOrganizationUser"
    },
    {
        method: "GET",
        route: "/v1/resiliences/fields",
        action: "ListResilienceRoadmapFlexibleFields"
    },
    {
        method: "POST",
        route: "/v1/resiliences/fields/byKeys",
        action: "ListResilienceRoadmapFlexibleFieldsByKeys"
    },
    {
        method: "POST",
        route: "/v1/resiliences/objectives",
        action: "CreateObjectiveInCatalog"
    },
    {
        method: "GET",
        route: "/v1/resiliences/objectives/export",
        action: "ExportCatalogObjectives"
    },
    {
        method: "GET",
        route: "/v1/resiliences/objectives/facets",
        action: "ListCatalogObjectiveFacets"
    },
    {
        method: "GET",
        route: "/v1/resiliences/objectives/:objectiveId",
        action: "GetCatalogObjectiveDetails"
    },
    {
        method: "GET",
        route: "/v1/resiliences/objectives",
        action: "ListCatalogObjectives"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/objectives/:objectiveId",
        action: "RemoveObjectiveFromCatalog"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/objectives/:objectiveId",
        action: "UpdateObjectiveDetails"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/comments",
        action: "CreateTrackerObjectiveStatusUpdateComment"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/comments",
        action: "ListTrackerObjectiveStatusUpdateComments"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/allComments/:commentId",
        action: "RemoveTrackerObjectiveStatusUpdateAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/comments/:commentId",
        action: "RemoveTrackerObjectiveStatusUpdateComment"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/allComments/:commentId",
        action: "UpdateTrackerObjectiveStatusUpdateAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/comments/:commentId",
        action: "UpdateTrackerObjectiveStatusUpdateComment"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates",
        action: "CreateTrackerObjectiveStatusUpdate"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/inReview/export",
        action: "ExportInReviewTrackerStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/pendingForReview/export",
        action: "ExportPendingForReviewTrackerStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/published/export",
        action: "ExportPublishedTrackerStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objective/:objectiveId/statusUpdates/published/export",
        action: "ExportPublishedTrackerStatusUpdatesForObjective"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/facets",
        action: "ListStatusUpdateFacets"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/objectives/facets",
        action: "ListStatusUpdatesForObjectiveFacets"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/pendingForReview/count",
        action: "ListTrackerObjectivePendingForReviewStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/general",
        action: "GetTrackerObjectiveStatusUpdateGeneralDetails"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/status",
        action: "GetTrackerObjectiveStatusUpdateStatusDetails"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/inReview",
        action: "ListTrackerObjectiveInReviewStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/pendingForReview",
        action: "ListTrackerObjectivePendingForReviewStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/published",
        action: "ListTrackerObjectivePublishedStatusUpdates"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objective/:objectiveId/statusUpdates/published",
        action: "ListTrackerObjectivePublishedStatusUpdatesForObjective"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates",
        action: "RemoveTrackerObjectiveStatusUpdates"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/reviewStatus",
        action: "UpdatePendingForReviewStatusOfTrackerStatusUpdate"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/general",
        action: "UpdateTrackerObjectiveStatusUpdateGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/statusUpdates/:statusUpdateId/status",
        action: "UpdateTrackerObjectiveStatusUpdateStatusDetails"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/objectives",
        action: "AddObjectiveInTracker"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/comments",
        action: "CreateTrackerObjectiveComment"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/comments",
        action: "ListTrackerObjectiveComments"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/allComments/:commentId",
        action: "RemoveTrackerObjectiveAnyComment"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/comments/:commentId",
        action: "RemoveTrackerObjectiveComment"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/allComments/:commentId",
        action: "UpdateTrackerObjectiveAnyComment"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/comments/:commentId",
        action: "UpdateTrackerObjectiveComment"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/objectives/tracker",
        action: "CreateObjectiveInTracker"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/export",
        action: "ExportTrackerObjectives"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/facets",
        action: "ListTrackerObjectiveFacets"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/general",
        action: "GetTrackerObjectiveGeneralDetails"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/moxfiveNotes",
        action: "GetTrackerObjectiveMOXFIVENotesDetails"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/projectDetails",
        action: "GetTrackerObjectiveProjectDetails"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/projectDetails/moxId",
        action: "GetTrackerObjectiveProjectMOXID"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives",
        action: "ListTrackerObjectives"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/objectives/request",
        action: "CreateCustomObjectiveRequest"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives",
        action: "RemoveObjectivesFromTracker"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/general",
        action: "UpdateTrackerObjectiveGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/moxfiveNotes",
        action: "UpdateTrackerObjectiveMOXFIVENotesDetails"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/projectDetails",
        action: "UpdateTrackerObjectiveProjectDetails"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/projectDetails/moxId",
        action: "UpdateTrackerObjectiveProjectMOXID"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers",
        action: "CreateTracker"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/export",
        action: "ExportTrackers"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/organizations/:organizationId/export",
        action: "ExportTrackersForOrganization"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/organizations/:organizationId/users/:userId/export",
        action: "ExportTrackersForOrganizationUser"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/facets",
        action: "ListTrackerFacets"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/timeline",
        action: "GetTrackerTimelineData"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/clients",
        action: "CreateTracker"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers",
        action: "ListTrackers"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/organizations/:organizationId",
        action: "ListTrackersForOrganization"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/organizations/:organizationId/users/:userId",
        action: "ListTrackersForOrganizationUser"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId",
        action: "RemoveTracker"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/users",
        action: "AddTrackerMember"
    },
    {
        method: "GET",
        route: "/v1/resiliences/tracker/:trackerId/users/facets",
        action: "ListTrackerMemberFacets"
    },
    {
        method: "GET",
        route: "/v1/resiliences/tracker/:trackerId/users",
        action: "ListTrackerMembers"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/tracker/:trackerId/users",
        action: "RemoveTrackerUsers"
    },
    {
        method: "GET",
        route: "/v1.1/resiliences/tracker/:trackerId/users",
        action: "ListTrackerMembers"
    },
    {
        method: "GET",
        route: "/v1/notifications/configuration",
        action: "ListNotificationConfiguration"
    },
    {
        method: "GET",
        route: "/v1/notifications/preferences",
        action: "ListNotificationPreference"
    },
    {
        method: "GET",
        route: "/v1/notifications/recurrenceTypes",
        action: "ListRecurrenceTypes"
    },
    {
        method: "GET",
        route: "/v1/notifications/customized/project/:projectId",
        action: "GetProjectNotificationPreferenceCustomizationStatus"
    },
    {
        method: "GET",
        route: "/v1/notifications/configuration/project/:projectId",
        action: "ListProjectNotificationConfiguration"
    },
    {
        method: "GET",
        route: "/v1/notifications/preferences/project/:projectId",
        action: "ListProjectNotificationPreference"
    },
    {
        method: "PUT",
        route: "/v1/notifications/configuration",
        action: "UpdateNotificationConfiguration"
    },
    {
        method: "PUT",
        route: "/v1/notifications/preferences",
        action: "UpdateNotificationPreference"
    },
    {
        method: "PUT",
        route: "/v1/notifications/:notificationId/schedule",
        action: "UpdateNotificationSchedule"
    },
    {
        method: "PUT",
        route: "/v1/notifications/customized/project/:projectId",
        action: "UpdateProjectNotificationPreference"
    },
    {
        method: "PUT",
        route: "/v1/notifications/configuration/project/:projectId",
        action: "UpdateProjectNotificationConfiguration"
    },
    {
        method: "PUT",
        route: "/v1/notifications/preferences/project/:projectId",
        action: "UpdateProjectNotificationPreference"
    },
    {
        method: "PUT",
        route: "/v1/notifications/:notificationId/project/:projectId/schedule",
        action: "UpdateProjectNotificationSchedule"
    },
    {
        method: "PUT",
        route: "/v1/notifications/:notificationId/project/:projectId/schedule/user",
        action: "UpdateProjectNotificationScheduleForUser"
    },
    {
        method: "GET",
        route: "/v1/notifications/web/unread",
        action: "GetUnreadNotificationCount"
    },
    {
        method: "GET",
        route: "/v1/notifications/web",
        action: "ListLatestNotifications"
    },
    {
        method: "PUT",
        route: "/v1/notifications/web/markAsRead",
        action: "UpdateNotificationsReadStatus"
    },
    {
        method: "GET",
        route: "/v1/nps/projects/:projectId",
        action: "GetNPSFormsData"
    },
    {
        method: "GET",
        route: "/v1/nps/projects/:projectId/status",
        action: "GetNPSStatus"
    },
    {
        method: "POST",
        route: "/v1/nps/projects/:projectId",
        action: "SubmitNPSResponses"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/incidentsupport",
        action: "UpdateIncidentSupport"
    },
    {
        method: "GET",
        route: "/v1/incidents/fields/:key/values",
        action: "GetFlexibleFieldSpecificValues"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/offerings",
        action: "AddProjectOffering"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/offerings",
        action: "ListProjectOfferings"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/offerings/:offeringId",
        action: "UpdateProjectOffering"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/offerings/:offeringId",
        action: "GetProjectOfferingDetail"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/offerings/:offeringId",
        action: "RemoveProjectOffering"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/offerings/:offeringId/tasks",
        action: "AddProjectOfferingTask"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/offerings/tasks",
        action: "ListProjectOfferingTasks"
    },
    {
        method: "PUT",
        route: "/v1/incidents/:incidentId/offerings/:offeringId/tasks/:taskId",
        action: "UpdateProjectOfferingTask"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/offerings/:offeringId/tasks/:taskId",
        action: "GetProjectOfferingTaskDetail"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/offerings/:offeringId/tasks/:taskId",
        action: "RemoveProjectOfferingTask"
    },
    {
        method: "POST",
        route: "/v1/platformSettings/announcements",
        action: "AddSystemMessage"
    },
    {
        method: "GET",
        route: "/v1/platformSettings/announcements/published",
        action: "GetPublishedSystemMessageDetail"
    },
    {
        method: "GET",
        route: "/v1/platformSettings/announcements/:announcementId",
        action: "GetSystemMessageDetail"
    },
    {
        method: "GET",
        route: "/v1/platformSettings/announcements",
        action: "ListSystemMessages"
    },
    {
        method: "PUT",
        route: "/v1/platformSettings/announcements",
        action: "RemoveSystemMessage"
    },
    {
        method: "PUT",
        route: "/v1/platformSettings/announcements/:announcementId/published",
        action: "UpdateSystemMessagePublishStatus"
    },
    {
        method: "PUT",
        route: "/v1/platformSettings/announcements/:announcementId",
        action: "UpdateSystemMessage"
    },
    {
        method: "POST",
        route: "/v1/automation/rules",
        action: "CreateRule"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/actions/:actionId",
        action: "GetActionDetailsOfRule"
    },
    {
        method: "POST",
        route: "/v1/automation/rules/:ruleId/ruleElements/:ruleElementId/actions/fields/values",
        action: "ListActionComponentFieldValues"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/actions/ruleElements/:ruleElementId",
        action: "ListActionComponentFields"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/ruleElements/actions",
        action: "ListActionComponents"
    },
    {
        method: "DELETE",
        route: "/v1/automation/rules/:ruleId/actions/:actionId",
        action: "RemoveRuleAction"
    },
    {
        method: "POST",
        route: "/v1/automation/rules/:ruleId/actions",
        action: "UpdateActionForRule"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/criteria/:criteriaId",
        action: "GetCriteriaDetailsOfRule"
    },
    {
        method: "POST",
        route: "/v1/automation/rules/:ruleId/ruleElements/:ruleElementId/criteria/fields/values",
        action: "ListCriteriaComponentFieldValues"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/criteria/ruleElements/:ruleElementId",
        action: "ListCriteriaComponentFields"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/ruleElements/criteria",
        action: "ListCriteriaComponents"
    },
    {
        method: "DELETE",
        route: "/v1/automation/rules/:ruleId/criteria/:criteriaId",
        action: "RemoveRuleCriteria"
    },
    {
        method: "POST",
        route: "/v1/automation/rules/:ruleId/criteria",
        action: "UpdateCriteriaForRule"
    },
    {
        method: "GET",
        route: "/v1/automation/facets",
        action: "ListRuleFacets"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/ruleElements",
        action: "ListRuleComponents"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/placeholders",
        action: "ListPlaceholdersForAction"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/versions",
        action: "ListRuleVersions"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/workflow",
        action: "GetRuleWorkflowDetails"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId",
        action: "GetRuleDetails"
    },
    {
        method: "GET",
        route: "/v1/automation/projects",
        action: "ListProjects"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/draft",
        action: "ListDraftRules"
    },
    {
        method: "PUT",
        route: "/v1/automation/rules/:ruleId/publish",
        action: "PublishRule"
    },
    {
        method: "DELETE",
        route: "/v1/automation/rules/:ruleId",
        action: "RemoveRule"
    },
    {
        method: "PUT",
        route: "/v1/automation/rules/status",
        action: "UpdateRuleStatus"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/ruleElements/triggers",
        action: "ListTriggerComponents"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/triggers",
        action: "GetTriggerDetailsOfRule"
    },
    {
        method: "POST",
        route: "/v1/automation/rules/:ruleId/ruleElements/:ruleElementId/triggers/fields/values",
        action: "ListTriggerComponentFieldValues"
    },
    {
        method: "GET",
        route: "/v1/automation/rules/:ruleId/triggers/ruleElements/:ruleElementId",
        action: "ListTriggerComponentFields"
    },
    {
        method: "DELETE",
        route: "/v1/automation/rules/:ruleId/triggers",
        action: "RemoveRuleTrigger"
    },
    {
        method: "POST",
        route: "/v1/automation/rules/:ruleId/triggers",
        action: "UpdateTriggerForRule"
    },
    {
        method: "PUT",
        route: "/v1/automation/rules/:ruleId",
        action: "UpdateRuleGeneralDetails"
    },
    {
        method: "GET",
        route: "/v1/automation/rules",
        action: "ListRules"
    },
    {
        method: "GET",
        route: "/v1/resiliences/organizations/:organizationId/trackers/general",
        action: "GetTrackerDetailsByOrganization"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/economics/overview",
        action: "GetRoadmapEconomicsOverview"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/economics/workstreams",
        action: "GetRoadmapEconomicsWorkStreams"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/economics/offerings",
        action: "ListOfferingsForTrackerObjectives"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings",
        action: "AddOfferingForTrackerObjective"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings",
        action: "GetOfferingForTrackerObjective"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings",
        action: "UpdateOfferingForTrackerObjective"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings",
        action: "RemoveOfferingFromTrackerObjective"
    },
    {
        method: "POST",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings/:offeringId/tasks",
        action: "AddOfferingTaskForTrackerObjective"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings/:offeringId/tasks",
        action: "ListOfferingTasksForTrackerObjective"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings/:offeringId/tasks/:taskId",
        action: "GetOfferingTaskDetailsForTrackerObjective"
    },
    {
        method: "PUT",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings/:offeringId/tasks/:taskId",
        action: "UpdateOfferingTaskForTrackerObjective"
    },
    {
        method: "DELETE",
        route: "/v1/resiliences/trackers/:trackerId/objectives/:objectiveId/offerings/:offeringId/tasks/:taskId",
        action: "RemoveOfferingTaskFromTrackerObjective"
    },
    {
        method: "POST",
        route: "/v1/authentication/organizations/:organizationId/upload/logo",
        action: "UpdateProfileIcon"
    },
    {
        method: "DELETE",
        route: "/v1/authentication/organizations/:organizationId/upload/logo",
        action: "RemoveProfileIcon"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/environment/general",
        action: "UpdateOrganizationEnvironmentGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/environment/activeDirectory",
        action: "UpdateOrganizationEnvironmentActiveDirectoryDetail"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/environment/backup",
        action: "UpdateOrganizationEnvironmentBackupDetail"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/environment/solutions",
        action: "UpdateOrganizationEnvironmentSolutionsDetail"
    },
    {
        method: "PUT",
        route: "/v1/organizations/:organizationId/environment/email",
        action: "UpdateOrganizationEnvironmentEmailDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/environment/general",
        action: "GetOrganizationEnvironmentGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/environment/activeDirectory",
        action: "GetOrganizationEnvironmentActiveDirectoryDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/environment/backup",
        action: "GetOrganizationEnvironmentBackupDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/environment/solutions",
        action: "GetOrganizationEnvironmentSolutionsDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/environment/email",
        action: "GetOrganizationEnvironmentEmailDetail"
    },
    {
        method: "GET",
        route: "/v1/organizations/:organizationId/environment/export",
        action: "ExportOrganizationEnvironmentDetail"
    },
    {
        method: "POST",
        route: "/v1/incidents/:incidentId/engagedParties",
        action: "AddEngagedPartiesToIncident"
    },
    {
        method: "GET",
        route: "/v1/incidents/:incidentId/engagedParties",
        action: "ListEngagedParties"
    },
    {
        method: "DELETE",
        route: "/v1/incidents/:incidentId/engagedParties/:engagedPartyId/organization/:organizationId",
        action: "DeleteEngagedParty"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/offerings",
        action: "ListProjectOfferingsForTracker"
    },
    {
        method: "GET",
        route: "/v1/resiliences/trackers/:trackerId/tasks",
        action: "ListProjectOfferingTasksForTracker"
    },
    {
        method: "GET",
        route: "/v2/resiliences/trackers/:trackerId/statusUpdates/forMF",
        action: "ListTrackerObjectiveStatusUpdatesForMF"
    },
    {
        method: "GET",
        route: "/v2/resiliences/trackers/:trackerId/statusUpdates/forNonMF",
        action: "ListTrackerObjectiveStatusUpdatesForNonMF"
    },
    {
        method: "GET",
        route: "/v2/resiliences/trackers/:trackerId/statusUpdates/pendingForReview/count",
        action: "ListTrackerObjectiveStatusUpdatesForMF"
    },
    {
        method: "GET",
        route: "/v2/resiliences/trackers/:trackerId/statusUpdates/forNonMF/export",
        action: "ExportTrackerObjectiveStatusUpdatesForNonMF"
    },
    {
        method: "GET",
        route: "/v2/resiliences/trackers/:trackerId/statusUpdates/forMF/export",
        action: "ExportTrackerObjectiveStatusUpdatesForMF"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/milestonesProgress",
        action: "GetMilestonesByProgress"
    },
    {
        method: "GET",
        route: "/v2/resiliences/oragnizations/:organizationId/statistics/objectivesByStatus",
        action: "GetObjectivesByStatus"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByMonths",
        action: "GetIncidentsByMonths"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByIndustryType",
        action: "GetIncidentsByIndustryType"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByEmployeeCount",
        action: "GetIncidentsByEmployeeCount"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByRansomwareVariants",
        action: "GetIncidentsByRansomwareVariants"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByRansomPaid",
        action: "GetIncidentsByRansomPaid"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByBackupViability",
        action: "GetIncidentsByBackupViability"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByDomainJoinedBackups",
        action: "GetIncidentsByDomainJoinedBackups"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByInitialEntryPoint",
        action: "GetIncidentsByInitialEntryPoint"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByStatus",
        action: "GetIncidentsByStatus"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/statistics/incidentsByCarriers",
        action: "GetIncidentsByCarriers"
    },
    {
        method: "GET",
        route: "/v2/resiliences/oragnizations/:organizationId/statistics/associatedResilienceObjectives",
        action: "GetAssociatedResilienceObjectives"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/:objectiveId/partners",
        action: "ListPartnersForMarketplaceObjective"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/:objectiveId/clients",
        action: "ListClientsForMarketplaceObjective"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId/objectives",
        action: "ListObjectiveListingForPartner"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId/objectives/:objectiveId/clients",
        action: "ListObjectiveClientsForPartner"
    },
    {
        method: "POST",
        route: "/v2/resiliences/partners/:partnerId/objectives/request",
        action: "CreatePartnerRequestForMarketplaceObjective"
    },
    {
        method: "PUT",
        route: "/v2/resiliences/partners/:partnerId/objectives/request/status",
        action: "UpdatePartnerRequestStatusForMarketplaceObjective"
    },
    {
        method: "PUT",
        route: "/v2/resiliences/partners/:partnerId/objectives/request",
        action: "RemovePartnerRequestForMarketplaceObjective"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partnerRequests",
        action: "ListPartnerRequestsForMarketplaceObjectives"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId/requests",
        action: "ListPartnerRequestsForMarketplaceObjectivesForPartner"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners",
        action: "ListMarketplacePartners"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId",
        action: "GetMarketplacePartnerDetail"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId/customers/tracker/:trackerId",
        action: "ListClientObjectivesForPartner"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId/customers",
        action: "ListClientsForPartner"
    },
    {
        method: "POST",
        route: "/v2/resiliences/objectives/:objectiveId/reviews",
        action: "PostReviewForObjective"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/:objectiveId/reviews/:reviewId",
        action: "GetReviewDetail"
    },
    {
        method: "PUT",
        route: "/v2/resiliences/objectives/:objectiveId/reviews/:reviewId",
        action: "UpdateReviewDetail"
    },
    {
        method: "DELETE",
        route: "/v2/resiliences/objectives/:objectiveId/reviews/:reviewId",
        action: "RemoveReview"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/:objectiveId/reviews",
        action: "ListReviewsForMarketplaceObjective"
    },
    {
        method: "GET",
        route: "/v2/resiliences/partners/:partnerId/reviews",
        action: "ListReviewsForPartnerObjectivesListing"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/:objectiveId/trackerMembership",
        action: "CheckCatalogObjectiveTrackerMembership"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/:objectiveId/reviews/eligibilityCheck",
        action: "GetEligibilityCheckResultForPostingReview"
    },
    {
        method: "POST",
        route: "/v2/resiliences/objectives/:objectiveId/upload/icon",
        action: "UpdateMarketplaceObjectiveIcon"
    },
    {
        method: "GET",
        route: "/v2/resiliences/objectives/byCategory",
        action: "ListMarketplaceObjectivesByCategory"
    },
    {
        method: "GET",
        route: "/v2/resiliences/search",
        action: "SearchMarketplace"
    },
    {
        method: "GET",
        route: "/v2.1/resiliences/trackers",
        action: "ListTrackers"
    },
    {
        method: "GET",
        route: "/v2.1/resiliences/trackers/organizations/:organizationId",
        action: "ListTrackersForOrganization"
    },
    {
        method: "GET",
        route: "/v2.1/resiliences/trackers/organizations/:organizationId/users/:userId",
        action: "ListTrackersForOrganizationUser"
    },
    {
        method: "GET",
        route: "/v2/organizations/:organizationId/users",
        action: "ListOrganizationUsers"
    },
    {
        method: "PUT",
        route: "/v2/resiliences/trackers/:trackerId/objectives/:objectiveId/partners",
        action: "AddPartnerToTrackerObjective"
    },
    {
        method: "DELETE",
        route: "/v2/resiliences/trackers/:trackerId/objectives/:objectiveId/partners/:partnerId",
        action: "RemovePartnerFromTrackerObjective"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets",
        action: "ListAssets"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/assets/status",
        action: "AddAssetStatus"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/assets/:assetId/comments",
        action: "CreateAssetComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/:assetId/comments",
        action: "ListAssetComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/assets/:assetId/allComments/:commentId",
        action: "RemoveAssetAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/assets/:assetId/comments/:commentId",
        action: "RemoveAssetComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/allComments/:commentId",
        action: "UpdateAssetAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/comments/:commentId",
        action: "UpdateAssetComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/assets",
        action: "CreateAsset"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/export",
        action: "ExportAssets"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/facets",
        action: "ListAssetFacets"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/:assetId/dataPreservation",
        action: "GetAssetDataPreservationDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/:assetId/dates",
        action: "GetAssetDatesDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/:assetId/forensics",
        action: "GetAssetForensicDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/:assetId/host",
        action: "GetAssetHostDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/:assetId/recovery",
        action: "GetAssetRecoveryDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/statusLibrary",
        action: "GetAssetStatusLibrary"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/assets/import/csv",
        action: "ImportAssets"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets/statuses",
        action: "ListAssetStatuses"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/assets",
        action: "ListAssets"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/assets/:assetId",
        action: "RemoveAsset"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/assets/status/:statusId",
        action: "RemoveAssetStatusFromLibrary"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets",
        action: "RemoveAssets"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/dataPreservation",
        action: "UpdateAssetDataPreservationDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/dates",
        action: "UpdateAssetDatesDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/forensics/compromisedStatus",
        action: "UpdateAssetForensicsCompromisedStatus"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/forensics",
        action: "UpdateAssetForensicsDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/host",
        action: "UpdateAssetHostDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/recovery",
        action: "UpdateAssetRecoveryDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/:assetId/recovery/status",
        action: "UpdateAssetRecoveryStatus"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/status/:statusId",
        action: "UpdateAssetStatusOfLibrary"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets/statuses",
        action: "UpdateAssetStatuses"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/assets-attribute",
        action: "UpdateAssets"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/comments",
        action: "CreateTaskComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/comments",
        action: "ListTaskComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/allComments/:commentId",
        action: "RemoveTaskAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/comments/:commentId",
        action: "RemoveTaskComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/allComments/:commentId",
        action: "UpdateTaskAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/comments/:commentId",
        action: "UpdateTaskComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/tasks",
        action: "CreateTask"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/export",
        action: "ExportProjectTasks"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/general",
        action: "GetProjectTaskGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/status",
        action: "GetProjectTaskStatusDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/tasks",
        action: "ListProjectTasks"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/tasks",
        action: "RemoveProjectTasks"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/general",
        action: "UpdateProjectTaskGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/status",
        action: "UpdateProjectTaskStatusDetails"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments",
        action: "CreateMilestoneComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments",
        action: "ListMilestoneComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/allComments/:commentId",
        action: "RemoveMilestoneAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments/:commentId",
        action: "RemoveMilestoneComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/allComments/:commentId",
        action: "UpdateMilestoneAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/comments/:commentId",
        action: "UpdateMilestoneComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/milestones",
        action: "CreateMilestone"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/export",
        action: "ExportProjectMilestones"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/general",
        action: "GetProjectMilestoneGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/status",
        action: "GetProjectMilestoneStatusDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/milestones",
        action: "ListProjectMilestones"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/milestones",
        action: "RemoveProjectMilestones"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/general",
        action: "UpdateProjectMilestoneGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/:milestoneId/status",
        action: "UpdateProjectMilestoneStatusDetails"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/status",
        action: "GetProjectTaskStatusDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/:taskId/milestone",
        action: "UpdateProjectTaskMilestoneDetails"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/milestonesProgress",
        action: "GetMilestonesByProgress"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/forMF/export",
        action: "ExportProjectStatusUpdatesforMF"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/forNonMF/export",
        action: "ExportProjectStatusUpdatesforNonMF"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/forMF",
        action: "ListProjectStatusUpdatesforMF"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/forNonMF",
        action: "ListProjectStatusUpdatesforNonMF"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/pendingForReview/count",
        action: "ListProjectStatusUpdatesforMF"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates",
        action: "CreateStatusUpdate"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/availableDates",
        action: "GetAvailableDatesForStatusUpdates"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/general",
        action: "GetProjectStatusUpdateGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/status",
        action: "GetProjectStatusUpdateStatusDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/chronicles",
        action: "GetStatusUpdateChronicles"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates",
        action: "RemoveProjectStatusUpdates"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/general",
        action: "UpdateProjectStatusUpdateGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/status",
        action: "UpdateProjectStatusUpdateStatusDetails"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/reviewStatus",
        action: "UpdatePendingForReviewStatusOfProject"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments",
        action: "CreateStatusUpdateComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments",
        action: "ListStatusUpdateComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/allComments/:commentId",
        action: "RemoveStatusUpdateAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments/:commentId",
        action: "RemoveStatusUpdateComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/allComments/:commentId",
        action: "UpdateStatusUpdateAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments/:commentId",
        action: "UpdateStatusUpdateComment"
    },
    {
        method: "POST",
        route: "/v1/oauth/applications",
        action: "GenerateApplication"
    },
    {
        method: "GET",
        route: "/v1/oauth/applications",
        action: "ListApplication"
    },
    {
        method: "POST",
        route: "/v1/oauth/applications/tokens",
        action: "TokenRequest"
    },
    {
        method: "PUT",
        route: "/v1/oauth/applications/:applicationId",
        action: "UpdateApplication"
    },
    {
        method: "PUT",
        route: "/v1/oauth/applications/:applicationId/status",
        action: "UpdateApplicationStatus"
    },
    {
        method: "GET",
        route: "/v1/oauth/applications/:applicationId",
        action: "GetApplication"
    },
    {
        method: "DELETE",
        route: "/v1/oauth/applications/:applicationId",
        action: "RemoveApplication"
    },
    {
        method: "GET",
        route: "/v2/resiliences/trackers/:trackerId/timeline",
        action: "GetTrackerTimelineData"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/ioc/:iocId/comments",
        action: "CreateIOCComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/ioc/:iocId/comments",
        action: "ListIOCComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/ioc/:iocId/allComments/:commentId",
        action: "RemoveIOCAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/ioc/:iocId/comments/:commentId",
        action: "RemoveIOCComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/ioc/:iocId/allComments/:commentId",
        action: "UpdateIOCAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/ioc/:iocId/comments/:commentId",
        action: "UpdateIOCComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/ioc",
        action: "CreateIOC"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/ioc/export",
        action: "ExportIOC"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/ioc/:iocId/indicator",
        action: "GetIOCIndicatorDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/ioc/:iocId/remediationAndContainment",
        action: "GetIOCRemediationAndContainmentDetail"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/ioc/import/csv",
        action: "ImportIOC"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/ioc",
        action: "ListIOC"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/ioc",
        action: "RemoveIOC"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/ioc/:iocId/indicator",
        action: "UpdateIOCIndicatorDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/ioc/:iocId/remediationAndContainment",
        action: "UpdateIOCRemediationAndContainmentDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/compromisedSystemsbyStatus",
        action: "WidgetCompromisedSystemsByStatusDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/compromisedSystems",
        action: "WidgetCompromisedSystemsDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/incidentProgress",
        action: "WidgetIncidentProgressDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/milestonesbyCategory",
        action: "WidgetMilestonesByCategoryDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/milestonesbyProgress",
        action: "WidgetMilestonesByProgressDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/systembyRecoveryStage",
        action: "WidgetSystemsByRecoveryStageDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/systemsGroupedbyApplication",
        action: "WidgetSystemsGroupedByApplicationDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/systemsGroupedbyTier",
        action: "WidgetSystemsGroupedByTierDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/tasksbyOrganizations",
        action: "WidgetTasksByOrganizationDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/tasksbyUsers",
        action: "WidgetTasksByUsersDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId",
        action: "GetIncidentDetails"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/engagedParties",
        action: "AddEngagedPartiesToIncident"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/engagedParties/:engagedPartyId",
        action: "DeleteEngagedParty"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/engagedParties",
        action: "ListEngagedParties"
    },
    {
        method: "GET",
        route: "/v2/incidents/opportunities/export",
        action: "ExportIncidentOpportunities"
    },
    {
        method: "GET",
        route: "/v2/incidents/export",
        action: "ExportIncidents"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/export",
        action: "ExportIncidentsForOrganization"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/users/:userId/export",
        action: "ExportIncidentsForOrganizationUser"
    },
    {
        method: "POST",
        route: "/v2/incidents",
        action: "CreateIncidentOpportunity"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId",
        action: "RemoveIncident"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/environment/activeDirectory",
        action: "GetEnvironmentActiveDirectoryDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/environment/backups",
        action: "GetEnvironmentBackupsDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/environment/email",
        action: "GetEnvironmentEmailDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/environment/general",
        action: "GetEnvironmentGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/environment/solutions",
        action: "GetEnvironmentSolutionsDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/environment/backups",
        action: "UpdateEnvironmentBackupsDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/environment/email",
        action: "UpdateEnvironmentEmailDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/environment/solutions",
        action: "UpdateEnvironmentSolutionsDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/facets",
        action: "ListIncidentFacets"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/companyBackground",
        action: "GetCompanyBackground"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/companyBackground",
        action: "GetCompanyBackground"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/modules",
        action: "GetIncidentModules"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/incident/businessEmailCompromise",
        action: "GetIncidentBusinessEmailCompromiseDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/incident/extortion",
        action: "GetIncidentExtortionDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/incident/general",
        action: "GetIncidentGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/incident/ransomware",
        action: "GetIncidentRansomewareDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/incident/businessEmailCompromise",
        action: "UpdateIncidentBusinessEmailCompromiseDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/incident/extortion",
        action: "UpdateIncidentExtortionDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/incident/general",
        action: "UpdateIncidentGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/incident/ransomware",
        action: "UpdateIncidentRansomwareDetail"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/insurance/general",
        action: "DetachInsuranceCarrier"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/insurance/claim",
        action: "GetInsuranceClaimDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/insurance/general",
        action: "GetInsuranceGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/insurance/policy",
        action: "GetInsurancePolicyDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/insurance/claim",
        action: "UpdateInsuranceClaimDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/insurance/policy",
        action: "UpdateInsurancePolicyDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId",
        action: "PublishIncidentDeal"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/companyBackground",
        action: "UpdateCompanyBackgroundDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/incidentsupport",
        action: "UpdateIncidentSupport"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/progressPercentage",
        action: "UpdateIncidentProgressPercentage"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/status",
        action: "UpdateIncidentStatus"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/opportunity/validate",
        action: "ValidateIncidentOpportunity"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/widgets/:widgetId/myTasksbyIncident",
        action: "WidgetTasksByIncidentDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/modules",
        action: "UpdateIncidentModules"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/tasks/import/csv",
        action: "ImportTasks"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/tasks-attribute",
        action: "UpdateTasks"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/statusUpdates/import/csv",
        action: "ImportStatusUpdates"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/statusupdates-attribute",
        action: "UpdateStatusUpdates"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/milestones/import/csv",
        action: "ImportMilestones"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/milestones-attribute",
        action: "UpdateMilestones"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/users/:userId",
        action: "ListIncidentsForOrganizationUser"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId",
        action: "ListIncidentsForOrganization"
    },
    {
        method: "GET",
        route: "/v2/incidents",
        action: "ListIncidents"
    },
    {
        method: "GET",
        route: "/v2/incidents/opportunities",
        action: "ListIncidentOpportunities"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/comments",
        action: "CreateIncidentTimelineComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/comments",
        action: "ListIncidentTimelineComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/allComments/:commentId",
        action: "RemoveIncidentTimelineAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/comments/:commentId",
        action: "RemoveIncidentTimelineComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/allComments/:commentId",
        action: "UpdateIncidentTimelineAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/comments/:commentId",
        action: "UpdateIncidentTimelineComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/timeline",
        action: "CreateIncidentTimelineEntry"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/timeline/:timelineId",
        action: "RemoveIncidentTimeline"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/export",
        action: "ExportIncidentTimelineEntries"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/file/:fileId",
        action: "RemoveTimelineNegotiationAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/file/:fileId/download",
        action: "DownloadTimelineNegotiationAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/download/files",
        action: "DownloadTimelineNegotiationAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/files",
        action: "ListTimelineNegotiationAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/upload/files",
        action: "UploadTimelineNegotiationAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/forensic",
        action: "GetTimelineForensicDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/general",
        action: "GetTimelineGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/negotiation",
        action: "GetTimelineNegotiationDetail"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/timeline/import/csv",
        action: "ImportTimelineEntries"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/timeline",
        action: "ListTimelines"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/forensic",
        action: "UpdateTimelineForensicDetails"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/general",
        action: "UpdateTimelineGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/timeline/:timelineId/negotiation",
        action: "UpdateTimelineNegotiationDetails"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/overview/general/economics",
        action: "GetEconomicsOverview"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/projectStatus/overview/general/economics/workstreams",
        action: "GetEconomicsWorkStreams"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/economics/offerings",
        action: "ListOfferingsForIncident"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/offerings/:offeringId/tasks",
        action: "AddProjectOfferingTask"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/offerings/:offeringId/tasks/:taskId",
        action: "GetProjectOfferingTaskDetail"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/offerings/:offeringId/tasks/:taskId",
        action: "RemoveProjectOfferingTask"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/offerings/:offeringId/tasks/:taskId",
        action: "UpdateProjectOfferingTask"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/offerings/tasks",
        action: "ListProjectOfferingTasks"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/offerings",
        action: "AddProjectOffering"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/offerings/:offeringId",
        action: "GetProjectOfferingDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/offerings",
        action: "ListProjectOfferings"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/offerings/:offeringId",
        action: "RemoveProjectOffering"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/offerings/:offeringId",
        action: "UpdateProjectOffering"
    },
    {
        method: "GET",
        route: "/v2.1/incidents/export",
        action: "ExportIncidents"
    },
    {
        method: "GET",
        route: "/v2.1/incidents",
        action: "ListIncidents"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/serviceLines",
        action: "UpdateServiceLines"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/comments",
        action: "CreateInvoiceComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/comments",
        action: "ListInvoiceComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/allComments/:commentId",
        action: "RemoveInvoiceAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/comments/:commentId",
        action: "RemoveInvoiceComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/allComments/:commentId",
        action: "UpdateInvoiceAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/comments/:commentId",
        action: "UpdateInvoiceComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/invoices",
        action: "CreateInvoice"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/export",
        action: "ExportClaimInvoices"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/file/:fileId",
        action: "RemoveClaimInvoiceAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/file/:fileId/download",
        action: "DownloadClaimInvoiceAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/download/files",
        action: "DownloadClaimInvoiceAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/files",
        action: "ListClaimInvoiceAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/upload/files",
        action: "UploadClaimInvoiceAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/carrierUpdates",
        action: "GetClaimInvoiceCarrierUpdatesDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/general",
        action: "GetClaimInvoiceGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/moxfiveAnalysis",
        action: "GetClaimInvoiceMOXFIVEAnalysisDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/status",
        action: "GetClaimInvoiceStatusDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/invoices",
        action: "ListClaimInvoices"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices",
        action: "RemoveClaimInvoices"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/carrierUpdates",
        action: "UpdateClaimInvoiceCarrierUpdatesDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/general",
        action: "UpdateClaimInvoiceGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/moxfiveAnalysis",
        action: "UpdateClaimInvoiceMOXFIVEAnalysisDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/invoices/:invoiceId/status",
        action: "UpdateClaimInvoiceStatusDetail"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments",
        action: "CreateRFIComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments",
        action: "ListRFIComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/allComments/:commentId",
        action: "RemoveRFIAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments/:commentId",
        action: "RemoveRFIComment"
    }, {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/allComments/:commentId",
        action: "UpdateRFIAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/comments/:commentId",
        action: "UpdateRFIComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/requestForInformations",
        action: "CreateRFI"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/export",
        action: "ExportRequestForInformations"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/file/:fileId",
        action: "RemoveClaimRFIAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/file/:fileId/download",
        action: "DownloadClaimRFIAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/download/files",
        action: "DownloadClaimRFIAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/files",
        action: "ListClaimRFIAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/upload/files",
        action: "UploadClaimRFIAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/general",
        action: "GetClaimRequestForInformationGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/insuredResponse",
        action: "GetClaimRequestForInformationInsuredResponseDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/requestForInformations",
        action: "ListClaimRequestForInformations"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/requestForInformations",
        action: "RemoveClaimRequestForInformation"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/general",
        action: "UpdateClaimRequestForInformationGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/requestForInformations/:requestForInformationId/insuredResponse",
        action: "UpdateClaimRequestForInformationInsuredResponseDetail"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/comments",
        action: "CreateSupplementalComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/comments",
        action: "ListSupplementalComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/allComments/:commentId",
        action: "RemoveSupplementalAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/comments/:commentId",
        action: "RemoveSupplementalComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/allComments/:commentId",
        action: "UpdateSupplementalAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/comments/:commentId",
        action: "UpdateSupplementalComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/supplementals",
        action: "CreateSupplementalData"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/export",
        action: "ExportClaimSupplementals"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/file/:fileId",
        action: "RemoveClaimSupplementalDataAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/file/:fileId/download",
        action: "DownloadClaimSupplementalDataAttachmentFile"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/download/files",
        action: "DownloadClaimSupplementalDataAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/files",
        action: "ListClaimSupplementalDataAttachmentFiles"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/upload/files",
        action: "UploadClaimSupplementalDataAttachmentFiles"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/analysis",
        action: "GetClaimSupplementalAnalysisDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/general",
        action: "GetClaimSupplementalGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/supplementals",
        action: "ListClaimSupplementals"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/supplementals",
        action: "RemoveClaimSupplementals"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/analysis",
        action: "UpdateClaimSupplementalAnalysisDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/supplementals/:supplementalId/general",
        action: "UpdateClaimSupplementalGeneralDetail"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/comments",
        action: "CreateTimelineComment"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/comments",
        action: "ListTimelineComments"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/allComments/:commentId",
        action: "RemoveTimelineAnyComment"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/comments/:commentId",
        action: "RemoveTimelineComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/allComments/:commentId",
        action: "UpdateTimelineAnyComment"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/comments/:commentId",
        action: "UpdateTimelineComment"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/timeline",
        action: "CreateTimeline"
    },
    {
        method: "DELETE",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId",
        action: "RemoveClaimTimeline"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/timeline/export",
        action: "ExportClaimTimelineEntries"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/general",
        action: "GetClaimTimelineGeneralDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/vendorDetails",
        action: "GetClaimTimelineVendorDetailsDetail"
    },
    {
        method: "POST",
        route: "/v2/incidents/:incidentId/claims/timeline/import/csv",
        action: "ImportClaimTimelineEntries"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/claims/timeline",
        action: "ListClaimTimelines"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/general",
        action: "UpdateClaimTimelineGeneralDetail"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/claims/timeline/:timelineId/vendorDetails",
        action: "UpdateClaimTimelineVendorDetails"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/carrierUpdates",
        action: "WidgetCarrierUpdatesDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/totalClaimCostbyCategory",
        action: "WidgetTotalClaimCostsByCategory"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/totalClaimCostbyVendor",
        action: "WidgetTotalClaimCostsByVendorDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/invoicesbyStatus",
        action: "WidgetInvoicesByStatusDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/invoicesStatusbyVendor",
        action: "WidgetInvoicesStatusByVendorDetail"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/projects/:projectId/widgets/:widgetId/moxfiveAnalysis",
        action: "WidgetMOXFIVEAnalysisDetail"
    },
    {
        method: "GET",
        route: "/v2/authentication/am/summary",
        action: "GetAccessManagementSummary"
    },
    {
        method: "POST",
        route: "/v2/authentication/organizations/:organizationId/members",
        action: "AddMemberToOrganization"
    },
    {
        method: "POST",
        route: "/v2/authentication/organizations/:organizationId/members/emails",
        action: "AddMemberToOrganizationUsingEmail"
    },
    {
        method: "PUT",
        route: "/v2/authentication/organizations/:organizationId/changeMembership",
        action: "ChangeOrganizationMembership"
    },
    {
        method: "POST",
        route: "/v2/authentication/organizations",
        action: "CreateOrganization"
    },
    {
        method: "DELETE",
        route: "/v2/authentication/organizations/:organizationId/upload/logo",
        action: "RemoveProfileIcon"
    },
    {
        method: "GET",
        route: "/v2/authentication/users/ad",
        action: "GetUsersOfActiveDirectory"
    },
    {
        method: "GET",
        route: "/v2/authentication/organizations",
        action: "ListOrganizations"
    },
    {
        method: "GET",
        route: "/v2/authentication/users",
        action: "ListUsers"
    },
    {
        method: "POST",
        route: "/v2/authentication/users/membersOf",
        action: "GetUserMembershipDetail"
    },
    {
        method: "GET",
        route: "/v2/authentication/organizations/:organizationId/images/:type",
        action: "GetOrganizationProfileFavicon"
    },
    {
        method: "GET",
        route: "/v2/authentication/organizations/:organizationId/users/:userId",
        action: "GetOrganizationUserDetail"
    },
    {
        method: "GET",
        route: "/v2/authentication/organizations/:organizationId/users",
        action: "ListOrganizationUsers"
    },
    {
        method: "GET",
        route: "/v2/authentication/dashboards/widgets/:widgetId/organizationsByTypes",
        action: "WidgetOrganizationsByTypesDetail"
    },
    {
        method: "GET",
        route: "/v2/authentication/organizations/:organizationId",
        action: "GetOrganizationDetail"
    },
    {
        method: "GET",
        route: "/v2/authentication/users/:userId",
        action: "GetUserDetail"
    },
    {
        method: "GET",
        route: "/v2/authentication/users/me/permissions",
        action: "GetUserPermissions"
    },
    {
        method: "GET",
        route: "/v2/authentication/users/me",
        action: "GetUserProfile"
    },
    {
        method: "GET",
        route: "/v2/authentication/refreshToken",
        action: "RefreshToken"
    },
    {
        method: "DELETE",
        route: "/v2/authentication/organizations/:organizationId/members",
        action: "RemoveMembersOfOrganization"
    },
    {
        method: "DELETE",
        route: "/v2/authentication/organizations/:organizationId/owners",
        action: "RemoveOwnersOfOrganization"
    },
    {
        method: "PUT",
        route: "/v2/authentication/organizations/:organizationId",
        action: "UpdateOrganization"
    },
    {
        method: "PUT",
        route: "/v2/authentication/organizations/:organizationId/users/:userId",
        action: "UpdateUserProfileOfAnOrganization"
    },
    {
        method: "PUT",
        route: "/v2/authentication/organizations/status",
        action: "UpdateOrganizationStatus"
    },
    {
        method: "PUT",
        route: "/v2/authentication/organizations/:organizationId/users/status",
        action: "UpdateUsersStatusOfAnOrganization"
    },
    {
        method: "PUT",
        route: "/v2/authentication/users/:userId",
        action: "UpdateUserDetails"
    },
    {
        method: "PUT",
        route: "/v2/authentication/users/status",
        action: "UpdateUsersStatus"
    },
    {
        method: "POST",
        route: "/v2/authentication/organizations/:organizationId/upload/logo",
        action: "UpdateProfileIcon"
    },
    {
        method: "GET",
        route: "/v2/authorization/policies",
        action: "ListPolicies"
    },
    {
        method: "POST",
        route: "/v2/authentication/userupdate/callback",
        action: "AzureUsersUpdatesWebhook"
    },
    {
        method: "POST",
        route: "/v2/authentication/groupupdate/callback",
        action: "AzureGroupsUpdatesWebhook"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByBackupViability",
        action: "GetIncidentsByBackupViability"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByDomainJoinedBackups",
        action: "GetIncidentsByDomainJoinedBackups"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByEmployeeCount",
        action: "GetIncidentsByEmployeeCount"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByEndpointCount",
        action: "GetIncidentsByEndpointCount"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByIndustryType",
        action: "GetIncidentsByIndustryType"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByInitialEntryPoint",
        action: "GetIncidentsByInitialEntryPoint"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByRansomPaid",
        action: "GetIncidentsByRansomPaid"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByRansomwareVariants",
        action: "GetIncidentsByRansomwareVariants"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByStatus",
        action: "GetIncidentsByStatus"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByCarriers",
        action: "GetIncidentsByCarriers"
    },
    {
        method: "GET",
        route: "/v2/incidents/organizations/:organizationId/statistics/incidentsByMonths",
        action: "GetIncidentsByMonths"
    },
    {
        method: "GET",
        route: "/v2/incidents/dashboards/widgets/:widgetId/incidentsbyStatus",
        action: "WidgetIncidentsByStatusDetail"
    },
    // Threat-intel
    {
        method: "POST",
        route: "/v1/mfi/fields",
        action: "AddMOXFIVEInsightFlexibleField"
    },
    {
        method: "POST",
        route: "/v1/mfi/fields/byKeys",
        action: "ListMOXFIVEInsightFlexibleFieldsByKeys"
    },
    {
        method: "POST",
        route: "/v1/mfi/threatActors",
        action: "AddThreatActor"
    },
    {
        method: "GET",
        route: "/v1/mfi/countries",
        action: "ListTargetedRegions"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/attachments/:attachmentId",
        action: "GetThreatActorAttchmentFile"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/channels",
        action: "GetThreatActorChannelsDetail"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/general",
        action: "GetThreatActorGeneralDetail"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors",
        action: "ListThreatActors"
    },
    {
        method: "DELETE",
        route: "/v1/mfi/threatActors/:threatActorId/attachments/:attachmentId",
        action: "RemoveThreatActorAttachments"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors",
        action: "RemoveThreatActors"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/channels",
        action: "UpdateThreatActorChannelsDetails"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/general",
        action: "UpdateThreatActorGeneralDetails"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/general/thumbImage",
        action: "UpdateThreatActorThumbImage"
    },
    {
        method: "POST",
        route: "/v1/mfi/files/uploads",
        action: "UploadTempAttachments"
    },
    {
        method: "PUT",
        route: "/v2/incidents/:incidentId/incident/initialEntryPointDetails",
        action: "UpdateIncidentInitialEntryPointDetails"
    },
    {
        method: "GET",
        route: "/v2/incidents/:incidentId/incident/initialEntryPointDetails",
        action: "GetIncidentInitialEntryPointDetails"
    },
    /* <<<<<< Status Updates v3 >>>>>>  */
    // email
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/email/statistics",
        action: "GetStatusUpdateEmailsStatistics"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/email/send",
        action: "SendStatusUpdateViaEmail"
    },
    // layout
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/layout",
        action: "GetStatusUpdateLayoutDetail"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/layout",
        action: "UpdateStatusUpdateLayout"
    },
    // meta data widgets
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/widgets/metadata",
        action: "GetGlobalWidgetMetadata"
    },
    // status updates version
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/version",
        action: "GetStatusUpdateFeatureActiveVersion"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/version",
        action: "UpdateStatusUpdateFeatureActiveVersion"
    },
    // updates
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/clone",
        action: "CloneStatusUpdate"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId",
        action: "GetStatusUpdateDetail"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates",
        action: "ListStatusUpdates"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/published",
        action: "ListPublishedStatusUpdates"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/publish",
        action: "PublishStatusUpdate"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/draft",
        action: "RemoveStatusUpdateDraft"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId",
        action: "RemoveStatusUpdate"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId",
        action: "UpdateStatusUpdateWidgetOrder"
    },
    // Widget configuration
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/widgets",
        action: "ListStatusUpdateAssociatedWidgets"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/widgets",
        action: "UpdateStatusUpdateAssociatedWidgets"
    },
    // widgets
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/statusUpdates/widgets",
        action: "CreateGlobalWidget"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/widgets",
        action: "ListGlobalWidgets"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/statusUpdates/widgets/:widgetId",
        action: "RemoveGlobalWidget"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/widgets/:widgetId",
        action: "UpdateGlobalWidget"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/ioc/:iocId/snapshots",
        action: "AddIOCSnapshot"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/ioc/:iocId/snapshots",
        action: "ListIOCSnapshots"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/ioc/:iocId/snapshots/:snapshotId",
        action: "RemoveIOCSnapshot"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/tasks/:taskId/snapshots",
        action: "AddTaskSnapshot"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/tasks/:taskId/snapshots",
        action: "ListTaskSnapshots"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/tasks/:taskId/snapshots/:snapshotId",
        action: "RemoveTaskSnapshot"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/timeline/:timelineId/snapshots",
        action: "ListTimelineSnapshots"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/timeline/:timelineId/snapshots/:snapshotId",
        action: "RemoveTimelineSnapshot"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/timeline/:timelineId/snapshots",
        action: "AddTimelineSnapshot"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/activityFeed/events",
        action: "ListActivityFeedWidgetEvents"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId/custom",
        action: "CreateActivityListWidgetCustomRecord"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId",
        action: "CreateActivityListWidgetRecord"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId/records/:recordId",
        action: "GetActivityListWidgetRecordDetail"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId",
        action: "ListActivityListWidgetRecords"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId/records",
        action: "RemoveActivityListWidgetRecords"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId/records/:recordId",
        action: "RemoveActivityListWidgetRecord"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId/custom/:customId",
        action: "UpdateActivityListWidgetCustomRecord"
    },
    {
        method: "PUT",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/actlWidgets/:actlWidgetId/records/:recordId",
        action: "UpdateActivityListWidgetRecordOrder"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/notWidgets/:notWidgetId/files/:fileId",
        action: "GetNotesWidgetDetail"
    },
    {
        method: "GET",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/notWidgets/:notWidgetId",
        action: "GetNotesWidgetDetail"
    },
    {
        method: "DELETE",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/notWidgets/:notWidgetId/files/:fileId",
        action: "UpdateNotesWidgetDetail"
    },
    {
        method: "POST",
        route: "/v3/incidents/:incidentId/statusUpdates/:statusUpdateId/notWidgets/:notWidgetId/upload",
        action: "UpdateNotesWidgetDetail"
    },
    //threatActor-highlight
    {
        method: "POST",
        route: "/v1/mfi/threatActors/:threatActorId/highlights",
        action: "AddThreatActorHighlight"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId",
        action: "UpdateThreatActorHighlight"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/highlights",
        action: "ListHighlightRequestsForThreatActor"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/highlights",
        action: "ListThreatActorHighlights"
    },
    {
        method: "POST",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId/attachments",
        action: "AddThreatActorHighlightAttachments"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId/screenshots/:screenshotId/download",
        action: "DownloadThreatActorHighlightAttachment"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId/screenshots/:screenshotId",
        action: "GetThreatActorHighlightAttachmentFile"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId/screenshots",
        action: "ListThreatActorHighlightAttachments"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId/screenshots",
        action: "RemoveThreatActorHighlightAttachments"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/highlights",
        action: "RemoveThreatActorHighlights"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/highlights/:highlightId/status",
        action: "UpdateThreatActorHighlightStatus"
    },
    {
        method: "GET",
        route: "/v1/mfi/threatActors/:threatActorId/stats",
        action: "GetThreatActorStats"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/stats",
        action: "UpdateThreatActorStats"
    },
    {
        method: "PUT",
        route: "/v1/mfi/threatActors/:threatActorId/stats/configuration",
        action: "UpdateThreatActorStatsConfiguration"
    },
    // unified organizations
    {
        method: "GET",
        route: "/v1/incidents/organizations",
        action: "ListOrganizationsOfIncidents"
    },
    {
        method: "GET",
        route: "/v1/incidents/users/:userId/organizations",
        action: "ListOrganizationsForUserOfIncidents"
    },
    {
        method: "POST",
        route: "/v1/incidents/users/:userId/projects",
        action: "ListIncidentsOfOrganizationUser"
    },
    {
        method: "GET",
        route: "/v1/incidents/organizations/:organizationId/projects",
        action: "ListIncidentsOfOrganization"
    },
    {
        method: "GET",
        route: "/v1/resiliences/organizations",
        action: "ListOrganizationsOfResilience"
    },
    {
        method: "GET",
        route: "/v1/resiliences/users/:userId/organizations",
        action: "ListOrganizationsForUserOfResilience"
    },
    {
        method: "POST",
        route: "/v1/resiliences/users/:userId/projects",
        action: "ListObjectivesOfOrganizationUser"
    },
    {
        method: "GET",
        route: "/v1/resiliences/organizations/:organizationId/projects",
        action: "ListObjectivesOfOrganization"
    },
    // new authentication
    {
        method: "POST",
        route: "/v3/authentication/organizations/:organizationId/users",
        action: "AddUserToOrganization",
    },
    {
        method: "POST",
        route: "/v3/authentication/validateUser",
        action: "ValidateUser",
    },
    {
        method: "POST",
        route: "/v3/authentication/mfa/associate",
        action: "AssociateMFAEnrollment",
    },
    {
        method: "POST",
        route: "/v3/authentication/mfa/verify",
        action: "VerifyMFAEnrollment",
    },
    {
        method: "POST",
        route: "/v3/authentication/verifyEmail",
        action: "VerifyUserEmail",
    },
    {
        method: "POST",
        route: "/v3/authentication/setPassword",
        action: "SetUserPassword",
    },
    {
        method: "POST",
        route: "/v3/authentication/verifyEmail/request",
        action: "CreateVerifyUserEmailRequest",
    },
    {
        method: "POST",
        route: "/v3/authentication/forgotPassword/request",
        action: "RequestForgotPasword",
    },
    {
        method: "POST",
        route: "/v3/authentication/forgotPassword/verify",
        action: "VerifyForgotPasswordRequest",
    },
    {
        method: "POST",
        route: "/v3/authentication/forgotPassword/reset",
        action: "ResetPassword",
    },
    {
        method: "GET",
        route: "/v3/authentication/users/:userId/devices",
        action: "ListUserLoggedinDevices",
    },
    {
        method: "GET",
        route: "/v3/authentication/organizations/:organizationId/users/:userId/devices",
        action: "ListUserLoggedinDevicesOfOrganization",
    },
    {
        method: "PUT",
        route: "/v3/authentication/users/:userId/devices/logout",
        action: "LogoutUserFromLoggedinDevices",
    },
    {
        method: "PUT",
        route: "/v3/authentication/organizations/:organizationId/users/:userId/devices/logout",
        action: "LogoutUserFromLoggedinDevicesOfOrganization",
    },
    {
        method: "GET",
        route: "/v3/authentication/users/:userId/mfa/enrollments",
        action: "ListUserMFAEnrollments",
    },
    {
        method: "GET",
        route: "/v3/authentication/organizations/:organizationId/users/:userId/mfa/enrollments",
        action: "ListUserMFAEnrollmentsOfOrganization",
    },
    {
        method: "DELETE",
        route: "/v3/authentication/users/:userId/mfa/enrollments",
        action: "ResetUserMFAEnrollments",
    },
    {
        method: "DELETE",
        route: "/v3/authentication/organization/:organizationId/users/:userId/mfa/enrollments",
        action: "ResetUserMFAEnrollmentsOfOrganization",
    },
    {
        method: "PUT",
        route: "/v3/authentication/users/:userId/password",
        action: "UpdateUserPassword",
    },
    {
        method: "PUT",
        route: "/v3/authentication/users/:userId/email",
        action: "UpdateUserEmail",
    },
    {
        method: "PUT",
        route: "/v3/authentication/organizations/:organizationId/users/:userId/email",
        action: "UpdateUserEmailOfOrganization",
    },
    {
        method: "GET",
        route: "/v3/authentication/logs",
        action: "ListAuthenticationLogs",
    },
    {
        method: "POST",
        route: "/v3/authentication/auth0/logs-webhook",
        action: "Auth0LogsWebhook",
    },
    {
        method: "PUT",
        route: "/v3/authentication/organizations/:organizationId/users/:userId/unlock",
        action: "UnlockUserOfOrganization",
    },
    {
        method: "GET",
        route: "/v3/authentication/organizations/connections/types",
        action: "ListEnterpriseConnectionTypes",
    },
    {
        method: "GET",
        route: "/v1/authentication/organizations/connections/fields",
        action: "ListEnterpriseConnectionTypeFields",
    },
    {
        method: "POST",
        route: "/v1/authentication/eph-files",
        action: "UploadTemporaryFiles",
    },
    {
        method: "POST",
        route: "/v1/authentication/connections",
        action: "CreateOragnizationConnection",
    },
    {
        method: "PUT",
        route: "/v1/authentication/connections/:connectionId",
        action: "UpdateOrganizationConnection",
    },
    {
        method: "DELETE",
        route: "/v1/authentication/connections/:connectionId",
        action: "RemoveOrganizationConnection",
    },
    {
        method: "GET",
        route: "/v1/authentication/connections/:connectionId",
        action: "GetOrganizationConnectionDetails",
    },
    {
        method: "GET",
        route: "/v1/authentication/connections",
        action: "ListOrganizationConnections",
    },
    {
        method: "GET",
        route: "/v1/authentication/connections/files/:fileId",
        action: "DownloadOrganizationConnectionFile",
    },
    {
        method: "GET",
        route: "/v3/authentication/users/actionToken",
        action: "GetUserActionToken",
    },
    {
        method: "DELETE",
        route: "/v3/authentication/organizations/:organizationId/users/:userId",
        action: "RemoveOrganizationUser",
    },
    {
        method: "GET",
        route: "/v3/authentication/logs/facets",
        action: "ListAuthenticationLogsFacets",
    },
    {
        method: "POST",
        route: "/v3/authentication/login",
        action: "Login",
    },
    {
        method: "GET",
        route: "/v3/authentication/redirect",
        action: "Redirect",
    },
    {
        method: "GET",
        route: "/v3/authentication/refreshToken",
        action: "RefreshToken",
    },
    {
        method: "POST",
        route: "/v3/authentication/verifyCode",
        action: "VerifyCode",
    },
    {
        method: "POST",
        route: "/v3/authentication/organizations",
        action: "CreateOrganization",
    },
    {
        method: "PUT",
        route: "/v3/authentication/organizations/:organizationId",
        action: "UpdateOrganization",
    },
    {
        method: "PUT",
        route: "/v3/authentication/organizations/:organizationId/users/:userId",
        action: "UpdateUserProfileOfAnOrganization",
    },
    {
        method: "PUT",
        route: "/v3/authentication/organizations/:organizationId/users/status",
        action: "UpdateUsersStatusOfAnOrganization",
    },
    {
        method: "PUT",
        route: "/v3/authentication/users/:userId",
        action: "UpdateUserDetails",
    },
    {
        method: "PUT",
        route: "/v3/authentication/users/status",
        action: "UpdateUsersStatus",
    },
    {
        method: "POST",
        route: "/v3/authentication/setPassword/verify",
        action: "VerifySetUserPasswordRequest",
    },
    {
        method: "POST",
        route: "/v3/authentication/recaptcha/latest",
        action: "ValidateReCaptchaV3",
    },
    {
        method: "POST",
        route: "/v3/authentication/recaptcha/traditional",
        action: "ValidateReCaptchaV2",
    },
    {
        method: "GET",
        route: "/v3/authentication/logout",
        action: "LogOut"
    }
];
