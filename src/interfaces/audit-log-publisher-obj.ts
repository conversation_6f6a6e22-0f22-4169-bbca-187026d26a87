import { TargetType } from "../enums/target-type";
import { initiatorPermissionLogObj } from "./initiator-permission-log-obj";
import { modifiedProperties } from "./modified-properties";
import { requestLogObj } from "./request-log-obj";
import { responseLogObj } from "./response-log-obj";

export interface auditLogPublisherObj {
    request: requestLogObj,
    response: responseLogObj,
    initiatorPermission: initiatorPermissionLogObj,
    targets: {
        type: TargetType,
        details: {
            [key: string]: string
        }
    }[],
    modifiedProperties: modifiedProperties[]
}
