import { ValueTypesEnum } from "../enums/value-types.enum";

export interface FilterFields {
    displayName?: string,
    name: string,
    isFlexibleField: boolean,
    key?: string | null,
    allowedFilters: {label: string, value: string}[],
    type: ValueTypesEnum,
    assetStatusField?: boolean,
    ownerField?: boolean,
    milestoneField?: boolean,
    categoryType?: boolean,
    values?: {id: string, value: string, count?: number}[]  | null,
    ignoreSanitize?: boolean,
    trackerObjectives?: boolean,
    trackerClients?: boolean,
    organizationFlexibleField?: boolean,
    isQuickFilter?: boolean,
    isBooleanField?: boolean,
    moxIds?: boolean,
    engagedByMoxfive?: boolean
}
