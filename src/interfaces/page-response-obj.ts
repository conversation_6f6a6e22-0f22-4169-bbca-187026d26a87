import { FilterFields } from "./filter-fields";

export interface PageResponseObj {
    data: any[];
    meta: {
        totalCount: number;
        rowsPerPage: number;
        totalPages: number;
        currentPage: number;
        firstPageLink: string;
        lastPageLink: string;
        previousPageLink: string;
        nextPageLink: string;
        facets: FilterFields[];
        appliedFilters: { [key: string]: any };
        additionalAttributes: { [key: string]: any };
    }
}
