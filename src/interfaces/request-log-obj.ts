export interface requestLogObj {
    requestId: string,
    timestamp: number,
    method: string,
    api: string,
    userId: string,
    m5pTenantId: string,
    applicationId: string | null,
    ip: string,
    headers: {
        userAgent: string,
        referer: string,
        origin: string,
        acceptEncoding: string,
        xM5PHostname: string,
        xM5PHostip: string,
        contentType: string,
        contentLength: string,
        accept: string
    },
    body: string,
    pathParams: {
        [key: string]: string
    },
    queryParams: {
        [key: string]: string
    }
}
