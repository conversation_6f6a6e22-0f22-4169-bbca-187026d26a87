import mongoose from "mongoose";

// An interface that describes the properties to define a new File
interface TemporaryFileAttrs {
    fileName: string;
    originalFileName: string;
    parent: string;
    uploadedBy: string;
}

// An interface that describes the properties
interface TemporaryFileDoc extends mongoose.Document {
    id: string;
    fileName: string;
    originalFileName: string;
    parent: string;
    uploadedBy: string;
    uploadedOn: string;
}

// An interface that describes the properties that a File model has
interface TemporaryFileModel extends mongoose.Model<TemporaryFileDoc> {
    build(attrs: TemporaryFileAttrs): TemporaryFileDoc
}

const temporaryFileSchema = new mongoose.Schema(
    {
        fileName: {
            type: String,
            unique: true,
            required: true
        },
        originalFileName: {
            type: String,
            required: true
        },
        parent: {
            type: String,
            required: true
        },
        uploadedBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true
        },
        uploadedOn: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        }
    }
);

temporaryFileSchema.statics.build = (attrs: TemporaryFileAttrs) => {
    return new TemporaryFile(attrs);
};

const TemporaryFile = mongoose.model<TemporaryFileDoc, TemporaryFileModel>("TemporaryFile", temporaryFileSchema);

export { TemporaryFile, TemporaryFileDoc };
