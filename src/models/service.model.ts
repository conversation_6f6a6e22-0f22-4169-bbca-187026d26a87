import mongoose from "mongoose";

// An interface that describes the properties that Service Document has
interface ServiceDoc extends mongoose.Document {
    id: string,
    name: string,
    displayName?: string,
    description: string,
    eligiblePolicyTypes: string[]
}

const ServiceSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true,
        },
        description: {
            type: String,
            required: false,
        },
        eligiblePolicyTypes: {
            type: [String],
            required: true
        }
    },
    {
        collection: "services",
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

ServiceSchema.index({ organizationTypeIds: 1 });
ServiceSchema.index({ parentId: 1 });
ServiceSchema.index({ name: 1 }, { unique: true });

const Service = mongoose.model<ServiceDoc>("CommonService", ServiceSchema);

export { Service, ServiceDoc };
