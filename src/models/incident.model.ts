import mongoose from "mongoose";

// An interface that describes the properties that Incident Document has
interface IncidentDoc extends mongoose.Document {
  id: string,
  published: boolean,
  members: string[],
  policyIds: string[],
  version: number
}

const incidentSchema = new mongoose.Schema(
    {
        published: {
            type: Boolean,
            default: false
        },
        members: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "User",
            default: []
        },
        policyIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "Policy",
            default: []
        }
    },
    {
        collection: "incidents"
    }
);

const Incident = mongoose.model<IncidentDoc>("CommonIncident", incidentSchema);

export { Incident, IncidentDoc };
