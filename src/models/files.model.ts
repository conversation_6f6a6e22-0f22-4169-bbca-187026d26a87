import mongoose from "mongoose";

// An interface that describes the properties to define a new File
interface FileAttrs {
    fileName: string;
    originalFileName: string;
    parent: string;
    parentId: string;
    entity: string;
    entityId: string;
    uploadedBy: string;
    uploadedOn?: string;
}

// An interface that describes the properties
interface FileDoc extends mongoose.Document {
    id: string;
    fileName: string;
    originalFileName: string;
    parent: string;
    parentId: string;
    entity: string;
    entityId: string;
    uploadedBy: string;
    uploadedOn: string;
}

// An interface that describes the properties that a File model has
interface FileModel extends mongoose.Model<FileDoc> {
    build(attrs: FileAttrs): FileDoc
}

const fileSchema = new mongoose.Schema(
    {
        fileName: {
            type: String,
            unique: true,
            required: true
        },
        originalFileName: {
            type: String,
            required: true
        },
        parent: {
            type: String,
            required: true
        },
        parentId: {
            type: String,
            required: true
        },
        entity: {
            type: String,
            required: true
        },
        entityId: {
            type: String,
            required: true
        },
        uploadedBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true
        },
        uploadedOn: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        }
    }
);

fileSchema.statics.build = (attrs: FileAttrs) => {
    return new File(attrs);
};

const File = mongoose.model<FileDoc, FileModel>("File", fileSchema);

export { File, FileDoc };
