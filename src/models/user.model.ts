import mongoose from "mongoose";
import { ApplicationPolicyIdObj } from "../interfaces";

// An interface that describes the properties
// that a Role Document has
interface UserDoc extends mongoose.Document {
    firstName: string,
    lastName: string,
    displayName: string,
    email: string;
    policyIds: string[];
    isEnabled: boolean;
    applicationPolicyIds: ApplicationPolicyIdObj[] | [],
    organizationId: string;
    allowedLoginTokens: string[]
}

const applicationPolicySchema = new mongoose.Schema({
    applicationType: {
        type: String,
        enum: ["Incident", "Resilience"],
        required: true
    },
    applicationId: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "applicationType"
    },
    policyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "CommonPolicy"
    }
}, { _id: false });

const userSchema = new mongoose.Schema({
    firstName: {
        type: String,
        default: null
    },
    lastName: {
        type: String,
        default: null
    },
    displayName: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true
    },
    isEnabled: {
        type: Boolean,
        required: true
    },
    policyIds: {
        type: [mongoose.Schema.Types.ObjectId],
        required: true,
        ref: "CommonPolicy"
    },
    applicationPolicyIds: {
        type: [applicationPolicySchema],
        default: []
    },
    organizationId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: "CommonOrganization"
    },
    allowedLoginTokens: {
        type: [mongoose.Schema.Types.ObjectId],
        default: []
    },
}, {
    collection: "users"
});

const User = mongoose.model<UserDoc>("CommonUser", userSchema);
export { User, UserDoc };
