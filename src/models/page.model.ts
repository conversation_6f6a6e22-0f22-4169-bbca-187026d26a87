import mongoose from "mongoose";

interface PageAttrs {
    rows: string;
    count: number;
}

// An interface that describes the properties
interface PageDoc extends mongoose.Document {
    id: string;
    rows: string;
    count: number;
    createdOn: Date;
}

const pageSchema = new mongoose.Schema(
    {
        rows: {
            type: String,
            required: true
        },
        count: {
            type: Number,
            required: true
        },
        createdOn: {
            type: Date,
            required: true,
            default: Date.now
        }
    },
    {
        versionKey: false
    }
);

pageSchema.index({ createdOn: 1 }, { expireAfterSeconds: 1 * 24 * 60 * 60 });

const Page = mongoose.model<PageDoc>("Page", pageSchema);

export { Page, PageAttrs, PageDoc };
