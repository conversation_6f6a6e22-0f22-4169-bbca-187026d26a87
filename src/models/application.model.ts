import mongoose from "mongoose";
import { referenceValueMongooseSchemaV2 } from "../utils/reference-value-mongoose-schema-v2";
import { ReferenceValueV2 } from "../interfaces";

// An interface that describes the properties that are required to create a new Action
interface ApplicationtAttrs {
    user: ReferenceValueV2,
    organization: ReferenceValueV2,
    isEnabled?: boolean,
    isDeleted?: boolean,
    expiresIn: string,
}

// An interface that describes the properties that a Action Document has
interface ApplicationtDoc extends mongoose.Document {
    id: string,
    user: ReferenceValueV2,
    organization: ReferenceValueV2,
    isEnabled: boolean,
    isDeleted: boolean,
    expiresIn: string,
    version: number
}

// An interface that describes the properties that a Action Model has
interface ApplicationtModel extends mongoose.Model<ApplicationtDoc> {
    build(attrs: ApplicationtAttrs): ApplicationtDoc
}

const applicationSchema = new mongoose.Schema(
    {
        user: {
            type: referenceValueMongooseSchemaV2("users"),
            required: true
        },
        organization: {
            type: referenceValueMongooseSchemaV2("organizations"),
            required: true
        },
        isEnabled: {
            type: Boolean,
            default: false
        },
        isDeleted: {
            type: Boolean,
            default: false
        },
        expiresIn: {
            type: Date,
            required: true
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
        collection: "applications"
    }
);

applicationSchema.set("versionKey", "version");

applicationSchema.statics.build = (attrs: ApplicationtAttrs) => {
    return new Application(attrs);
};

const Application = mongoose.model<ApplicationtDoc, ApplicationtModel>("ApplicationClient", applicationSchema);

export { Application, ApplicationtDoc, ApplicationtAttrs };
