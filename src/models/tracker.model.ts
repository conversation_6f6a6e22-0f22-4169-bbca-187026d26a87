import mongoose from "mongoose";

// An interface that describes the properties that Tracker Document has
interface TrackerDoc extends mongoose.Document {
  id: string,
  members: string[],
  policyIds: string[],
  version: number
}

const trackerSchema = new mongoose.Schema(
    {
        members: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "User",
            default: []
        },
        policyIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "Policy",
            default: []
        }
    },
    {
        collection: "trackers"
    }
);

const Tracker = mongoose.model<TrackerDoc>("CommonTracker", trackerSchema);

export { Tracker, TrackerDoc };
