import mongoose from "mongoose";

// An interface that describes the properties
// that a Role Document has
interface OrganizationDoc extends mongoose.Document {
    id: string;
    version: number;
    isEnabled: boolean;
    name: string;
}

const organizationSchema = new mongoose.Schema({
    isEnabled: {
        type: Boolean,
        required: true
    },
    name: {
        type: String,
        required: true
    },
}, {
    collection: "organizations"
});

const Organization = mongoose.model<OrganizationDoc>("CommonOrganization", organizationSchema);
export { Organization, OrganizationDoc };
