import mongoose from "mongoose";

// An interface that describes the properties
// that a Permission Document has
interface ActionDoc extends mongoose.Document {
    _id: string;
    id: string;
    name: string;
    description: string;
    isEnabled: boolean;
    ruleIds: string[];
    serviceId: string;
    isModuleLevel: boolean;
    accessControlId: string;
    moxfiveExclusive: boolean;
    requireAuthorization: boolean;
}

const ActionSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        description: {
            type: String
        },
        isEnabled: {
            type: Boolean,
            default: true,
            required: true
        },
        ruleIds: {
            type: [mongoose.Types.ObjectId],
            ref: "Rule",
            required: true
        },
        serviceId: {
            type: mongoose.Types.ObjectId,
            ref: "Service",
            required: true
        },
        isModuleLevel: {
            type: Boolean,
            required: true
        },
        accessControlId: {
            type: mongoose.Types.ObjectId,
            ref: "AccessControl",
            required: true
        },
        moxfiveExclusive: {
            type: Boolean
        },
        requireAuthorization: {
            type: Boolean,
            default: true
        }
    },
    {
        collection: "actions"
    }
);

const Action = mongoose.model<ActionDoc>("CommonActions", ActionSchema);

export { Action };
