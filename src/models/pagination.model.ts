import mongoose from "mongoose";
import { PaginationEntity } from "../enums/pagination-entity";

interface PaginationAttrs {
    entity: PaginationEntity;
    totalCount: number;
    rowsPerPage: number;
    totalPages: number;
    facets: string;
    appliedFilters: string;
    additionalAttributes: string;
    pageLinks: string[];
    createdBy: string;
}

// An interface that describes the properties
interface PaginationDoc extends mongoose.Document {
    id: PaginationEntity;
    entity: string;
    totalCount: number;
    rowsPerPage: number;
    totalPages: number;
    facets: string;
    appliedFilters: string;
    additionalAttributes: string;
    pageLinks: string[];
    createdBy: string;
    createdOn: Date;
}

// An interface that describes the properties that a Pagination model has

interface PaginationModel extends mongoose.Model<PaginationDoc> {
    build(attrs: PaginationAttrs): PaginationDoc
}

const paginationSchema = new mongoose.Schema(
    {
        entity: {
            type: String,
            required: true
        },
        totalCount: {
            type: Number,
            required: true
        },
        rowsPerPage: {
            type: Number,
            required: true
        },
        totalPages: {
            type: Number,
            required: true
        },
        facets: {
            type: String,
            required: true
        },
        appliedFilters: {
            type: String,
            required: true
        },
        additionalAttributes: {
            type: String,
            required: true
        },
        pageLinks: {
            type: [String],
            required: true
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true
        },
        createdOn: {
            type: Date,
            required: true,
            default: Date.now
        }
    },
    {
        versionKey: false
    }
);

paginationSchema.statics.build = (attrs: PaginationAttrs) => {
    return new Pagination(attrs);
};

paginationSchema.index({ createdOn: 1 }, { expireAfterSeconds: 1 * 24 * 60 * 60 });

const Pagination = mongoose.model<PaginationDoc, PaginationModel>("Pagination", paginationSchema);

export { Pagination, PaginationAttrs, PaginationDoc };

