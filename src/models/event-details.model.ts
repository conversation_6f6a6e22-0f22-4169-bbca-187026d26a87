import mongoose from "mongoose";

interface EventDetailsAttrs {
    subject: string,
    producers: string[],
    consumers: string[]
}

interface EventDetailsDoc extends mongoose.Document {
    subject: string,
    producers: string[],
    consumers: string[]
}

interface EventDetailsModel extends mongoose.Model<EventDetailsDoc> {
    build(attrs: EventDetailsAttrs): EventDetailsDoc
}

const eventDetailsSchema = new mongoose.Schema(
    {
        subject: {
            type: String,
            unique: true,
            required: true
        },
        producers: {
            type: [String],
            required: true
        },
        consumers: {
            type: [String],
            required: true
        }
    },
    {
        collection: "eventdetails"
    }
);

eventDetailsSchema.statics.build = (attrs: EventDetailsAttrs) => {
    return new EventDetails(attrs);
};

const EventDetails = mongoose.model<EventDetailsDoc, EventDetailsModel>("CommonEventDetails", eventDetailsSchema);

export { EventDetails, EventDetailsDoc, EventDetailsAttrs };
