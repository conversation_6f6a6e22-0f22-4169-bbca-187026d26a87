import mongoose from "mongoose";

// An interface that describes the properties that Incident Document has
interface IncidentV2Doc extends mongoose.Document {
  id: string,
  published: boolean,
  members: string[],
  policyIds: string[],
  version: number
}

const incidentV2Schema = new mongoose.Schema(
    {
        published: {
            type: Boolean,
            default: false
        },
        members: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "User",
            default: []
        },
        policyIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "Policy",
            default: []
        }
    },
    {
        collection: "incidentsv2"
    }
);

const IncidentV2 = mongoose.model<IncidentV2Doc>("CommonIncidentV2", incidentV2Schema);

export { IncidentV2, IncidentV2Doc };
