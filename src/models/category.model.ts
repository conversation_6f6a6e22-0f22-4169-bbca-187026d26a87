import mongoose from "mongoose";

// An interface that describes the properties that are required to create a new Action
interface CategoryAttrs {
    name: string;
    actionIds: string[];
    serviceId: string;
}

// An interface that describes the properties that a Action Document has
interface CategoryDoc extends mongoose.Document {
    id: string;
    name: string;
    actionIds: string[];
    serviceId: string;
}

// An interface that describes the properties that a Action Model has
interface CategoryModel extends mongoose.Model<CategoryDoc> {
  build(attrs: CategoryAttrs): CategoryDoc
}

const categorySchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true
        },
        serviceId: {
            type: mongoose.Types.ObjectId,
            ref: "Service",
            required: true
        },
        actionIds: {
            type: [mongoose.Types.ObjectId],
            ref: "Action",
            required: true
        },
    },
    {
        collection: "categories"
    }
);

categorySchema.index({ actionIds: 1 });

const Category = mongoose.model<CategoryDoc, CategoryModel>("CommonCategory", categorySchema);

export { Category, CategoryDoc, CategoryAttrs };
