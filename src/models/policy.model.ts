import mongoose from "mongoose";

// An interface that describes the properties that a Role Document has
interface PolicyDoc extends mongoose.Document {
    _id: any;
    name: string;
    type: "Global" | "Application";
    isEnabled: boolean;
    actionIds: string[];
    version: string;
}

const policySchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        isEnabled: {
            type: Boolean,
            required: true
        },
        type: {
            type: String,
            required: true
        },
        actionIds: {
            type: [mongoose.Schema.Types.ObjectId],
            required: true,
            ref: "CommonActions"
        }
    },
    {
        collection: "policies"
    }
);

const Policy = mongoose.model<PolicyDoc>("CommonPolicy", policySchema);

export { Policy };
