import mongoose from "mongoose";

// An interface that describes the properties
// that a Role Document has
interface UserDoc extends mongoose.Document {
    email: string;
}

const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true
    }
}, {
    collection: "localusers"
});

const LocalUser = mongoose.model<UserDoc>("CommonLocalUser", userSchema);
export { LocalUser };
