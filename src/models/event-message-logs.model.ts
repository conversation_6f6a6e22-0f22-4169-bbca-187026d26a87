import mongoose from "mongoose";
import { EventTypeEnum } from "../enums/event-type";

interface EventMessageLogsAttrs {
    type : EventTypeEnum,
    subject: string,
    metadata: any,
    succeeded: boolean,
    error?: any,
    producer?: string | null,
    consumers?: string[] | null
}

interface EventMessageLogsDoc extends mongoose.Document {
    id: string,
    type : EventTypeEnum,
    subject: string,
    metadata: any,
    succeeded: boolean,
    error: any | null,
    producer: string | null,
    consumers: string[] | null,
    createdAt: string,
    _ts: string
}

interface EventMessageLogsModel extends mongoose.Model<EventMessageLogsDoc> {
    build(attrs: EventMessageLogsAttrs): EventMessageLogsDoc
}

const eventMessageLogsSchema = new mongoose.Schema(
    {
        type: {
            type: String,
            required: true
        },
        subject: {
            type: String,
            required: true
        },
        metadata: {
            type: mongoose.Schema.Types.Mixed,
            default: ""
        },
        succeeded: {
            type: Boolean,
            required: true
        },
        error: {
            type: mongoose.Schema.Types.Mixed,
            default: null
        },
        producer: {
            type: String,
            default: null
        },
        consumers: {
            type: [String],
            default: null,
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        _ts: {
            type: Date,
            default: Date.now
        }
    },
    {
        collection: "eventmessagelogs"
    }
);

eventMessageLogsSchema.statics.build = (attrs: EventMessageLogsAttrs) => {
    return new EventMessageLogs(attrs);
};

eventMessageLogsSchema.index({ createdAt: 1 });
eventMessageLogsSchema.index({ type: 1 });
eventMessageLogsSchema.index({ type: 1, succeeded: -1 });

const EventMessageLogs = mongoose.model<EventMessageLogsDoc, EventMessageLogsModel>("CommonEventMessageLogs", eventMessageLogsSchema);

export { EventMessageLogs, EventMessageLogsDoc, EventMessageLogsAttrs };
