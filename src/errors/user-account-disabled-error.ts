import { CustomError } from "./custom-error";

export class UserAccountDisabledError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, UserAccountDisabledError.prototype);
    }

    serializeErrors() {
        return {
            title: "User Account Disabled",
            detail: "Your account is disabled and is not permitted to access this feature.",
            type: "userAccountDisabled",
            code: 4032
        };
    }
}
