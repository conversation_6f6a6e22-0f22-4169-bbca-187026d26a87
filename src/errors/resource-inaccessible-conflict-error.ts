import { CustomError } from "./custom-error";

export class ResourceInaccessibleBadRequestError extends CustomError {
    statusCode = 409;

    constructor(public message: string) {
        super();

        Object.setPrototypeOf(this, ResourceInaccessibleBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Inaccessible",
            detail: this.message,
            type: "resourceInaccessible",
            code: 4098
        };
    }
}
