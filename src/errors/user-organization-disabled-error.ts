import { CustomError } from "./custom-error";

export class UserOrganizationDisabledError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, UserOrganizationDisabledError.prototype);
    }

    serializeErrors() {
        return {
            title: "User Organization Disabled",
            detail: "Your account is disabled and is not permitted to access this feature.",
            type: "userOrganizationDisabled",
            code: 4033
        };
    }
}
