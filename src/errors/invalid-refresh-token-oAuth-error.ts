import { CustomError } from "./custom-error";

export class InvalidRefreshTokenOAuthError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidRefreshTokenOAuthError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Refresh Token",
            detail: "The token request is malformed or missing required parameters.",
            type: "invalid_refresh_token",
            code: 4015
        };
    }
}
