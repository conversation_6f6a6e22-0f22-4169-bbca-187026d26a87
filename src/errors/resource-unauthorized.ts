import { CustomError } from "./custom-error";

export class ResourceUnauthorizedError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, ResourceUnauthorizedError.prototype);
    }

    serializeErrors() {
        return {
            errors: [{
                "type": "unauthorizedAppAccess",
                "message": "User not found in MOXFIVE Platform."
            }],
            title: "Resource Unauthorized",
            detail: "Couldn't authenticate you.",
            type: "resourceUnauthorized",
            code: 4011
        };
    }
}
