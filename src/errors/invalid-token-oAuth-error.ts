import { CustomError } from "./custom-error";

export class InvalidTokenOAuthError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidTokenOAuthError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Token",
            detail: "The token request is malformed or missing required parameters.",
            type: "invalid_token",
            code: 4013
        };
    }
}
