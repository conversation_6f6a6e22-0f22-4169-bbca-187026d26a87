import { CustomError } from "./custom-error";

export class InvalidFileError extends CustomError {
    statusCode = 400;

    constructor(public message: string) {
        super();

        Object.setPrototypeOf(this, InvalidFileError.prototype);
    }

    serializeErrors() {
        return {
            "title": "Invalid File",
            "detail": this.message || "Please upload a valid file.",
            "type": "invalidFile",
            "code": 4006
        };
    }
}
