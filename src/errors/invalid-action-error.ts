import { CustomError } from "./custom-error";

export class InvalidActionError extends CustomError {
    statusCode = 403;

    constructor(public message: string) {
        super();

        Object.setPrototypeOf(this, InvalidActionError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Action",
            detail: this.message,
            type: "invalidAction",
            code: 4035
        };
    }
}
