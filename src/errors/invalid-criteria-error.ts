import { CustomError } from "./custom-error";

export class InvalidCriteriaBadRequestError extends CustomError {
    statusCode = 400;

    constructor(public criterias: string[]) {
        super();

        Object.setPrototypeOf(this, InvalidCriteriaBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Criteria",
            detail: "Requested Criteria are invalid.",
            type: "invalidCriteria",
            code: 40011,
            errors: [{
                parameters: [{
                    criteria: this.criterias,
                    message: "The specified criteria for the requested rule are invalid."
                }]
            }]
        };
    }
}
