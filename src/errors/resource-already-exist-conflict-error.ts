import { CustomError } from "./custom-error";

export class ResourceAlreadyExistBadRequestError extends CustomError {
    statusCode = 409;

    constructor(public name: string, public value: string, public message: string) {
        super();

        Object.setPrototypeOf(this, ResourceAlreadyExistBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Already Exist",
            detail: "The requested resource is already exist.",
            type: "resourceAlreadyExist",
            code: 4094,
            errors: [{
                parameters: [{
                    name: this.name,
                    value: this.value,
                    message: this.message
                }]
            }]
        };
    }
}
