import { CustomError } from "./custom-error";

export class OwnerMustExistBadRequestError extends CustomError {
    statusCode = 409;

    constructor() {
        super();

        Object.setPrototypeOf(this, OwnerMustExistBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "One Owner Must Exist",
            detail: "All the owners cannot be removed from organization. Each orgnization must have at least one owner assigned.",
            type: "oneOwnerMustExist",
            code: 4092
        };
    }
}
