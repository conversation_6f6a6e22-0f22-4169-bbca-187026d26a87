import { CustomError } from "./custom-error";

export class InvalidCredentialsError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidCredentialsError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Credentials Error",
            detail: "Email and/or password is incorrect. <NAME_EMAIL>",
            type: "invalidCredentialsError",
            code: 4016
        };
    }
}
