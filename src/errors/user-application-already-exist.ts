import { CustomError } from "./custom-error";

export class UserApplicationAlreadyExistBadRequestError extends CustomError {
    statusCode = 409;

    constructor() {
        super();

        Object.setPrototypeOf(this, UserApplicationAlreadyExistBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Duplicate Application Creation",
            detail: "Duplicate application cannot be created.",
            type: "duplicate_application_creation",
            code: 40948
        };
    }
}
