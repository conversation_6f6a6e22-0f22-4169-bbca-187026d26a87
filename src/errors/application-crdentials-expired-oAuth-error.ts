import { CustomError } from "./custom-error";

export class ApplicationCredentialsExpiredOAuthError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, ApplicationCredentialsExpiredOAuthError.prototype);
    }

    serializeErrors() {
        return {
            title: "Credentials Expired",
            detail: "Application credentials have expired.",
            type: "credentials_expired",
            code: 4015
        };
    }
}
