import { ConflictErrorCodes } from "../utils/conflict-error-codes";
import { CustomError } from "./custom-error";

export class CommonAlreadyExistConflictError extends CustomError {
    statusCode = 409;

    constructor(public options: {code: ConflictErrorCodes, title: string, type: string, message: string}) {
        super();

        Object.setPrototypeOf(this, CommonAlreadyExistConflictError.prototype);
    }

    serializeErrors() {
        return {
            title: this.options.title,
            detail: this.options.message,
            type: this.options.type,
            code: this.options.code
        };
    }
}
