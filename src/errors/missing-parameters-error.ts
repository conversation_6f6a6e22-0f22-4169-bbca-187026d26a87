import { CustomError } from "./custom-error";

export class MissingParametersError extends CustomError {
    statusCode = 422;

    constructor(public errors: string[], public message: string = "") {
        super();

        Object.setPrototypeOf(this, MissingParametersError.prototype);
    }

    serializeErrors() {
        return {
            title: "Missing Parameters",
            detail: "One or more required parameters are missing.",
            type: "missingParameters",
            code: 4221,
            errors: [{
                parameters: [{
                    attributes: this.errors,
                    message: this.message || "These paramters are missing."
                }]
            }]
        };
    }
}
