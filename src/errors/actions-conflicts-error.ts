import { CustomError } from "./custom-error";

export class ActionsConflictsError extends CustomError {
    statusCode = 409;

    constructor(public actions: string[]) {
        super();

        Object.setPrototypeOf(this, ActionsConflictsError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Value Unacceptable",
            detail: "Application Type policy cannot contain the permissions of services exclusive to Global Type.",
            type: "resourceValueUnacceptable",
            code: 4099,
            errors: [{
                parameters: [{
                    attributes: this.actions,
                    message: "These actions are invalid for Application Type Policy."
                }]
            }]
        };
    }
}
