import { GraphParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class ExternalServerError extends CustomError {
    statusCode = 500;

    constructor(public errors: GraphParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, ExternalServerError.prototype);
    }

    serializeErrors() {
        return {
            title: "External Server Error",
            detail: "Sorry something went wrong, please try again. If problem persist then write <NAME_EMAIL>.",
            type: "externalServerError",
            code: 5001,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
