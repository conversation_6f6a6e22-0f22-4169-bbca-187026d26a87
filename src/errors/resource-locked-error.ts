import { CustomError } from "./custom-error";

export class ResourceLockedError extends CustomError {
    statusCode = 423;

    constructor(public message: string, public code: number) {
        super();

        Object.setPrototypeOf(this, ResourceLockedError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Locked Error",
            detail: this.message,
            type: "resourceLockedError",
            code: this.code
        };
    }
}
