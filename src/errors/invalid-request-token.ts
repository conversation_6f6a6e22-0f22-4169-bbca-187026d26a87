import { CustomError } from "./custom-error";

export class InvalidRequestTokenError extends CustomError {
    statusCode = 400;

    constructor(public message: string, public code: number) {
        super();

        Object.setPrototypeOf(this, InvalidRequestTokenError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Request Token Error",
            detail: this.message,
            type: "invalidRequestTokenError",
            code: this.code
        };
    }
}
