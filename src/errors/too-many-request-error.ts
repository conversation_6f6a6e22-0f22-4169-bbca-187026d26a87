import { CustomError } from "./custom-error";

export class TooManyRequestsError extends CustomError {
    statusCode = 429;

    constructor(public message: string, public code: number) {
        super();

        Object.setPrototypeOf(this, TooManyRequestsError.prototype);
    }

    serializeErrors() {
        return {
            title: "Too Many Requests Error",
            detail: this.message,
            type: "tooManyRequestsError",
            code: this.code
        };
    }
}
