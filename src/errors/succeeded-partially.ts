import { errorResponseObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class SucceededPartially extends CustomError {
    statusCode = 200;

    constructor(public errors: errorResponseObj["errors"], public errorDetailMessage: string) {
        super();

        Object.setPrototypeOf(this, SucceededPartially.prototype);
    }

    serializeErrors() {
        return {
            title: "Succeeded Partially",
            detail: this.errorDetailMessage,
            type: "succeededPartially",
            code: 2001,
            errors: this.errors
        };
    }
}
