import { CustomError } from "./custom-error";

export class InvalidTriggerBadRequestError extends CustomError {
    statusCode = 400;

    constructor(public triggers: string[]) {
        super();

        Object.setPrototypeOf(this, InvalidTriggerBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Trigger",
            detail: "Requested Trigger is invalid.",
            type: "invalidTrigger",
            code: 40010,
            errors: [{
                parameters: [{
                    triggers: this.triggers,
                    message: "The specified trigger for the requested rule is invalid."
                }]
            }]
        };
    }
}
