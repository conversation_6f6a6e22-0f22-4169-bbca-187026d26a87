import { InvalidBodyParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class InvalidNotificationError extends CustomError {
    statusCode = 400;

    constructor(public errors: InvalidBodyParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, InvalidNotificationError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Notification ID",
            detail: "Requested notification id not found.",
            type: "invalidNotificationId",
            code: 4007,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
