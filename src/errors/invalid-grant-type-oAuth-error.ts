import { CustomError } from "./custom-error";

export class InvalidGrantTypeOAuthError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidGrantTypeOAuthError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Grant Type",
            detail: "The requested grant type is not supported.",
            type: "invalid_grant_type",
            code: 4036
        };
    }
}
