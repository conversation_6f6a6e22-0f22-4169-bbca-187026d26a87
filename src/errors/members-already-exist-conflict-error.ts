import { CustomError } from "./custom-error";

export class MembersAlreadyExistBadRequestError extends CustomError {
    statusCode = 409;

    constructor(public members: string[]) {
        super();

        Object.setPrototypeOf(this, MembersAlreadyExistBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Memebers Already Exist",
            detail: "Requested members are already exist in the specified organization.",
            type: "memebersAlreadyExist",
            code: 4091,
            errors: [{
                parameters: [{
                    members: this.members,
                    message: "These members are already exist in the specified organization."
                }]
            }]
        };
    }
}
