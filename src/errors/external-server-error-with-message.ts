import { GraphParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class ExternalServerErrorWithMessage extends CustomError {
    statusCode = 500;

    constructor(public message: string, public errors?: GraphParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, ExternalServerErrorWithMessage.prototype);
    }

    serializeErrors() {
        return {
            title: "External Server Error",
            detail: this.message,
            type: "externalServerError",
            code: 5001,
            errors: (this.errors && this.errors.length) ? [{
                parameters: this.errors
            }] : []
        };
    }
}
