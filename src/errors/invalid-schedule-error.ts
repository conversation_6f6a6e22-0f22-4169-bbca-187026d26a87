import { InvalidBodyParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class InvalidScheduleError extends CustomError {
    statusCode = 400;

    constructor(public errors: InvalidBodyParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, InvalidScheduleError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Schedule",
            detail: "Requested schedule cannot be set.",
            type: "invalidSchedule",
            code: 4008,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
