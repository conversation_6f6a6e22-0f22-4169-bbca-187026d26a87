import { InvalidBodyParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class BodyInvalidBadRequestError extends CustomError {
    statusCode = 400;

    constructor(public errors: InvalidBodyParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, BodyInvalidBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Request",
            detail: "One or more request body parameters are invalid.",
            type: "invalidRequest",
            code: 4002,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
