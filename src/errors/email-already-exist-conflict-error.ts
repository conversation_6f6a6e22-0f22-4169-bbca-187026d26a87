import { CustomError } from "./custom-error";

export class EmailAlreadyExistBadRequestError extends CustomError {
    statusCode = 409;

    constructor() {
        super();

        Object.setPrototypeOf(this, EmailAlreadyExistBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Email Already Exist",
            detail: "The requested email is already exist.",
            type: "emailAlreadyExist",
            code: 4093
        };
    }
}
