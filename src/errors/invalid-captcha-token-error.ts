import { CustomError } from "./custom-error";

export class InvalidCaptchaTokenError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidCaptchaTokenError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Token",
            detail: "The captcha token is invalid.",
            type: "invalidCaptchaToken",
            code: 4038
        };
    }
}
