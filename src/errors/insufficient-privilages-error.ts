import { CustomError } from "./custom-error";

export class InsufficientPrivilagesError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, InsufficientPrivilagesError.prototype);
    }

    serializeErrors() {
        return {
            title: "Insufficient Privilages",
            detail: "Your account does not have sufficient privileges to perform this action.",
            type: "insufficientPrivilages",
            code: 4034
        };
    }
}
