import { NotFoundCode } from "../utils/not-found-codes";
import { CustomError } from "./custom-error";

export class ResourceNotFoundError extends CustomError {
    statusCode = 404;

    constructor(public code: NotFoundCode, public message: string) {
        super();

        Object.setPrototypeOf(this, ResourceNotFoundError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Not Found",
            detail: this.message,
            type: "resourceNotFound",
            code: this.code
        };
    }
}
