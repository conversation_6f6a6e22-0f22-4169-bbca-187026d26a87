import { CustomError } from "./custom-error";

export class UserMembershipAlreadyExistBadRequestError extends CustomError {
    statusCode = 409;

    constructor() {
        super();

        Object.setPrototypeOf(this, UserMembershipAlreadyExistBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "User Membership Already Exist",
            detail: "The user already has the specified membership with the given organization.",
            type: "userMembershipAlreadyExist",
            code: 4095
        };
    }
}
