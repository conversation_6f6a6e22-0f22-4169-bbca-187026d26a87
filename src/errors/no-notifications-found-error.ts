import { InvalidBodyParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class NoNotificationsFoundError extends CustomError {
    statusCode = 400;

    constructor(public errors: InvalidBodyParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, NoNotificationsFoundError.prototype);
    }

    serializeErrors() {
        return {
            title: "No Notifications Found",
            detail: "No notifications found.",
            type: "noNotificationsFound",
            code: 4009,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
