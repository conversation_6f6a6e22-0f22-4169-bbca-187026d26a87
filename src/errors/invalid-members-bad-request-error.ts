import { CustomError } from "./custom-error";

export class InvalidMembersBadRequestError extends CustomError {
    statusCode = 400;

    constructor(public members: string[]) {
        super();

        Object.setPrototypeOf(this, InvalidMembersBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Members",
            detail: "Requested members are already part of other organization.",
            type: "invalidMembers",
            code: 4001,
            errors: [{
                parameters: [{
                    members: this.members,
                    message: "Disallowed members. These members are already part of other organization."
                }]
            }]
        };
    }
}
