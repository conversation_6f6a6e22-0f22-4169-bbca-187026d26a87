import { CustomError } from "./custom-error";

export class InvalidApplicationCredentialsOAuthError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidApplicationCredentialsOAuthError.prototype);
    }

    serializeErrors() {
        return {
            title: "Authentication Failed",
            detail: "Invalid client ID or client secret.",
            type: "authentication_failed",
            code: 4014
        };
    }
}
