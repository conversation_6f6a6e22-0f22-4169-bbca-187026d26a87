import { CustomError } from "./custom-error";

export class InvalidRuleActionBadRequestError extends CustomError {
    statusCode = 400;

    constructor(public actions: string[]) {
        super();

        Object.setPrototypeOf(this, InvalidRuleActionBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Actions",
            detail: "Requested Actions are invalid.",
            type: "invalidActions",
            code: 40012,
            errors: [{
                parameters: [{
                    actions: this.actions,
                    message: "The specified actions for the requested rule are invalid."
                }]
            }]
        };
    }
}
