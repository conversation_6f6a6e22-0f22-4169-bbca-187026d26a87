import { CustomError } from "./custom-error";

export class InvalidApplicationOAuthError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidApplicationOAuthError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Application",
            detail: "The application does not have permission to access the requested resource.",
            type: "invalid_application",
            code: 4037
        };
    }
}
