import { CustomError } from "./custom-error";

export class InternalServerError extends CustomError {
    statusCode = 500;

    constructor() {
        super();

        Object.setPrototypeOf(this, InternalServerError.prototype);
    }

    serializeErrors() {
        return {
            "title": "Internal Server Error",
            "detail": "Sorry something went wrong, please try again. If problem persist then write <NAME_EMAIL>.",
            "type": "internalServerError",
            "code": 5002
        };
    }
}
