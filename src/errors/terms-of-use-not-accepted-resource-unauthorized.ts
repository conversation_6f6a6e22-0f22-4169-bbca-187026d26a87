import { CustomError } from "./custom-error";

export class TermsOfUseNotAcceptedResourceUnauthorizedError extends CustomError {
    statusCode = 401;

    constructor() {
        super();

        Object.setPrototypeOf(this, TermsOfUseNotAcceptedResourceUnauthorizedError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Unauthorized",
            detail: "Make sure you accept Platform Terms of Use before accessing the Platform.",
            type: "resourceUnauthorized",
            code: 4012
        };
    }
}
