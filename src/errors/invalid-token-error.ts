import { CustomError } from "./custom-error";

export class InvalidTokenError extends CustomError {
    statusCode = 403;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidTokenError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Or Expired Token",
            detail: "The access token used in the request is incorrect or has expired.",
            type: "invalidOrExpiredToken",
            code: 4031
        };
    }
}
