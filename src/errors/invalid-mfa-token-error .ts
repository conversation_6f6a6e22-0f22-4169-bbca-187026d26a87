import { CustomError } from "./custom-error";

export class InvalidMFATokenError extends CustomError {
    statusCode = 401;

    constructor(public message: string, public code: number) {
        super();

        Object.setPrototypeOf(this, InvalidMFATokenError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid MFA Token",
            detail: this.message,
            type: "invalidMFAToken",
            code: this.code
        };
    }
}
