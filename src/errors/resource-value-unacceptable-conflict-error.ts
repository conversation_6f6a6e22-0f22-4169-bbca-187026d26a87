import { InvalidBodyParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class ResourceValueUnacceptableConflictError extends CustomError {
    statusCode = 409;

    constructor(public errors: InvalidBodyParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, ResourceValueUnacceptableConflictError.prototype);
    }

    serializeErrors() {
        return {
            title: "Resource Value Unacceptable",
            detail: "The requested resource cannot accept the specified value.",
            type: "resourceValueUnacceptable",
            code: 4096,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
