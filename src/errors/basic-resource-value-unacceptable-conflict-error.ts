import { ConflictErrorCodes } from "../utils/conflict-error-codes";
import { CustomError } from "./custom-error";
import { errorResponseObj, ResourceValueUnacceptableParameterErrorObj } from "../interfaces";

export class BasicResourceValueUnacceptableConflictError extends CustomError {
    statusCode = 409;

    constructor(public code: ConflictErrorCodes, public message: string, public errors: ResourceValueUnacceptableParameterErrorObj[] = []) {
        super();

        Object.setPrototypeOf(this, BasicResourceValueUnacceptableConflictError.prototype);
    }

    serializeErrors() {
        const errorData: errorResponseObj = {
            title: "Resource Value Unacceptable",
            detail: this.message,
            type: "resourceValueUnacceptable",
            code: this.code
        };

        if(this.errors.length) {
            errorData.errors = [{
                parameters: this.errors
            }];
        }
        return errorData;
    }
}
