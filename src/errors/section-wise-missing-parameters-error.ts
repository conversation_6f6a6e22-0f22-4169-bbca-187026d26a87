import { CustomError } from "./custom-error";

export class SectionWiseMissingParametersError extends CustomError {
    statusCode = 422;

    constructor(public errors: {[key: string]: string[]}, public message: string = "") {
        super();

        Object.setPrototypeOf(this, SectionWiseMissingParametersError.prototype);
    }

    serializeErrors() {
        return {
            title: "Missing Parameters",
            detail: "One or more required parameters are missing.",
            type: "missingParameters",
            code: 4222,
            errors: [{
                parameters: [{
                    attributes: this.errors,
                    message: this.message || "These paramters are missing."
                }]
            }]
        };
    }
}
