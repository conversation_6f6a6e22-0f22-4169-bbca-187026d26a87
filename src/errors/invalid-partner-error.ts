import { CustomError } from "./custom-error";

export class InvalidPartnerError extends CustomError {
    statusCode = 400;

    constructor() {
        super();

        Object.setPrototypeOf(this, InvalidPartnerError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Partner",
            detail: "The specified partner is invalid.",
            type: "invalidPartner",
            code: 40011,
        };
    }
}
