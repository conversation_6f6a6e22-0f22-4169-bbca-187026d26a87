import { errorResponseObj } from "../interfaces";

export abstract class CustomError extends Error {
    abstract statusCode: number;
    constructor() {
        super();

        Object.setPrototypeOf(this, CustomError.prototype);
    }

    abstract serializeErrors(): {
        title: errorResponseObj["title"],
        detail: errorResponseObj["detail"],
        type: errorResponseObj["type"],
        code: errorResponseObj["code"],
        errors?: errorResponseObj["errors"]
    };
}
