import { ValidationError } from "express-validator";
import { CustomError } from "./custom-error";

export class RequestValidationError extends CustomError {
    statusCode = 400;

    constructor(public errors: ValidationError[]) {
        super();

        // Only because we are extending a built in class
        Object.setPrototypeOf(this, RequestValidationError.prototype);
    }

    serializeErrors() {
        const invalidPathParams = this.errors.filter(error => error.type === "field").filter(error => error.location === "params");
        if (invalidPathParams && invalidPathParams.length) {
            return {
                title: "Invalid Path",
                detail: "Specified path parameters are invalid.",
                type: "invalidPath",
                code: 4003,
                errors: [{
                    parameters: [{
                        attributes: invalidPathParams.map(error => error.path),
                        message: "These path parameters must be valid."
                    }]
                }]
            };
        }

        const requiredQueryParams = this.errors.filter(error => error.type === "field").filter(error => error.location === "query" && error.value === undefined);
        if (requiredQueryParams && requiredQueryParams.length) {
            this.statusCode = 422;
            return {
                title: "Missing Parameters",
                detail: "One or more required parameters are missing.",
                type: "missingParameters",
                code: 4221,
                errors: [{
                    parameters: [{
                        attributes: requiredQueryParams.map(error => error.path),
                        message: "These paramters are missing."
                    }]
                }]
            };
        }

        const invalidQueryParams = this.errors.filter(error => error.type === "field").filter(error => error.location === "query");
        if (invalidQueryParams && invalidQueryParams.length) {
            return {
                title: "Invalid Query",
                detail: "One or more query parameters specified in the request are invalid.",
                type: "invalidQuery",
                code: 4004,
                errors: [{
                    parameters: invalidQueryParams.map(error => {
                        return { name: error.path, value: error.value, message: error.msg };
                    })
                }]
            };
        }

        const requiredBodyParams = this.errors.filter(error => error.type === "field").filter(error => error.location === "body" && error.value === undefined);
        if (requiredBodyParams && requiredBodyParams.length) {
            this.statusCode = 422;
            return {
                title: "Missing Parameters",
                detail: "One or more required parameters are missing.",
                type: "missingParameters",
                code: 4221,
                errors: [{
                    parameters: [{
                        attributes: requiredBodyParams.map(error => error.path),
                        message: "These paramters are missing."
                    }]
                }]
            };
        }

        const invalidRequest = this.errors.filter(error => error.type === "field");
        return {
            title: "Invalid Request",
            detail: "One or more request body parameters are invalid.",
            type: "invalidRequest",
            code: 4002,
            errors: [{
                parameters: invalidRequest.map(error => {
                    return { name: error.path, value: error.value, message: error.msg };
                })
            }]
        };
    }
}
