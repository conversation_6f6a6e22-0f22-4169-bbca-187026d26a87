import { InvalidBodyParameterErrorObj } from "../interfaces";
import { CustomError } from "./custom-error";

export class InvalidResourceIdBadRequestError extends CustomError {
    statusCode = 400;

    constructor(public errors: InvalidBodyParameterErrorObj[]) {
        super();

        Object.setPrototypeOf(this, InvalidResourceIdBadRequestError.prototype);
    }

    serializeErrors() {
        return {
            title: "Invalid Resource ID",
            detail: "Requested resource id not found.",
            type: "invalidResourceId",
            code: 4005,
            errors: [{
                parameters: this.errors
            }]
        };
    }
}
