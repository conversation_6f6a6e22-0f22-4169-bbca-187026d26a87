import { Subjects } from "./subjects";

interface IWidgets {
    widget: {
        id: string,
        name: string,
        color: string,
        type: string,
        restrictedUsers: string[]
    },
    data: {
        id: string,
        note: string
    } | {
        id: string,
        title: string,
        description: string | null,
        type: string,
        module: null
    }[]
}

export interface IncidentStatusUpdateV3EmailEvent {
    subject: Subjects.IncidentStatusUpdateV3EmailNotification;
    data: {
        requestId: string,
        recipientIds: string[],
        update: {
            id: string,
            incident: string,
            layout: null,
            name: string,
            incidentId: string,
            published: boolean,
            publishedDate: string,
            publishedBy: string,
        }
        widgets: IWidgets[],
        platformURL: string,
        url: string,
    }
}
