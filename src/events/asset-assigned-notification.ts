import { Subjects } from "./subjects";

export interface AssetAssignedNotificationEvent {
    subject: Subjects.AssetAssignedNotification;
    data: {
        url: string,
        hostName: string,
        previousOwner: string,
        currentOwner: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        recipientUserIDs: string[],
        date: string
    }
}
