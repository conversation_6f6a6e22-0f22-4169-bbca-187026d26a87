// import { Subjects } from "./subjects";

// export interface MultipleMilestonesAssignedNotificationEvent {
//     subject: Subjects.MultipleMilestonesAssignedNotification;
//     data: {
//         milestones: {
//             url: string,
//             name: string,
//             previousOwner: string
//         }[],
//         currentOwner: string,
//         projectName: string,
//         platformURL: string,
//         projectId: string,
//         recipientUserIDs: string[],
//         date: string
//     }
// }
