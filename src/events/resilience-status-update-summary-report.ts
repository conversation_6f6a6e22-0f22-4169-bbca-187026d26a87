import { Subjects } from "./subjects";

interface WorkStreamProgress {
    name: string;
    progress: number;
    moxId: string;
}

interface SummaryItem {
    name: string;
    URL: string;
    status: string;
    date: string;
}

export interface ResilienceStatusUpdateSummaryReportEvent {
    subject: Subjects.ResilienceStatusUpdateSummaryReport;
    data: {
        projectId: string;
        title: string;
        duration: string;
        platformURL: string;
        workStreamsProgress: WorkStreamProgress[];
        completedActivities: SummaryItem[];
        nextSteps: SummaryItem[];
        risk: SummaryItem[];
        issue: SummaryItem[];
        organizationId: string | null;
        deleteRecord: boolean;
        sendEmail: boolean;
    }
}
