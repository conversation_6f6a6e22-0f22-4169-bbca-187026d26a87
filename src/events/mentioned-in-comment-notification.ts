import { Subjects } from "./subjects";

export interface MentionedInCommentNotificationEvent {
    subject: Subjects.MentionedInCommentNotification;
    data: {
        username: string,
        userInitials: string,
        url: string,
        name: string,
        comment: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        projectType: "Incident" | "Resilience",
        recipientUserIDs: string[],
        date: string
    }
}
