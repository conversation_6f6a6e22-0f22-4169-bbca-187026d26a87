import { Subjects } from "./subjects";

export interface IncidentStatusUpdateSubmittedForReviewNotificationEvent {
    subject: Subjects.IncidentStatusUpdateSubmittedForReviewNotification;
    data: {
        url: string,
        name: string,
        type: string,
        submittedBy: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        date: string,
        reporterId: string
    }
}
