import { Subjects } from "./subjects";

export interface TaskAssignedNotificationEvent {
    subject: Subjects.TaskAssignedNotification;
    data: {
        url: string,
        name: string,
        previousOwner: string,
        currentOwner: string,
        changedBy: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        recipientUserIDs: string[],
        date: string
    }
}
