import { Subjects } from "./subjects";

export interface MultipleStatusUpdatesRejectedNotificationEvent {
    subject: Subjects.MultipleStatusUpdateRejectedNotification;
    data: {
        statusUpdates: {
            url: string,
            name: string,
            reason: string,
        }[],
        recipientUserIDs: string[],
        date: string,
        projectName: string,
        platformURL: string,
        projectId: string
    }
}
