import { Subjects } from "./subjects";

interface users {
    name: string;
    organization: string;
}

export interface SuperAdminPolicyAssignedNotificationEvent {
    subject: Subjects.SuperAdminPolicyAssignedNotification;
    data: {
        recipientUserIds: string[],
        assignedByName: string,
        assignedByInitials: string,
        assignedToUsers: users[];
        serviceName: string;
    }
}
