import { Subjects } from "./subjects";

export interface UserCreatedEvent {
    subject: Subjects.UserCreated;
    data: {
        id: string;
        version: number;
        email: string;
        displayName: string;
        isEnabled: boolean;
        firstName: string | null;
        lastName: string | null;
        name: string | null;
        organizationId: string | null;
        keys: string[];
        jobTitle: string | null,
        officePhone: string | null,
        role: string | null,
        allowedLoginTokens: string[]
    };
}
