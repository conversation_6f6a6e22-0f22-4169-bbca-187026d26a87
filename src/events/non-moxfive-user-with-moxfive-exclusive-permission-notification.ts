import { Subjects } from "./subjects";
import { NonMoxfiveUserWithMfExclusiveTypesEnum } from "../enums/non-moxfive-user-with-mf-exclusive-type.enum";

interface users {
    name: string;
    organization: string;
    email?: string;
}

export interface NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationEvent {
    subject: Subjects.NonMoxfiveUserWithMoxfiveExclusivePermissionNotification;
    data: {
        recipientUserIds: string[],
        assignedByName: string,
        assignedByInitials: string,
        assignedToUsers: users[];
        serviceName: string;
        policies: string[],
        type: NonMoxfiveUserWithMfExclusiveTypesEnum
    }
}
