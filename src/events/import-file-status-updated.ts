import { Subjects } from "./subjects";

export interface ImportFileStatusUpdatedEvent {
    subject: Subjects.ImportFileStatusUpdated;
    data: {
        id: string;
        status: string;
        module: string;
        incidentId: string;
        importedBy: string;
        totalRows: number;
        succeededRows: number;
        validationErrors: Array<{
            type: string;
            subType: string;
            message: string;
        }> | null;
        failures: string[] | null;
        fileName: string;
    }
}
