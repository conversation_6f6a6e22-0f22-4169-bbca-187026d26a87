import { Subjects } from "./subjects";

export interface ResilienceStatusUpdateSubmittedForReviewNotificationEvent {
    subject: Subjects.ResilienceStatusUpdateSubmittedForReviewNotification;
    data: {
        url: string,
        name: string,
        type: string,
        submittedBy: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        date: string,
        reporterId: string
    }
}
