import { Subjects } from "./subjects";

export interface ResilienceStatusUpdateApprovedNotificationEvent {
    subject: Subjects.ResilienceStatusUpdateApprovedNotification;
    data: {
        url: string,
        name: string,
        type: string,
        submittedBy: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        recipientUserIDs: string[],
        date: string
    }
}
