import { Subjects } from "./subjects";

interface WorkStreamProgress {
    name: string;
    progress: number;
    moxId: string;
}

// interface Milestone {
//     name: string;
//     owner: string;
//     percentage: number;
//     status: string;
// }

interface SummaryItem {
    name: string;
    URL: string;
    status: string;
    date: string;
}

export interface StatusUpdateSummaryReportEvent {
    subject: Subjects.StatusUpdateSummaryReport;
    data: {
        projectId: string;
        title: string;
        duration: string;
        platformURL: string;
        overallProjectProgress: number;
        workStreamsProgress: WorkStreamProgress[];
        // milestones: Milestone[];
        completedActivities: SummaryItem[];
        nextSteps: SummaryItem[];
        risk: SummaryItem[];
        issue: SummaryItem[];
        deleteRecord: boolean;
        sendEmail: boolean;
    }
}
