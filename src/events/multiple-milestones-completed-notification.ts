// import { Subjects } from "./subjects";

// export interface MultipleMilestonesCompletedNotificationEvent {
//     subject: Subjects.MultipleMilestonesCompletedNotification;
//     data: {
//         milestones: {
//             url: string,
//             name: string,
//             oldStatus: string
//         }[],
//         newStatus: string,
//         changedBy: string,
//         projectName: string,
//         platformURL: string,
//         projectId: string,
//         date: string
//     }
// }
