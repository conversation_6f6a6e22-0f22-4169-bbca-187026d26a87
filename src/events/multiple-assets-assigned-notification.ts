import { Subjects } from "./subjects";

export interface MultipleAssetsAssignedNotificationEvent {
    subject: Subjects.MultipleAssetsAssignedNotification;
    data: {
        assets: {
            url: string,
            hostName: string,
            previousOwner: string
        }[],
        currentOwner: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        recipientUserIDs: string[],
        date: string
    }
}
