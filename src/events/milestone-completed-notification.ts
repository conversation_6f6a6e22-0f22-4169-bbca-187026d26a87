// import { Subjects } from "./subjects";

// export interface MilestoneCompletedNotificationEvent {
//     subject: Subjects.MilestoneCompletedNotification;
//     data: {
//         url: string,
//         name: string,
//         oldStatus: string,
//         newStatus: string,
//         changedBy: string,
//         projectName: string,
//         platformURL: string,
//         projectId: string,
//         date: string
//     }
// }
