import { Subjects } from "./subjects";

interface Overview {
    membersAdded: number;
    policiesAdded: number;
    organizationsAdded: number;
}

interface NonMoxfiveUserWithMoxfivePermission {
    name: string;
    policy: string;
}

export interface WeeklyAccessControlSummaryReportEvent {
    subject: Subjects.WeeklyAccessControlSummaryReport;
    data: {
        title: string;
        duration: string;
        platformURL: string;
        overview: Overview;
        nonMoxfiveUsersWithMoxfivePermission: NonMoxfiveUserWithMoxfivePermission[];
    }
}
