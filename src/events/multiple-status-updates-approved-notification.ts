import { Subjects } from "./subjects";

export interface MultipleStatusUpdatesApprovedNotificationEvent {
    subject: Subjects.MultipleStatusUpdateApprovedNotification;
    data: {
        statusUpdates: {
            url: string,
            name: string,
            submittedBy: string,
            type: string
        }[],
        date: string,
        recipientUserIDs: string[],
        projectName: string,
        platformURL: string,
        projectId: string,
    }
}
