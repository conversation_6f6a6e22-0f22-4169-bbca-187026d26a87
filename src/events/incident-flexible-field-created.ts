import { Subjects } from "./subjects";

interface ValuesArr {
    _id?: string,
    id?: string,
    value: string,
}

export interface IncidentFlexibleFieldCreatedEvent {
    subject: Subjects.IncidentFlexibleFieldCreated;
    data: {
        id: string;
        version: number;
        name: string;
        key: string;
        values: ValuesArr[],
        multiChoice: boolean,
        uniqueLevel: string;
        defaultValues: string[];
        creatable: boolean;
    };
}
