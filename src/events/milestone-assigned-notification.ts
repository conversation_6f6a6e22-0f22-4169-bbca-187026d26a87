// import { Subjects } from "./subjects";

// export interface MilestoneAssignedNotificationEvent {
//     subject: Subjects.MilestoneAssignedNotification;
//     data: {
//         url: string,
//         name: string,
//         previousOwner: string,
//         currentOwner: string,
//         projectName: string,
//         platformURL: string,
//         projectId: string,
//         recipientUserIDs: string[],
//         date: string
//     }
// }
