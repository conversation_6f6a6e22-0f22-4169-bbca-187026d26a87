import { Subjects } from "./subjects";

export interface MultipleStatusUpdatesSubmittedForReviewNotificationEvent {
    subject: Subjects.MultipleStatusUpdatesSubmittedForReviewNotification;
    data: {
        statusUpdates: {
            url: string,
            name: string,
            submittedBy: string,
            type: string
        }[],
        projectName: string,
        platformURL: string,
        projectId: string,
        date: string,
        reporterId: string
    }
}
