import { Subjects } from "./subjects";

interface Overview {
    totalHours: string | null;
    spentHours:  string | null;
    remainingHours: string | null;
    budget:  string | null;
    budgetSpent:  string | null;
    budgetRemaining:  string | null;
    expenses:  string | null;
}

interface Offerings {
    id: string,
    moxId: string | null,
    active: boolean,
    progressPercentage: number,
    startDate: string | null,
    endDate: string | null,
    billBy: string | null,
    budgetBy: string | null,
    name: {
        id: string,
        value: string
    }[],
    feeTypes: {
        id: string,
        value: string
    }[],
    tasks: any
}

interface BudgetUtilization {
    timeAndMaterial: {
        economicSummary: { [key: string]: any },
        offerings: Offerings[]
    },
    fixedFee: {
        economicSummary: { [key: string]: any },
        offerings: Offerings[]
    },
    recurringFixedFee: {
        economicSummary: { [key: string]: any },
        offerings: Offerings[]
    }
}

interface WorkStreamProgress {
    name: string;
    progress: number;
    moxId: string;
}

export interface EconomicsUpdateReportEvent {
    subject: Subjects.EconomicsUpdateReport;
    data: {
        projectId: string;
        title: string;
        duration: string;
        platformURL: string;
        overallProjectProgress: number;
        overview: Overview;
        budgetUtilization: BudgetUtilization;
        workStreamsProgress: WorkStreamProgress[];
        deleteRecord: boolean;
        sendEmail: boolean;
    }
}
