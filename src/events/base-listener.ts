import {
    consumerOpts, ConsumerOptsBuilder, createInbox, JetStreamClient, JsMsg,
    StringCodec
} from "nats";
import { Subjects } from "./subjects";
// import { EventMessageLogs, EventMessageLogsAttrs } from "../models/event-message-logs.model";
// import { sendEmail } from "../utils/send-email";
// import { EventTypeEnum } from "../enums/event-type";

interface Event {
    subject: Subjects;
    data: any;
}

export abstract class Listener<T extends Event> {
    abstract subject: T["subject"];
    abstract queueGroupName: string;
    protected client: JetStreamClient;
    private serviceName = "";

    abstract onMessage(data: T["data"], msg: JsMsg): Promise<void>;
    constructor(client: JetStreamClient) {
        this.client = client;
    }

    async listen() {
        this.serviceName = this.queueGroupName.split("-").slice(0, -1).join("-");

        const sc = StringCodec();
        const opts: ConsumerOptsBuilder = consumerOpts();
        opts.queue(this.queueGroupName);
        opts.durable(`${this.queueGroupName}:${this.subject.split(".")[1]}`);
        opts.manualAck();
        opts.ackExplicit();
        opts.deliverTo(createInbox());
        const subscription = await this.client.subscribe(this.subject, opts);

        for await (const m of subscription) {
            // const eventLog: EventMessageLogsAttrs = {
            //     type: EventTypeEnum.CONSUMER,
            //     subject: this.subject,
            //     metadata: "",
            //     producer: null,
            //     consumers: [this.serviceName],
            //     succeeded: false,
            //     error: null
            // };

            const eventData = JSON.parse(sc.decode(m.data));
            // const parsedData = eventData.data;
            // eventLog.metadata = JSON.stringify(parsedData);
            // eventLog.producer = eventData.publisher;

            try {
                // await this.onMessage(parsedData, m);
                await this.onMessage(eventData, m);
                // eventLog.succeeded = true;
                //
                // // Add data in event message logs collection
                // await EventMessageLogs.build(eventLog).save();
            } catch (e: any) {
                console.info(`NATS Event message failed for ${this.subject} with data: ${e.message}`);
                //                 eventLog.error = JSON.stringify(e);
                //
                //                 // Add data in event message logs collection
                //                 const eventMessageLog = await EventMessageLogs.build(eventLog).save();
                //
                //                 // Trigger the email alert
                //                 if (this.subject !== Subjects.SendEmail) {
                //                     await sendEmail({
                //                         recipients: ["<EMAIL>"],
                //                         subject: "Event Bus Subscriber Failure",
                //                         message: `Service: ${this.serviceName}
                // Event Name: ${this.subject}
                // Error: ${JSON.stringify(e)}
                // Event Producer(Service): ${eventLog.producer}
                // Event Log ID: ${String(eventMessageLog._id)}`,
                //                         recipientType: "email",
                //                         serviceName: this.queueGroupName,
                //                         combined: true,
                //                         isHTML: false
                //                     });
                //                 }
            }

        }
        console.info(`subscription closed in ${this.serviceName} for ${this.subject}`);
    }
}
