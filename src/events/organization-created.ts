import { OrganizationLocation } from "../interfaces";
import { Subjects } from "./subjects";

export interface OrganizationCreatedEvent {
    subject: Subjects.OrganizationCreated;
    data: {
        id: string;
        version: number;
        isEnabled: boolean;
        name: string;
        officeLocations?: OrganizationLocation[];
        industry?: string;
        website?: string;
        numberOfEmployees?: number;
        numberOfITStaff?: number;
        itStaffLocation?: OrganizationLocation[];
        highLevelCompanyInformation?: string;
        descriptionOfEnvironment?: string;
        organizationTypeIds: string[];
        serviceLines?: string[] | null;
        activePartner?: boolean;
        profile?: string | null;
        favicon?: string | null;
        partnerEula?: string | null;
        partnerTermsConditions?: string | null;
        partnerType?: string | null,
        shortDescription?: string | null
    }
}
