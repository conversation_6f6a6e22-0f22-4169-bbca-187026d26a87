export enum Subjects {
    UserCreated = "phoenix.user:created",
    UserUpdated = "phoenix.user:updated",
    UserDeleted = "phoenix.user:deleted",
    UserPolicyUpdated = "phoenix.userpolicy:updated",
    PolicyCreated = "phoenix.policy:created",
    PolicyUpdated = "phoenix.policy:updated",
    PolicyDeleted = "phoenix.policy:deleted",
    OrganizationCreated = "phoenix.organization:created",
    OrganizationUpdated = "phoenix.organization:updated",
    OrganizationDeleted = "phoenix.organization:deleted",
    AuditLog = "phoenix.audit:log",
    IncidentCreated = "phoenix.incident:created",
    IncidentUpdated = "phoenix.incident:updated",
    IncidentDeleted = "phoenix.incident:deleted",
    IncidentPolicyUpdated = "phoenix.incidentpolicy:updated",
    IncidentMembersRemoved = "phoenix.incidentmembers:removed",
    OrganizationFlexibleFieldUpdated = "phoenix.organizationflexiblefield:updated",
    SendEmail = `phoenix.send:email`,
    TrackerCreated = "phoenix.tracker:created",
    TrackerUpdated = "phoenix.tracker:updated",
    TrackerDeleted = "phoenix.tracker:deleted",
    TrackerPolicyUpdated = "phoenix.trackerpolicy:updated",
    TrackerMembersRemoved = "phoenix.trackermembers:removed",
    PlatformDashboardCreated = "phoenix.platformdashboard:created",
    PlatformDashboardUpdated = "phoenix.platformdashboard:updated",
    IncidentDashboardCreated = "phoenix.incidentdashboard:created",
    IncidentDashboardUpdated = "phoenix.incidentdashboard:updated",
    // MilestoneAddedNotification = "phoenix.milestoneadded:notification",
    // MilestoneAssignedNotification = "phoenix.milestoneassigned:notification",
    // MilestoneCompletedNotification = "phoenix.milestonecompleted:notification",
    TaskAddedNotification = "phoenix.taskadded:notification",
    TaskAssignedNotification = "phoenix.taskassigned:notification",
    AssetAddedNotification = "phoenix.assetadded:notification",
    AssetAssignedNotification = "phoenix.assetassigned:notification",
    MultipleAssetsAssignedNotification = "phoenix.multipleassetsassigned:notification",
    IOCAddedNotification = "phoenix.iocadded:notification",
    InvoiceAddedNotification = "phoenix.invoiceadded:notification",
    RFIAddedNotification = "phoenix.rfiadded:notification",
    RFISubmittedNotification = "phoenix.rfisubmitted:notification",
    SupplementalAddedNotification = "phoenix.supplementaladded:notification",
    IncidentStatusUpdateSubmittedForReviewNotification = "phoenix.incidentstatusupdatesubmittedforreview:notification",
    IncidentStatusUpdateApprovedNotification = "phoenix.incidentstatusupdateapproved:notification",
    IncidentStatusUpdateRejectedNotification = "phoenix.incidentstatusupdaterejected:notification",
    IncidentTimelineNegotiationUpdateReceivedNotification = "phoenix.incidenttimelinenegotiationupdatereceived:notification",
    MentionedInCommentNotification = "phoenix.mentionedincomment:notification",
    ResilienceObjectiveAddedNotification = "phoenix.resilienceobjectiveadded:notification",
    ResilienceStatusUpdateSubmittedForReviewNotification = "phoenix.resiliencestatusupdatesubmittedforreview:notification",
    ResilienceStatusUpdateApprovedNotification = "phoenix.resiliencestatusupdateapproved:notification",
    ResilienceStatusUpdateRejectedNotification = "phoenix.resiliencestatusupdaterejected:notification",
    ResilienceObjectiveCompletedNotification = "phoenix.resilienceobjectivecompleted:notification",
    ProjectMissingDataNotification = "phoenix.projectmissingdata:notification",
    ProjectStatusReportNotification = "phoenix.projectstatusreport:notification",
    AccessControlSummaryNotification = "phoenix.accesscontrolsummary:notification",
    SendBulkEmailNotification = "phoenix.sendbulkemail:notification",
    NonMoxfiveUserWithMoxfiveExclusivePermissionNotification = "phoenix.nonmoxfiveuserwithmoxfiveexclusivepermission:notification",
    SuperAdminPolicyAssignedNotification = "phoenix.superadminpolicyassigned:notification",
    UserInviteNotification = "phoenix.userinvite:notification",
    StatusUpdateSummaryReport = "phoenix.statusupdatesummary:report",
    ResilienceStatusUpdateSummaryReport = "phoenix.resiliencestatusupdatesummary:report",
    ResilienceEconomicsUpdateReport = "phoenix.resilienceeconomicsupdate:report",
    EconomicsUpdateReport = "phoenix.economicsupdate:report",
    WeeklyAccessControlSummaryReport = "phoenix.weeklyaccesscontrol:report",
    IncidentMissingProjectDataReport = "phoenix.incidentmissingprojectdata:report",
    ImportFileStatusUpdated = "phoenix.importfilestatus:updated",
    ActiveFocusViewRemoved = "phoenix.activefocusview:removed",
    ProjectDashboardRemoved = "phoenix.projectdashboard:removed",
    UserAgreementSigned = "phoenix.useragreement:signed",
    IncidentFlexibleFieldCreated = "phoenix.incidentflexiblefield:created",
    IncidentFlexibleFieldUpdated = "phoenix.incidentflexiblefield:updated",
    ManagedFlexibleFieldCreated = "phoenix.managedflexiblefield:created",
    ManagedFlexibleFieldUpdated = "phoenix.managedflexiblefield:updated",
    AssetStatusAdded = "phoenix.assetstatus:added",
    AssetStatusUpdated = "phoenix.assetstatus:updated",
    AssetStatusDeleted = "phoenix.assetstatus:deleted",
    // MultipleMilestonesAssignedNotification = "phoenix.multiplemilestonesassigned:notification",
    // MultipleMilestonesCompletedNotification = "phoenix.multiplemilestonescompleted:notification",
    MultipleTasksAssignedNotification = "phoenix.multipletasksassigned:notification",
    ApplicationCreated = "phoenix.application:created",
    ApplicationUpdated = "phoenix.application:updated",
    MultipleStatusUpdateApprovedNotification = "phoenix.multipleincidentstatusupdateapproved:notification",
    MultipleStatusUpdateRejectedNotification = "phoenix.multiplestatusupdaterejected:notification",
    MultipleStatusUpdatesSubmittedForReviewNotification = "phoenix.multipleincidentstatusupdatesubmittedforreview:notification",
    IncidentStatusUpdateVersionChanged = "phoenix.incidentstatusupdateversion:changed",
    IncidentStatusUpdateV3EmailNotification = "phoenix.incidentstatusupdatev3email:notification",
    EmailNotificationAcknowledgement = "phoenix.emailnotification:acknowledgement",
    MoxfiveInsightFlexibleFieldUpdated = "phoenix.moxfiveinsightflexiblefield:updated",
    ForgotPasswordNotification = "phoenix.forgotpassword:notification",
    WelcomeEmailNotification = "phoenix.welcomeemail:notification",
    EmailChangeNotification = "phoenix.emailchange:notification",
    PasswordChangeSuccessNotification = "phoenix.passwordchangesuccess:notification",
    ConnectionClientSecretExpirationNotification = "phoenix.connectionclientsecretexpiration:notification",
}
