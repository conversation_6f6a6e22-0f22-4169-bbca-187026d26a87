import { Subjects } from "./subjects";

export interface IncidentCreatedEvent {
    subject: Subjects.IncidentCreated;
    data: {
        id: string,
        version: number,
        name: string,
        clientId: string,
        published: boolean,
        policyIds: string[],
        members: string[],
        modules: {[key: string]: boolean},
        progressPercentage: number,
        serviceLines?: string[] | null;
    }
}
