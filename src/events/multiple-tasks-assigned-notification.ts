import { Subjects } from "./subjects";

export interface MultipleTasksAssignedNotificationEvent {
    subject: Subjects.MultipleTasksAssignedNotification;
    data: {
        tasks: {
            url: string,
            name: string,
            previousOwner: string
        }[],
        currentOwner: string,
        changedBy: string
        projectName: string,
        platformURL: string,
        projectId: string,
        recipientUserIDs: string[],
        date: string
    }
}
