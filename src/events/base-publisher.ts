import { JetStreamClient, StringCodec } from "nats";
import { Subjects } from "./subjects";
// import { EventMessageLogs, EventMessageLogsAttrs } from "../models/event-message-logs.model";
// import { EventTypeEnum, sendEmail } from "../index";
// import { EventDetails } from "../models/event-details.model";

interface Event {
    subject: Subjects;
    data: any;
}

export abstract class Publisher<T extends Event> {
    abstract subject: T["subject"];
    protected queueGroupName = "";
    protected client: JetStreamClient;
    private serviceName = "";

    constructor(client: JetStreamClient, queueGroupName?: string) {
        this.client = client;
        if (queueGroupName) {
            this.queueGroupName = queueGroupName;
        }
    }

    async publish(data: T["data"]): Promise<void> {
        this.serviceName = this.queueGroupName.split("-").slice(0, -1).join("-");

        const sc = StringCodec();

        // const eventLog: EventMessageLogsAttrs = {
        //     type: EventTypeEnum.PRODUCER,
        //     subject: this.subject,
        //     metadata: JSON.stringify(data),
        //     succeeded: false,
        //     producer: this.serviceName,
        //     consumers: null,
        //     error: null
        // };

        try {
            // // Fetch the consumers of event
            // const eventDetails = await EventDetails.findOne({ subject: this.subject }).select("consumers").lean().exec();
            // if (eventDetails) {
            //     eventLog.consumers = eventDetails.consumers;
            // }

            // Publish the event
            // await this.client.publish(this.subject, sc.encode(JSON.stringify({
            //     data,
            //     publisher: this.serviceName
            // })));
            await this.client.publish(this.subject, sc.encode(JSON.stringify(data)));

            // // Set succeeded to true and add entry in EventMessageLogs
            // eventLog.succeeded = true;
            // await EventMessageLogs.build(eventLog).save();
        } catch (e) {
            console.error(e);
            console.info(`NATS Event publish failed for ${this.subject}`);
            //             // Set error object and add entry in EventMessageLogs
            //             eventLog.error = JSON.stringify(e);
            //             await EventMessageLogs.build(eventLog).save();
            //
            //             // If subject is not SendEmail then publish the SendEmail NATS Events (Ignoring SendEmail as if that event fails then it will again sendEmail and so on)
            //             if (this.subject !== Subjects.SendEmail) {
            //                 await sendEmail({
            //                     recipients: ["<EMAIL>"],
            //                     subject: "Event Bus Publisher Failure",
            //                     message: `Service: ${this.serviceName}
            // Event Name: ${this.subject}
            // Error: ${JSON.stringify(e)}
            // Affected Consumers(services): ${(eventLog.consumers || []).join(",")}`,
            //                     recipientType: "email",
            //                     serviceName: this.queueGroupName,
            //                     combined: true,
            //                     isHTML: false
            //                 });
            //             }
        }
    }
}
