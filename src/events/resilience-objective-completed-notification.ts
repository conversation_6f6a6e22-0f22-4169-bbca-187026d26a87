import { Subjects } from "./subjects";

export interface ResilienceObjectiveCompletedNotificationEvent {
    subject: Subjects.ResilienceObjectiveCompletedNotification;
    data: {
        url: string,
        name: string,
        type: string,
        oldStatus: string,
        newStatus: string,
        changedBy: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        date: string
    }
}
