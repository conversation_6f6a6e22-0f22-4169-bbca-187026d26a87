import { Subjects } from "./subjects";

export interface RFIAddedNotificationEvent {
    subject: Subjects.RFIAddedNotification;
    data: {
        url: string,
        requestType: string,
        invoiceNumber: string | null,
        vendor: string | null,
        requestDescription: string,
        projectName: string,
        platformURL: string,
        projectId: string,
        date: string,
        reporterId: string
    }
}
