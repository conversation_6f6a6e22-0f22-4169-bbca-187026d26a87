export enum PaginationEntity {
    TRACKER_OBJECTIVE = "TrackerObjective",
    CATALOGUE_OBJECTIVE = "CatalogObjective",
    STATUS_UPDATES_FOR_OBJECTIVE_FOR_MF = "StatusChangesForObjectiveForMF",
    STATUS_UPDATES_FOR_OBJECTIVE_FOR_NON_MF = "StatusChangesForObjectiveForNonMF",
    ORGANIZATION_MEMBERS = "OrganizationMembers",
    RESILIENCE_MEMBERS = "ResilienceMembers",
    ECONOMICS_OFFERINGS_FOR_RESILIENCE = "EconomicsOfferingsForResilience",
    CATALOGUE_OBJECTIVE_PARTNERS = "CatalogObjectivePartners",
    OBJECTIVES_FOR_PARTNER = "ObjectivesForPartner",
    OBJECTIVE_PARTNER_REQUESTS_FOR_PARTNER = "ObjectivePartnerRequestsForPartner",
    OBJECTIVE_PARTNER_REQUESTS_FOR_MF = "ObjectivePartnerRequestsForMF",
    OBJECTIVE_PARTNER_CUSTOMER_REVIEWS = "ObjectivePartnerCustomerReviews",
    CUSTOMER_REVIEWS_FOR_OBJECTIVE = "CustomerReviewsForObjective",
    CUSTOMER_REVIEWS_FOR_PARTNER_OBJECTIVE = "CustomerReviewsForPartnerObjective",
    LIST_PROJECTS_FOR_PARTNER = "CustomerReviewsForPartner",
    LIST_TRACKERS = "ListTrackers",
    LIST_TRACKERS_FOR_ORGANIZATION = "ListTracksForOrganization",
    LIST_TRACKERS_FOR_ORGANIZATION_USER = "ListTracksForOrganizationUser",
    LIST_INCIDENTS = "ListIncidents",
    LIST_INCIDENTS_FOR_ORGANIZATION = "ListIncidentsForOrganization",
    LIST_INCIDENTS_FOR_ORGANIZATION_USER = "ListIncidentsForOrganizationUser",
    LIST_DRAFT_INCIDENTS = "ListDraftIncidents",
    LIST_INCIDENT_USERS = "ListIncidentUsers",
    LIST_ASSETS = "ListAssets",
    LIST_STATUS_UPDATES_FOR_INCIDENT_FOR_MF = "ListStatusUpdatesForIncidentForMF",
    LIST_STATUS_UPDATES_FOR_INCIDENT_FOR_NON_MF = "ListStatusUpdatesForIncidentForNonMF",
    // LIST_MILESTONES = "ListMilestones",
    LIST_TASKS = "ListTasks",
    LIST_INCIDENT_TIMELINES = "ListIncidentTimelines",
    LIST_IOCS = "ListIOCs",
    LIST_MOXFIVE_CARRIER_INSIGHTS = "ListMoxfiveCarrierInsights",
    LIST_CLAIM_INVOICES = "ListClaimInvoices",
    LIST_CLAIM_RFIS = "ListClaimRFIs",
    LIST_SUPPLEMENTAL_DATA = "ListSupplementalData",
    LIST_CLAIM_TIMELINES = "ListClaimTimelines",
    LIST_APPLICATIONS = "ListApplications",
    LIST_USERS = "ListUsers",
    LIST_ORGANIZATION_USERS = "ListOrganizationUsers",
    LIST_ORGANIZATIONS = "ListOrganizations",
    LIST_POLICIES = "ListPolicies",
    LIST_ACTIVITY_FEED = "ListActivityFeed",
    LIST_IOC_SNAPSHOTS = "ListIOCSnapshots",
    LIST_INCIDENT_TIMELINE_SNAPSHOTS = "ListIncidentTimelineSnapshots",
    LIST_TASK_SNAPSHOTS = "ListTaskSnapshots",
    LIST_UPDATES = "ListUpdates",
    LIST_PUBLISHED_UPDATES = "ListPublishedUpdates",
    LIST_DRAFT_UPDATES = "ListDraftUpdates",
    LIST_THREAT_ACTORS = "ListThreatActors",
    LIST_HIGHTLIGHTS = "ListHighlights",
    LIST_HIGHTLIGHTS_FOR_MF = "ListHighlightsForMF",
    LIST_AUTH0_LOGS = "ListAuth0Logs",
    LIST_CONNECTIONS = "ListConnections"
}
