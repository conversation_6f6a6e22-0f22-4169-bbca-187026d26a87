export enum Actions {
    // Authentication
    AddOrganizationFlexibleField = "AddOrganizationFlexibleField",
    AddMembersToOrganizationUsingEmails = "AddMembersToOrganizationUsingEmails",
    AddMembersToOrganization = "AddMembersToOrganization",
    AddOwnersToOrganizationsUsingEmails = "AddOwnersToOrganizationsUsingEmails",
    AddOwnersToOrganization = "AddOwnersToOrganization",
    CreateOrganization = "CreateOrganization",
    GetUsersOfActiveDirectory = "GetUsersOfActiveDirectory",
    ListOrganizations = "ListOrganizations",
    ListUsers = "ListUsers",
    GetOrganizationFlexibleFields = "GetOrganizationFlexibleFields",
    GetOrganizationUserDetail = "GetOrganizationUserDetail",
    ListOrganizationTypes = "ListOrganizationTypes",
    ListOrganizationUsers = "ListOrganizationUsers",
    GetOrganizationDetail = "GetOrganizationDetail",
    InviteUser = "InviteUser",
    RemoveOwnersOfOrganization = "RemoveOwnersOfOrganization",
    RemoveMembersOfOrganization = "RemoveMembersOfOrganization",
    UpdateUserEmailOfOrganization = "UpdateUserEmailOfOrganization",
    UpdateUsersStatusOfOrganization = "UpdateUsersStatusOfOrganization",
    UpdateUserProfileOfOrganization = "UpdateUserProfileOfOrganization",
    UpdateOrganizationStatus = "UpdateOrganizationStatus",
    UpdateUsersStatus = "UpdateUsersStatus",
    UpdateOrganization = "UpdateOrganization",
    // Authorization
    CreatePolicy = "CreatePolicy",
    UpdatePolicy = "UpdatePolicy",
    UpdatePoliciesStatus = "UpdatePoliciesStatus",
    DeletePolicy = "DeletePolicy",
    ListServices = "ListServices",
    ListServiceActions = "ListServiceActions",
    ListPolicies = "ListPolicies",
    GetPolicyDetail = "GetPolicyDetail",
    ListServicesForPolicy = "ListServicesForPolicy",
    ListServiceActionsForPolicy = "ListServiceActionsForPolicy",
    AttachPolicyToEntities = "AttachPolicyToEntities",
    DetachPolicyFromEntities = "DetachPolicyFromEntities",
    ListEntitiesForPolicy = "ListEntitiesForPolicy",
    ListPoliciesForEntity = "ListPoliciesForEntity",
    AttachPoliciesToEntity = "AttachPoliciesToEntity",
    DetachPoliciesFromEntity = "DetachPoliciesFromEntity",
    // Incident
    AddIncidentFlexibleField = "AddIncidentFlexibleField",
    CreateDraftIncident = "CreateDraftIncident",
    GetIncidentModules = "GetIncidentModules",
    ListDraftIncidents = "ListDraftIncidents",
    ListIncidents = "ListIncidents",
    UpdateEnvironmentActiveDirectoryDetail = "UpdateEnvironmentActiveDirectoryDetail",
    UpdateEnvironmentBackupsDetail = "UpdateEnvironmentBackupsDetail",
    UpdateEnvironmentEmailDetail = "UpdateEnvironmentEmailDetail",
    UpdateEnvironmentGeneralDetail = "UpdateEnvironmentGeneralDetail",
    UpdateEnvironmentSolutionsDetail = "UpdateEnvironmentSolutionsDetail",
    UpdateIncidentBusinessEmailCompromiseDetail = "UpdateIncidentBusinessEmailCompromiseDetail",
    UpdateIncidentExtortionDetail = "UpdateIncidentExtortionDetail",
    UpdateIncidentGeneralDetail = "UpdateIncidentGeneralDetail",
    UpdateIncidentModules = "UpdateIncidentModules",
    UpdateIncidentRansomwareDetail = "UpdateIncidentRansomwareDetail",
    UpdateInsuranceClaimDetail = "UpdateInsuranceClaimDetail",
    UpdateInsuranceGeneralDetail = "UpdateInsuranceGeneralDetail",
    UpdateInsurancePolicyDetail = "UpdateInsurancePolicyDetail",
    GetCompanyBackground = "GetCompanyBackground",
    GetEnvironmentActiveDirectoryDetail = "GetEnvironmentActiveDirectoryDetail",
    GetEnvironmentBackupsDetail = "GetEnvironmentBackupsDetail",
    GetEnvironmentEmailDetail = "GetEnvironmentEmailDetail",
    GetEnvironmentGeneralDetail = "GetEnvironmentGeneralDetail",
    GetEnvironmentSolutionsDetail = "GetEnvironmentSolutionsDetail",
    GetIncidentBusinessEmailCompromiseDetail = "GetIncidentBusinessEmailCompromiseDetail",
    GetIncidentExtortionDetail = "GetIncidentExtortionDetail",
    GetIncidentGeneralDetail = "GetIncidentGeneralDetail",
    GetIncidentRansomewareDetail = "GetIncidentRansomewareDetail",
    GetInsuranceClaimDetail = "GetInsuranceClaimDetail",
    GetInsuranceGeneralDetail = "GetInsuranceGeneralDetail",
    GetInsurancePolicyDetail = "GetInsurancePolicyDetail",
    UpdateCompanyBackgroundDetail = "UpdateCompanyBackgroundDetail",
    PublishIncident = "PublishIncident",
    UpdateIncidentStatus = "UpdateIncidentStatus",
    UpdateIncidentProgressPercentage = "UpdateIncidentProgressPercentage",
}
