/* eslint-disable security/detect-non-literal-fs-filename */
/* eslint-disable no-console,no-sync */
import fs from "fs";
import { readdir, unlink } from "fs/promises";
import util from "util";
import moment from "moment-timezone";
import { BlobServiceClient, ContainerClient } from "@azure/storage-blob";
export class Logger {
    private readonly appName: string;
    private readonly fileName: string;
    private readonly filePath: string;
    private readonly AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING;
    private containerClient: ContainerClient | undefined;
    constructor(appName: string) {
        this.appName = `${appName}-logs`;
        this.fileName = `${appName}-${moment.tz("America/New_York").format("YYYYMMDDHHmm")}`;
        this.filePath = `/logs/${this.fileName}`;

        const logStdout = process.stdout;
        const myConsoleFunction = (type: string) => {
            fs.writeFileSync(`${this.filePath}-${type}.log`, "");
            const logfile = fs.createWriteStream(`${this.filePath}-${type}.log`,
                { flags: "w" });
            return function (...args: any[]) {
                const data = JSON.stringify({ messages: JSON.stringify(args), level: type, timeStamp: moment.tz("America/New_York").format() });
                logfile.write(util.format(data) + "\n");
                logStdout.write(util.format(data) + "\n");
            };
        };
        console.log = myConsoleFunction("log");
        console.info = myConsoleFunction("info");
        console.warn = myConsoleFunction("warn");
        console.error = myConsoleFunction("error");
    }

    async moveOldLogFiles() {
        try {
            if (!this.AZURE_STORAGE_CONNECTION_STRING) {
                console.error("connection string missing");
                return;
            }
            const blobServiceClient = BlobServiceClient.fromConnectionString(
                this.AZURE_STORAGE_CONNECTION_STRING
            );
            this.containerClient = blobServiceClient.getContainerClient(this.appName);
            await this.containerClient.createIfNotExists();
            const files = await readdir("/logs");
            const filesToMove = files.filter(file => file.includes(this.appName.replace("-logs", "")) && !file.includes(this.fileName));
            await this.moveFiles(new Set(filesToMove));
        } catch (err) {
            console.error("error reading files", err);
        }
    }

    private async moveFiles(files: Set<string>): Promise<void> {
        const [file] = files;
        if(!this.containerClient || !file) {
            return;
        }
        files.delete(file);
        try {
            const nameArr = file.split("-");
            const timestamp = parseInt(nameArr[nameArr.length - 2]) ? nameArr[nameArr.length - 2] : nameArr[nameArr.length - 1].replace(".log", "");
            const blockBlobClient = this.containerClient.getBlockBlobClient(
                `${timestamp.substring(0, 4)}/${timestamp.substring(4, 6)}/${timestamp.substring(6, 8)}/${timestamp.substring(8, 10)}/${file}`);
            await blockBlobClient.uploadFile(`/logs/${file}`);
            await unlink(`/logs/${file}`);
            return this.moveFiles(files);
        } catch (err) {
            console.error("error moving file", file, err);
            return this.moveFiles(files);
        }
    }
}
