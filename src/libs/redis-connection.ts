import { RedisClientType } from "redis";

class RedisConnection {
    private _client?: RedisClientType;
    get client() {
        if (!this._client) {
            throw new Error("Cannot access redis before connecting");
        }
        return this._client;
    }

    setClient(redisClient: RedisClientType): void {
        this._client = redisClient;
    }
}

export const redisConnection = new RedisConnection();
