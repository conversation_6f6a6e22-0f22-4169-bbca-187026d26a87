import tensorflow as tf
from tensorflow.keras.layers import LSTM, Dense, Input, LayerNormalization, MultiHeadAttention
from tensorflow.keras.models import Model
import numpy as np

class TransformerBlock(tf.keras.layers.Layer):
    def __init__(self, num_heads, d_model, ff_dim, rate=0.1):
        super(TransformerBlock, self).__init__()
        self.att = MultiHeadAttention(num_heads=num_heads, key_dim=d_model)
        self.ffn = tf.keras.Sequential([
            Dense(ff_dim, activation="relu"),
            Dense(d_model)
        ])
        self.layernorm1 = LayerNormalization(epsilon=1e-6)
        self.layernorm2 = LayerNormalization(epsilon=1e-6)
        self.dropout1 = tf.keras.layers.Dropout(rate)
        self.dropout2 = tf.keras.layers.Dropout(rate)
    
    def call(self, inputs, training):
        attn_output = self.att(inputs, inputs)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output)

def build_model(seq_len, feature_dim):
    inputs = Input(shape=(seq_len, feature_dim))
    x = LSTM(128, return_sequences=True)(inputs)
    x = TransformerBlock(num_heads=4, d_model=128, ff_dim=512)(x)
    x = LSTM(64)(x)
    outputs = Dense(1)(x)
    model = Model(inputs, outputs)
    model.compile(optimizer='adam', loss='mse')
    return model

def prepare_sequences(df, seq_len):
    features = ['temperature', 'humidity', 'cloud_cover', 'zenith_angle', 'hour', 'day', 'month']
    X, y = [], []
    for i in range(len(df) - seq_len):
        X.append(df[features].iloc[i:i+seq_len].values)
        y.append(df['generation_kWh'].iloc[i+seq_len])
    return np.array(X), np.array(y)

def train_model(model, df, seq_len=96, epochs=50, batch_size=32):
    X, y = prepare_sequences(df, seq_len)
    model.fit(X, y, epochs=epochs, batch_size=batch_size, validation_split=0.2)
    model.save('solar_model.h5')

def incremental_train(model, new_data, seq_len=96, epochs=5):
    X, y = prepare_sequences(new_data, seq_len)
    model.fit(X, y, epochs=epochs, batch_size=32, shuffle=False)
    model.save('solar_model.h5')