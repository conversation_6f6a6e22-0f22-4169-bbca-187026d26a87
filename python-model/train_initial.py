from preprocess import preprocess_data
from model import build_model, train_model
import tensorflow as tf

# Train on initial 2-year data
csv_file = "initial_plant_data.csv"  # Replace with your 2-year data file
latitude = 40.7128  # Example: New York
longitude = -74.0060
api_key = "your_openweather_api_key"

df, scaler = preprocess_data(csv_file, latitude, longitude, api_key)
model = build_model(seq_len=96, feature_dim=7)
train_model(model, df, seq_len=96, epochs=50, batch_size=32)