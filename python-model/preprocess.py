import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import requests
from astral import LocationInfo
from astral.sun import sun
from datetime import datetime
import psycopg2
from psycopg2.extras import execute_batch

def fetch_weather_data(lat, lon, start_date, end_date, api_key):
    url = f"https://api.openweathermap.org/data/2.5/history?lat={lat}&lon={lon}&start={start_date}&end={end_date}&appid={api_key}"
    response = requests.get(url)
    if response.status_code == 200:
        data = response.json()
        weather_df = pd.DataFrame({
            'timestamp': pd.to_datetime([entry['dt'] for entry in data['list']], unit='s'),
            'temperature': [entry['main']['temp'] for entry in data['list']],
            'humidity': [entry['main']['humidity'] for entry in data['list']],
            'cloud_cover': [entry['clouds']['all'] for entry in data['list']],
            # Note: GHI may require a different API or calculation
        })
        return weather_df
    else:
        raise Exception(f"Weather API failed: {response.status_code}")

def calculate_zenith_angle(lat, lon, timestamp):
    location = LocationInfo(latitude=lat, longitude=lon)
    s = sun(location.observer, date=timestamp)
    zenith = s['zenith']
    return zenith

def preprocess_data(csv_file, lat, lon, api_key):
    # Read CSV
    df = pd.read_csv(csv_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Standardize to 15-min intervals
    df = df.set_index('timestamp').resample('15min').interpolate().reset_index()
    
    # Clean data
    df = df.fillna(df.mean(numeric_only=True))
    df = df[(df['generation_kWh'] >= 0) & (df['generation_kWh'] <= df['generation_kWh'].quantile(0.99))]
    
    # Fetch weather data
    start_date = df['timestamp'].min().strftime('%Y-%m-%d')
    end_date = df['timestamp'].max().strftime('%Y-%m-%d')
    weather_df = fetch_weather_data(lat, lon, start_date, end_date, api_key)
    
    # Merge with generation data
    df = df.merge(weather_df, on='timestamp', how='left')
    
    # Feature engineering
    df['hour'] = df['timestamp'].dt.hour
    df['day'] = df['timestamp'].dt.day
    df['month'] = df['timestamp'].dt.month
    df['zenith_angle'] = df['timestamp'].apply(lambda x: calculate_zenith_angle(lat, lon, x))
    
    # Normalize features
    scaler = MinMaxScaler()
    numeric_cols = ['generation_kWh', 'temperature', 'humidity', 'cloud_cover', 'zenith_angle']
    df[numeric_cols] = scaler.fit_transform(df[numeric_cols])
    
    # Store in TimescaleDB
    conn = psycopg2.connect(
        dbname="solar_db", user="user", password="password", host="localhost", port="5432"
    )
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS solar_data (
            timestamp TIMESTAMPTZ PRIMARY KEY,
            generation_kWh FLOAT,
            latitude FLOAT,
            longitude FLOAT,
            temperature FLOAT,
            humidity FLOAT,
            cloud_cover FLOAT,
            zenith_angle FLOAT,
            hour INT,
            day INT,
            month INT
        );
    """)
    records = df[['timestamp', 'generation_kWh', 'latitude', 'longitude', 'temperature', 
                  'humidity', 'cloud_cover', 'zenith_angle', 'hour', 'day', 'month']].to_records(index=False)
    query = """
        INSERT INTO solar_data (timestamp, generation_kWh, latitude, longitude, temperature, 
                               humidity, cloud_cover, zenith_angle, hour, day, month)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    execute_batch(cursor, query, records)
    conn.commit()
    cursor.close()
    conn.close()
    
    return df, scaler