from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import pandas as pd
import numpy as np
from model import build_model, incremental_train, prepare_sequences
from preprocess import preprocess_data, fetch_weather_data
import tensorflow as tf
import os

app = FastAPI()
model = None
scaler = None
SEQ_LEN = 96  # 96 time steps (15-min intervals for 24 hours)
FEATURE_DIM = 7  # temperature, humidity, cloud_cover, zenith_angle, hour, day, month
API_KEY = "your_openweather_api_key"

@app.on_event("startup")
async def load_model():
    global model, scaler
    if os.path.exists('solar_model.h5'):
        model = tf.keras.models.load_model('solar_model.h5', custom_objects={'TransformerBlock': TransformerBlock})
    else:
        model = build_model(SEQ_LEN, FEATURE_DIM)

@app.post("/upload")
async def upload_data(file: UploadFile = File(...), latitude: float = 0.0, longitude: float = 0.0):
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Only CSV files are allowed")
    df, scaler = preprocess_data(file.file, latitude, longitude, API_KEY)
    return JSONResponse(content={"message": "Data uploaded and processed", "rows": len(df)})

@app.post("/train")
async def train_model_endpoint(file: UploadFile = File(...), latitude: float = 0.0, longitude: float = 0.0):
    global model
    df, _ = preprocess_data(file.file, latitude, longitude, API_KEY)
    incremental_train(model, df, SEQ_LEN)
    return JSONResponse(content={"message": "Model trained successfully"})

@app.post("/predict")
async def predict(data: dict):
    global model, scaler
    lat = data['latitude']
    lon = data['longitude']
    timestamp = pd.to_datetime(data['timestamp'])
    
    # Fetch weather forecast
    weather_df = fetch_weather_data(lat, lon, timestamp.strftime('%Y-%m-%d'), 
                                  (timestamp + pd.Timedelta(days=1)).strftime('%Y-%m-%d'), API_KEY)
    
    # Prepare input
    df = pd.DataFrame({
        'timestamp': [timestamp + pd.Timedelta(minutes=15*i) for i in range(SEQ_LEN)],
        'latitude': [lat] * SEQ_LEN,
        'longitude': [lon] * SEQ_LEN
    })
    df = df.merge(weather_df, on='timestamp', how='left')
    df['hour'] = df['timestamp'].dt.hour
    df['day'] = df['timestamp'].dt.day
    df['month'] = df['timestamp'].dt.month
    df['zenith_angle'] = df['timestamp'].apply(lambda x: calculate_zenith_angle(lat, lon, x))
    
    # Normalize
    numeric_cols = ['temperature', 'humidity', 'cloud_cover', 'zenith_angle']
    df[numeric_cols] = scaler.transform(df[numeric_cols])
    
    # Predict
    X, _ = prepare_sequences(df, SEQ_LEN)
    prediction = model.predict(X)
    prediction = scaler.inverse_transform(prediction.reshape(-1, 1))  # Reverse scaling
    return JSONResponse(content={"predicted_generation_kWh": prediction.tolist()})