const fs = require("fs");

let critical = 0;
let high = 0;
let medium = 0;
let low = 0;
try {
    const files = fs.readdirSync("./codeql-results.sarif");

    const data = fs.readFileSync("./codeql-results.sarif/" + files[0], { encoding: "utf8" });
    const sarifJson = JSON.parse(data);

    sarifJson.runs.forEach((run) => {
        const serifRules = [];
        sarifJson.runs[0].tool.extensions.forEach((extension) => {
            if(extension?.rules) {
                serifRules.push(...extension.rules);
            }
        });

        run.results.forEach((result) => {
            const ruleDetails = serifRules.find((rule) => rule.id === result.ruleId);
            const severity = parseFloat(ruleDetails?.properties?.["security-severity"] || "0");
            if (ruleDetails) {
                if (severity >= 9) {
                    critical = critical + 1;
                } else if (severity >= 7 && severity < 9) {
                    high = high + 1;
                } else if (severity >= 4 && severity < 7) {
                    medium = medium + 1;
                } else if (severity > 0 && severity < 4) {
                    low = low + 1;
                }
            }
        });
    }
    );
    console.log(critical, high, medium, low);
} catch (error) {
    console.log(critical, high, medium, low);
}
