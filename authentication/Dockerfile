# First Build
FROM node:22.12.0-alpine AS build
ARG GITHUB_TOKEN
RUN mkdir -p /home/<USER>/app && mkdir -p /home/<USER>/app/build
ENV GITHUB_TOKEN $GITHUB_TOKEN
WORKDIR /home/<USER>/app
COPY package.json ./
RUN echo //npm.pkg.github.com/:_authToken=$GITHUB_TOKEN >> ~/.npmrc
RUN echo @moxfive-llc:registry=https://npm.pkg.github.com/ >> ~/.npmrc
RUN npm install --only=prod
RUN echo > ~/.npmrc
COPY . .

# Second build
FROM node:22.12.0-alpine
WORKDIR /home/<USER>/app
EXPOSE 3000
COPY --from=build /home/<USER>/app /home/<USER>/app
RUN npm run build
USER node
CMD ["npm", "start"]
