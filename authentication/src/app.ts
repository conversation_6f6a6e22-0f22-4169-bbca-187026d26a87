import { error<PERSON><PERSON><PERSON> } from "@moxfive-llc/common";
import cookieParser from "cookie-parser";
import express, { json, urlencoded } from "express";
import "express-async-errors";
import helmet from "helmet";
import hpp from "hpp";
import cors from "cors";
import { rateLimiter } from "./util/rate-limitter";

// routes
import routes from "./routes";

/**
 * @swagger
 * components:
 *   schemas:
 *     addMemberInOrganization:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           example: "f6337d69-56a2-4e62-ac5e-47dbde966b93"
 *         mail:
 *           type: string
 *           example: "<EMAIL>"
 *         givenName:
 *           type: string
 *           example: "Test"
 *         surname:
 *           type: string
 *           example: "User"
 *         otherMails:
 *           type: string
 *           example: ["<EMAIL>"]
 *         streetAddress:
 *           type: string
 *           example: "102, Blue house"
 *         country:
 *           type: string
 *           example: "USA"
 *         state:
 *           type: string
 *           example: "New York"
 *         city:
 *           type: string
 *           example: "New York"
 *         postalCode:
 *           type: string
 *           example: "12345"
 *         businessPhones:
 *           type: string
 *           example: "+***********"
 *         accountEnabled:
 *           type: boolean
 *           example: true
 *         jobTitle:
 *           type: string
 *           example: "Software Engineer"
 *         companyName:
 *           type: string
 *           example: "RapidOps Inc"
 *       required:
 *         - id
 *         - mail
 *     updateUserDetails:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *           example: "Test"
 *         lastName:
 *           type: string
 *           example: "User"
 *         alternateEmail:
 *           type: string
 *           example: "<EMAIL>"
 *         streetAddress:
 *           type: string
 *           example: "102, Blue house"
 *         country:
 *           type: string
 *           example: "USA"
 *         state:
 *           type: string
 *           example: "New York"
 *         city:
 *           type: string
 *           example: "New York"
 *         postalCode:
 *           type: string
 *           example: "12345"
 *         officePhone:
 *           type: string
 *           example: "+***********"
 *         jobTitle:
 *           type: string
 *           example: "Software Engineer"
 *         companyName:
 *           type: string
 *           example: "RapidOps Inc"
 *     updateStatus:
 *       type: object
 *       properties:
 *         isEnabled:
 *           type: boolean
 *           example: true
 *       required:
 *         - isEnabled
 *     updateUserEmail:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           example: "<EMAIL>"
 *       required:
 *         - email
 */

// declare global {
//     // eslint-disable-next-line @typescript-eslint/no-namespace
//     namespace Express {
//         interface Request {
//             flexibleFieldValuesMap?: Map<string, string>,
//             activePartner?: boolean,
//             organizationIdToNameMap: Map<string, string>
//         }
//     }
// }

const app = express();

export function addMiddleWaresToApp() {
// middleware which blocks requests when we're too busy
//     app.use(toobusyChecker);

    app.locals.microsoftGraphAPIToken = {
        accessToken: "",
        expiresIn: 0
    };
    app.locals.auth0APIToken = {
        accessToken: "",
        expiresIn: 0
    };
    app.locals.ConfidentialClientApplication = { clientSecret: "" };
    app.locals.cookieOptions = {
        signed: process.env.NODE_ENV !== "test",
        secure: true,
        httpOnly: true,
        sameSite: "strict",
    };

    app.set("trust proxy", true);
    app.use(rateLimiter());
    app.use(helmet());
    app.use(json({
        limit: "60mb"
    }));
    app.use(urlencoded({
        extended: true,
        limit: "60mb"
    }));
    app.use(hpp());
    app.use(cookieParser(process.env.COOKIE_SECRET));
    app.use(
        cors({
            origin: "*",
            credentials: true,
            preflightContinue: true
        }),
    );
    app.use("/", routes);

    app.use(errorHandler);
}

export default app;
