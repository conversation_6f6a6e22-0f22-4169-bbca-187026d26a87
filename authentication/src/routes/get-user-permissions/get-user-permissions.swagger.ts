/**
 * @swagger
 * /v1/users/me/permissions:
 *   get:
 *     name: Get user permissions
 *     summary: Get user permissions
 *     description: This API will fetch authenticated user permissions.
 *     tags:
 *       - Users
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               [
 *                 {
 *                   "action": "read",
 *                   "subject": "Organization"
 *                 },
 *                 {
 *                   "action": "write",
 *                   "subject": "Organization"
 *                 },
 *                 {
 *                    "action": "read",
 *                    "subject": "User"
 *                 },
 *                 {
 *                    "action": "write",
 *                    "subject": "User"
 *                 },
 *                 {
 *                    "action": "read",
 *                    "subject": "Role"
 *                 }
 *               ]
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "User not found" } ] }
 */
