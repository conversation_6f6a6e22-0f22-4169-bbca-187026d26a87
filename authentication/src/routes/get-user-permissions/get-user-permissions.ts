/* eslint-disable max-depth */
import {
    currentUser,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHand<PERSON>
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { Action } from "../../models/action";
import { Policy } from "../../models/policy";
import { User } from "../../models/user";
import { aesEncrypt } from "../../util/aes-encypt";

const router = express.Router();

const getEncryptedPermissions = (permissions: any) => {
    return aesEncrypt(JSON.stringify(permissions), process.env.RESPONSE_ENCRYPTION_KEY!);
};

router.get(
    "/v1/users/me/permissions",
    responseHand<PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction):Promise<any> => {
        try {
            req.action = "GetUserPermissions";

            const permissions: {
                superAdminAssigned: boolean,
                global: string[],
                application: {
                    incidents: {
                        id: string,
                        actions: string[]
                    }[],
                    resiliences: {
                        id: string,
                        actions: string[]
                    }[]
                }
            } = {
                superAdminAssigned: false,
                global: [],
                application: {
                    incidents: [],
                    resiliences: []
                }
            };
            // Step 1: Check whether user exists or not
            const userId = req.currentUser?.id;
            const user = await User.findById(userId).select("policyIds applicationPolicyIds isEnabled").lean().exec();
            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Step 2: If user is not enabled return blank permissions
            if (!user.isEnabled) {
                return res.send(getEncryptedPermissions(permissions));
            }

            // Check whether the super admin is assigned or not
            const isSuperAdminAssigned = !!user.policyIds?.map(String).includes(process.env.SUPER_ADMIN_POLICY_ID || "");
            permissions.superAdminAssigned = isSuperAdminAssigned;

            // Step 3: If any global policies assigned then fetch actions of that policies
            if(user.policyIds && user.policyIds.length) {
                const policies = await Policy.find({ _id: { $in: user.policyIds }, isEnabled: true, type: "Global" }, { actionIds: 1, _id: 0 }).lean().exec();

                if (policies && policies.length) {
                    let actionIds: string[] = [];
                    policies.forEach(policy => actionIds = [...actionIds, ...policy.actionIds]);
                    const actions = await Action.find({ _id: { $in: actionIds } }, { name: 1, _id: 0 }).lean().exec();
                    const actionsResp = new Set(
                        actions.map(action => action.name)
                    );
                    permissions.global = [...actionsResp];
                }
            }

            // Step 4: If user has any application policies assigned then fetch actions of that
            if(user.applicationPolicyIds && user.applicationPolicyIds.length) {
                // Fetch policyIds of application policy assigned
                const policyIds: string[] = user.applicationPolicyIds.map(policy => String(policy.policyId));

                if(policyIds.length) {
                    // Fetch details of policies
                    const policiyActionsMap = new Map();
                    const policies = await Policy.find({ _id: { $in: policyIds }, isEnabled: true, type: "Application" }, { actionIds: 1, _id: 1 }).lean().exec();

                    if (policies && policies.length) {
                        // Loop through all policies and fetch actions of that policy and create map of policy and actions
                        for(const policy of policies) {
                        // eslint-disable-next-line no-await-in-loop
                            const actions = await Action.find({ _id: { $in: policy.actionIds } }, { name: 1, _id: 0 }).lean().exec();
                            const actionNames = actions.map(action => action.name);
                            policiyActionsMap.set(String(policy._id), actionNames);
                        }

                        const applicationPolicyMap: Map<string, {type: string, actions: string[]}> = new Map();
                        // Loop through all application policyIds
                        user.applicationPolicyIds.forEach(policy => {
                            // Fetch actions of that policy
                            const actions = policiyActionsMap.get(String(policy.policyId));
                            if(actions && actions.length) {
                                // Add entry in the applicationPolicyMap
                                const applicationPolicy = applicationPolicyMap.get(String(policy.applicationId));
                                if(applicationPolicy) {
                                    applicationPolicyMap.set(String(policy.applicationId), {
                                        type: policy.applicationType,
                                        actions: [...applicationPolicy.actions, ...actions]
                                    });
                                }
                                else {
                                    applicationPolicyMap.set(String(policy.applicationId), {
                                        type: policy.applicationType,
                                        actions
                                    });
                                }
                            }
                        });

                        // Loop through applicationPolicyMap and add actions in permissions object
                        for (const [id, value] of applicationPolicyMap.entries()) {
                            const actions = Array.from(new Set(value.actions));

                            if(value.type === "Incident") {
                                permissions.application.incidents.push({
                                    id,
                                    actions
                                });
                            }
                            else if(value.type === "Resilience") {
                                permissions.application.resiliences.push({
                                    id,
                                    actions
                                });
                            }
                        }
                    }
                }
            }

            // Step 5: send response
            res.sendResponse(getEncryptedPermissions(permissions), {});
        }
        catch (error) {
            console.error("Authentication.GetUserPermissions");
            console.error(error);
            next(error);
        }
    }
);

export { router as getUserPermissionsRouter };
