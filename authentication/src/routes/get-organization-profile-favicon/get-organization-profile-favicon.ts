// import {
//     currentUser, downloadFile, File, InternalServerError,
//     NotFoundCode,
//     requireAuth, ResourceNotFoundError,
//     responseHandler,
//     validateRequest
// } from "@moxfive-llc/common";
// import express, { Request, Response, NextFunction } from "express";
// import { Organization } from "../../models/organization";
// import fs from "fs";
// import { organizationPathParamValidation } from "../../validations/general/organizaiton-path-params-validation";
//
// const router = express.Router();
//
// router.get(
//     "/v1/organizations/:organizationId/images/:type",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     organizationPathParamValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             req.action = "GetOrganizationProfileFavicon";
//
//             const { organizationId, type } = req.params;
//
//             if(type !== "profile" && type !== "favicon") {
//                 throw new InternalServerError();
//             }
//
//             const organization = await Organization.findById(organizationId);
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }
//
//             const existingFile = await File.findOne({
//                 parent: organization.bucketName,
//                 parentId: organizationId,
//                 entity: organization.imageAndLogoSame ? "profile" : type,
//                 entityId: organizationId,
//             }).lean().exec();
//
//             if(!existingFile) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Image not found");
//             }
//
//             const { filePath, file } = await downloadFile({
//                 parent: organization.bucketName ?? "",
//                 parentId: organizationId,
//                 entity: organization.imageAndLogoSame ? "profile" : type,
//                 entityId: organizationId,
//                 fileId: existingFile._id as string,
//                 AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
//             });
//
//             // eslint-disable-next-line security/detect-non-literal-fs-filename
//             const fileStream = fs.createReadStream(
//                 filePath
//             );
//
//             // If the file ends with .svg then set content type to image/svg+xml otherwise application/octet-stream
//             if(existingFile.originalFileName.endsWith(".svg")) {
//                 res.setHeader("Content-Type", "image/svg+xml");
//             }
//             else {
//                 res.setHeader("Content-Type", "application/octet-stream");
//             }
//
//             res.setHeader("Content-Disposition", `attachment; filename="${file.fileName}"`);
//             fileStream.pipe(res);
//         }
//         catch (error) {
//             console.error("Authentication.GetOrganizationProfileFavicon");
//             console.error(error);
//             next(error);
//         }
//     }
// );
//
// export { router as getOrganizationProfileFaviconRouter };
