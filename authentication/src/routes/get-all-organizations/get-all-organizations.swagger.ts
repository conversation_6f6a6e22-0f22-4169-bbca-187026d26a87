/**
 * @swagger
 * /v1/organizations:
 *   get:
 *     name: Fetch all Organizations
 *     summary: Fetch all Organizations with filters
 *     description: Fetch all Organizations with filters
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         description: query.
 *         schema:
 *           type : number
 *           format: int64
 *           minimum: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         description: limit.
 *         schema:
 *           type : number
 *           format: int64
 *           minimum: 1
 *           maximum: 999
 *       - in: query
 *         name: search
 *         required: false
 *         description: search.
 *         schema:
 *           type : string
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               examples:
 *                 First:
 *                   summary: Get all Organizations with no filter.
 *                   description: No parameter is passed in this request.
 *                   value:
 *                    {
 *                        "totalRows": 1,
 *                        "data": [
 *                            {
 *                                "description": "Forensics description",
 *                                "name": "Forensics Inc.",
 *                                "industry": [
 *                                    {
 *                                        "id": "6245a4ed9f0bb12168847b53",
 *                                        "name": "Aerospace &amp; Defense"
 *                                    }
 *                                ],
 *                                "website": "forensics.com",
 *                                "phone": "+19876543210",
 *                                "executiveSponsor": "executiveSponsor",
 *                                "technicalSponsor": "technicalSponsor",
 *                                "salesSponsor": "salesSponsor",
 *                                "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                                "partnerStatus": [
 *                                    {
 *                                        "id": "6245a87bf41c4d94454e3b45",
 *                                        "name": "Established Partner"
 *                                    }
 *                                ],
 *                                "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                                "coverageStates": [
 *                                    {
 *                                        "id": "6245a914f41c4d94454e3b5d",
 *                                        "name": "Alabama"
 *                                    },
 *                                    {
 *                                        "id": "6245aadf3685b986e787fd1d",
 *                                        "name": "Alaska"
 *                                    },
 *                                    {
 *                                        "id": "6245ab6e043534be1086dff4",
 *                                        "name": "California"
 *                                    }
 *                                ],
 *                                "offerings": [
 *                                    {
 *                                        "id": "507f1f77bcf86cd799439011",
 *                                        "name": "Test"
 *                                    }
 *                                ],
 *                                "partnerEula": "partnerEula.com",
 *                                "partnerTermsConditions": "partnerTermsConditions.com",
 *                                "inboundRequestInfo": "inboundRequestInfo",
 *                                "pmSponsor": "pmSponsor",
 *                                "taSponsor": "taSponsor",
 *                                "parnterSalesSponsor": "parnterSalesSponsor",
 *                                "numberOfPMs": 1,
 *                                "numberOfLeads": 1,
 *                                "numberOfEngineers": 7,
 *                                "languages": [],
 *                                "isEnabled": true,
 *                                "createdAt": "2022-04-04T11:28:47.312Z",
 *                                "updatedAt": "2022-04-04T11:28:47.317Z",
 *                                "id": "624ad66fc59c559217f34d5b",
 *                                "owners": 0,
 *                                "members": 0,
 *                                "organizationTypes": [
 *                                    {
 *                                        "name": "Forensics",
 *                                        "id": "6225df3b52eb2f857a20b3d1"
 *                                    }
 *                                ]
 *                            }
 *                        ]
 *                    }
 *                 Second:
 *                   summary: Get all organizations with serach filter applied.
 *                   description: in this we pass search parameter in request /api/v1/organizations?search=ABC
 *                   value:
 *                    {
 *                        "totalRows": 1,
 *                        "data": [
 *                            {
 *                                "description": "Forensics description",
 *                                "name": "Forensics Inc.",
 *                                "industry": [
 *                                    {
 *                                        "id": "6245a4ed9f0bb12168847b53",
 *                                        "name": "Aerospace &amp; Defense"
 *                                    }
 *                                ],
 *                                "website": "forensics.com",
 *                                "phone": "+19876543210",
 *                                "executiveSponsor": "executiveSponsor",
 *                                "technicalSponsor": "technicalSponsor",
 *                                "salesSponsor": "salesSponsor",
 *                                "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                                "partnerStatus": [
 *                                    {
 *                                        "id": "6245a87bf41c4d94454e3b45",
 *                                        "name": "Established Partner"
 *                                    }
 *                                ],
 *                                "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                                "coverageStates": [
 *                                    {
 *                                        "id": "6245a914f41c4d94454e3b5d",
 *                                        "name": "Alabama"
 *                                    },
 *                                    {
 *                                        "id": "6245aadf3685b986e787fd1d",
 *                                        "name": "Alaska"
 *                                    },
 *                                    {
 *                                        "id": "6245ab6e043534be1086dff4",
 *                                        "name": "California"
 *                                    }
 *                                ],
 *                                "offerings": [
 *                                    {
 *                                        "id": "507f1f77bcf86cd799439011",
 *                                        "name": "Test"
 *                                    }
 *                                ],
 *                                "partnerEula": "partnerEula.com",
 *                                "partnerTermsConditions": "partnerTermsConditions.com",
 *                                "inboundRequestInfo": "inboundRequestInfo",
 *                                "pmSponsor": "pmSponsor",
 *                                "taSponsor": "taSponsor",
 *                                "parnterSalesSponsor": "parnterSalesSponsor",
 *                                "numberOfPMs": 1,
 *                                "numberOfLeads": 1,
 *                                "numberOfEngineers": 7,
 *                                "languages": [],
 *                                "isEnabled": true,
 *                                "createdAt": "2022-04-04T11:28:47.312Z",
 *                                "updatedAt": "2022-04-04T11:28:47.317Z",
 *                                "id": "624ad66fc59c559217f34d5b",
 *                                "owners": 0,
 *                                "members": 0,
 *                                "organizationTypes": [
 *                                    {
 *                                        "name": "Forensics",
 *                                        "id": "6225df3b52eb2f857a20b3d1"
 *                                    }
 *                                ]
 *                            }
 *                        ]
 *                    }
 *                 Third:
 *                   summary: Get all organizations with page and limit applied.
 *                   description: in this we pass page and limit parameters in request /api/v1/organizations?page=2&limit=1
 *                   value:
 *                    {
 *                        "totalRows": 1,
 *                        "data": [
 *                            {
 *                                "description": "Forensics description",
 *                                "name": "Forensics Inc.",
 *                                "industry": [
 *                                    {
 *                                        "id": "6245a4ed9f0bb12168847b53",
 *                                        "name": "Aerospace &amp; Defense"
 *                                    }
 *                                ],
 *                                "website": "forensics.com",
 *                                "phone": "+19876543210",
 *                                "executiveSponsor": "executiveSponsor",
 *                                "technicalSponsor": "technicalSponsor",
 *                                "salesSponsor": "salesSponsor",
 *                                "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                                "partnerStatus": [
 *                                    {
 *                                        "id": "6245a87bf41c4d94454e3b45",
 *                                        "name": "Established Partner"
 *                                    }
 *                                ],
 *                                "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                                "coverageStates": [
 *                                    {
 *                                        "id": "6245a914f41c4d94454e3b5d",
 *                                        "name": "Alabama"
 *                                    },
 *                                    {
 *                                        "id": "6245aadf3685b986e787fd1d",
 *                                        "name": "Alaska"
 *                                    },
 *                                    {
 *                                        "id": "6245ab6e043534be1086dff4",
 *                                        "name": "California"
 *                                    }
 *                                ],
 *                                "offerings": [
 *                                    {
 *                                        "id": "507f1f77bcf86cd799439011",
 *                                        "name": "Test"
 *                                    }
 *                                ],
 *                                "partnerEula": "partnerEula.com",
 *                                "partnerTermsConditions": "partnerTermsConditions.com",
 *                                "inboundRequestInfo": "inboundRequestInfo",
 *                                "pmSponsor": "pmSponsor",
 *                                "taSponsor": "taSponsor",
 *                                "parnterSalesSponsor": "parnterSalesSponsor",
 *                                "numberOfPMs": 1,
 *                                "numberOfLeads": 1,
 *                                "numberOfEngineers": 7,
 *                                "languages": [],
 *                                "isEnabled": true,
 *                                "createdAt": "2022-04-04T11:28:47.312Z",
 *                                "updatedAt": "2022-04-04T11:28:47.317Z",
 *                                "id": "624ad66fc59c559217f34d5b",
 *                                "owners": 0,
 *                                "members": 0,
 *                                "organizationTypes": [
 *                                    {
 *                                        "name": "Forensics",
 *                                        "id": "6225df3b52eb2f857a20b3d1"
 *                                    }
 *                                ]
 *                            }
 *                        ]
 *                    }
 *       400:
 *         description: Invalid Query params
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Search term must be more than 1 character",
 *                          "field": "search"
 *                      },
 *                      {
 *                          "message": "Page number must be positive integer",
 *                          "field": "page"
 *                      },
 *                      {
 *                          "message": "Limit must be positive integer between 1-999",
 *                          "field": "limit"
 *                      }
 *                  ]
 *              }
 *
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 */
