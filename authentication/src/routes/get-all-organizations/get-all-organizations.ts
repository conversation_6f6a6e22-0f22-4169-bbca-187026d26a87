// import {
//     currentUser,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     requireAuth,
//     responseHandler,
//     validateRequest
// } from "@moxfive-llc/common";
// import express, { NextFunction, Request, Response } from "express";

// import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
// import { OrganizationFields } from "../../services/organization-fields";
// import { FilterQuery, GetOrganizationsDetails, SortQuery } from "../../interfaces";
// import { Organization } from "../../models/organization";
// import { getAllOrganizationsValidation } from "./get-all-organizations.validation";
// import { fetchDefaultSortObject, escapeRegExp, getUsersByIds, unSanitizeEmail, unSanitizeURI } from "../../util";
// import { authenticationFields } from "../../util/authentication-fields";
// import {
//     fetchFiltersFromMultipleSectionsBasedOnPermission
// } from "../../util/fetch-filters-from-multiple-sections-based-on-permission";
// import {
//     fetchSortFromMultipleSectionsBasedOnPermission
// } from "../../util/fetch-sort-from-multiple-sections-based-on-permission";

// const router = express.Router();

// router.get("/v1/organizations",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     getAllOrganizationsValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction):Promise<any> => {
//         try {
//             // Step 1: Check user has permission to get all organizations
//             const hasPermission = await hasGlobalAction(req, "ListOrganizations");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const defaultLimit = 100;

//             // Step 2: Make search filter and sort query
//             // Make search query, if name is there then add it in serach query
//             const searchQuery: { name?: { $regex: string, $options: string } } = {};
//             const { search, page = 1, limit = defaultLimit, filter, sort  } = req.query;

//             if (search) {
//                 searchQuery.name = { $regex: `${escapeRegExp(search as string)}`, $options: "i" };
//             }

//             const skip = (page as number - 1) * (limit as number);

//             // Define sections and assigned actions set
//             const sections = [
//                 {
//                     path: authenticationFields.organizations
//                 },
//                 {
//                     path: authenticationFields.modifiedAt
//                 }
//             ];

//             const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);
//             const filtersQuery: FilterQuery = {};

//             // If filter is provided then prepare filter query
//             if(filter) {
//                 const filters = fetchFiltersFromMultipleSectionsBasedOnPermission({
//                     filter: filter as string,
//                     sections
//                 });
//                 Object.assign(filtersQuery, filters);
//             }

//             // If sort is provided then prepare sort query
//             if(sort) {
//                 const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
//                     sort: sort as string,
//                     sections
//                 });
//                 Object.assign(sortQuery, sortObj);
//             }

//             // Step 3: Fetch all organizations
//             let aggregateQuery:any = [
//                 { $match: searchQuery },
//                 { $match: filtersQuery },
//                 {
//                     $lookup: {
//                         from: "organizationtypes",
//                         localField: "organizationTypeIds",
//                         foreignField: "_id",
//                         as: "organizationTypes"
//                     }
//                 }
//             ];

//             if(Object.keys(sortQuery).length) {
//                 aggregateQuery.push({ $sort: sortQuery });
//             }

//             aggregateQuery = aggregateQuery.concat([
//                 {
//                     $project: {
//                         "organizationTypeIds": 0,
//                         // "azureId": 0,
//                         "version": 0,
//                         "owner": 0,
//                         // "member": 0
//                     }
//                 },
//                 {
//                     $facet: { totalRows: [{ $count: "count" }], data: [{ $skip: skip }, { $limit: (limit as number) }] }
//                 }
//             ]);

//             const organizationDetails: {totalRows: {count: number}[], data: GetOrganizationsDetails[]}[] = await Organization.aggregate(aggregateQuery, {
//                 collation: {
//                     locale: "en",
//                     numericOrdering: true
//                 }
//             });

//             if(!organizationDetails || !organizationDetails.length || !organizationDetails[0].totalRows ||
//                 !organizationDetails[0].totalRows.length || !organizationDetails[0].totalRows[0].count || !organizationDetails[0].data) {
//                 return res.json({
//                     totalRows: 0,
//                     data: []
//                 });
//             }

//             const organizationsCount = organizationDetails[0].totalRows[0].count;
//             const organizations = organizationDetails[0].data;

//             // Step 4: get all custom fields
//             const flexibleFieldsList = await OrganizationFlexibleField.aggregate([
//                 {
//                     $unwind: "$values"
//                 },
//                 {
//                     $project: {
//                         id: "$values._id",
//                         name: "$values.value",
//                         _id: 0
//                     }
//                 }
//             ]);

//             // Step 5: fetch all moxfivePMSponsor, moxfiveTASponsor and moxfiveSalesSponsor
//             const userIds: string[] = [];
//             organizations.forEach(org => {
//                 if(org.moxfivePMSponsor) {
//                     userIds.push(org.moxfivePMSponsor as string);
//                 }
//                 if(org.moxfiveTASponsor) {
//                     userIds.push(org.moxfiveTASponsor as string);

//                 }
//                 if(org.moxfiveSalesSponsor) {
//                     userIds.push(org.moxfiveSalesSponsor as string);
//                 }
//             });

//             const usersMap = await getUsersByIds(userIds);

//             // Step 6: Format the response
//             const result: GetOrganizationsDetails[] = await Promise.all(organizations.map(async (organization) => {
//                 let org = { ...organization };

//                 // unSanitize values
//                 if (org.website) {
//                     org.website = unSanitizeURI(org.website);
//                 }
//                 if (org.hotlineEmail) {
//                     org.hotlineEmail = unSanitizeEmail(org.hotlineEmail);
//                 }
//                 if (org.billingContactEmail) {
//                     org.billingContactEmail = unSanitizeEmail(org.billingContactEmail);
//                 }
//                 if(org.moxfivePMSponsor) {
//                     org.moxfivePMSponsor = {
//                         id: String(org.moxfivePMSponsor),
//                         name: usersMap.get(String(org.moxfivePMSponsor)) || ""
//                     };
//                 }
//                 if(org.moxfiveTASponsor) {
//                     org.moxfiveTASponsor = {
//                         id: String(org.moxfiveTASponsor),
//                         name: usersMap.get(String(org.moxfiveTASponsor)) || ""
//                     };
//                 }
//                 if(org.moxfiveSalesSponsor) {
//                     org.moxfiveSalesSponsor = {
//                         id: String(org.moxfiveSalesSponsor),
//                         name: usersMap.get(String(org.moxfiveSalesSponsor)) || ""
//                     };
//                 }

//                 // get all Flexible fields
//                 org = await OrganizationFields.addFlexibleFieldsData(org, flexibleFieldsList);

//                 org.id = org._id;
//                 org.organizationTypes = org.organizationTypes?.map(type => ({
//                     id: String(type._id),
//                     name: type.name
//                 }));

//                 delete org._id;
//                 return org;
//             }));

//             // Step 7: Send response
//             return res.sendResponse({
//                 totalRows: organizationsCount,
//                 data: result
//             }, {});
//         }
//         catch (error) {
//             console.error("Authentication.GetAllOrganizations");
//             console.error(error);
//             next(error);
//         }
//     });

// export { router as getAllOrganizationsRouter };
