import express, { NextFunction, Request, Response } from "express";
import {
    currentUser, hasGlobalAction, InsufficientPrivilagesError, NotFoundCode, requireAuth,
    ResourceNotFoundError, responseHandler, TargetType, validateRequest
} from "@moxfive-llc/common";
import { OrganizationV2 } from "../../models/v2/oragnizations-v2";
import { intersectTwoObjects, pickFromObject } from "../../util";
import { Environment } from "../../models/organization-enviroment";
import { flexibleFieldValidation } from "../../util/flexible-field-validation-middleware";
import { UpdateEnvironmentBackupsDetailValidation } from "./update-enviroment-backup.validation";
import { authenticationFields } from "../../util/authentication-fields";
import { AuditLog } from "../../services/audit-log";
import { MakeSingleSectionFieldsFlexibleFields } from "../../util/make-single-section-fields-flexible-fields";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../util/v2/organization-updated-publisher-wrapper";
import { MongoTransaction } from "../../services/mongo-transaction";
import { organizationV2ToV1Sync } from "../../util/organization-v2-to-v1-sync";
import { OperationTypesEnums } from "../../enums/operation-types.enum";

const router = express.Router();

// Fetch all fields and flexible fields
const { fields, flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.environment.backups);

router.put("/v1/organizations/:organizationId/environment/backup",
    responseHandler,
    currentUser,
    requireAuth,
    UpdateEnvironmentBackupsDetailValidation(),
    validateRequest,
    flexibleFieldValidation(flexibleFieldsNameKey),
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1:  Check if user has a ability
            const hasPermission = await hasGlobalAction(req, "UpdateOrganizationEnvironmentBackupDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Step 2: Check Organization is valid or not
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3 : Get Environment Backup Record
            let modifiedProperties: any = [];

            const environmentBackup: any = await Environment.findOne({ organizationId }).session(mongoTransaction.session);
            // If Environment Backup not found
            if (!environmentBackup) {
                // If enviroment not present then create new record
                const data = pickFromObject(req.body, fields);
                await Environment.build({
                    backup: {
                        ...data,
                        createdAt: Date.now(),
                        createdBy: req.currentUser?.id || null
                    },
                    organizationId: organizationId
                }).save({ session: mongoTransaction.session });

                await organization.save({ session: mongoTransaction.session });

                await organizationV2ToV1Sync({
                    organization: organization.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit transaction
                await mongoTransaction.commitTransaction();

                await OrganizationUpdatedPublisherWrapperV2(organization);

                // Prepare data for audit logs
                modifiedProperties = AuditLog.prepareModifiedProperties({
                    data,
                    req,
                    flexibleFieldsNameKey,
                    target: TargetType.ORGANIZATION
                });

                return res.sendResponse({
                    meta: {
                        message: "Environment Backup updated successfully."
                    }
                }, {
                    targets: [{
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    }],
                    correlation: TargetType.ORGANIZATION,
                    correlationId: organizationId,
                    modifiedProperties
                });
            }

            // Step 4: Fetch only those fields which are chagned and updated that
            const environmentBackupDetail = pickFromObject(environmentBackup.backup ?? {}, fields);
            const updatedData = intersectTwoObjects(environmentBackupDetail, req.body);

            if (Object.keys(updatedData).length) {
                environmentBackup.backup = environmentBackup.backup ?? {};
                Object.assign(environmentBackup.backup, updatedData);
                environmentBackup.backup.updatedBy = req.currentUser?.id || null;

                await Promise.all([environmentBackup.save({ session: mongoTransaction.session }), organization.save({ session: mongoTransaction.session })]);

                await organizationV2ToV1Sync({
                    organization: organization.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit transaction
                await mongoTransaction.commitTransaction();

                await OrganizationUpdatedPublisherWrapperV2(organization);

                // Prepare data for audit logs
                const data = { ...updatedData };
                const oldData = { ...environmentBackupDetail };

                modifiedProperties = AuditLog.prepareModifiedProperties({
                    data,
                    flexibleFieldsNameKey,
                    req,
                    oldData,
                    target: TargetType.ORGANIZATION
                });
            }

            // Step 5:  Send response
            res.sendResponse({
                meta: {
                    message: "Environment Backup updated successfully."
                }
            }, modifiedProperties.length ? {
                targets: [{
                    type: TargetType.ORGANIZATION,
                    details: {
                        id: organizationId,
                        name: organization.name
                    }
                }],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId,
                modifiedProperties
            } : {});
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateEnvironmentBackup");
            console.error(error);
            next(error);
        }
    });

export { router as UpdateEnvironmentBackupRouter };
