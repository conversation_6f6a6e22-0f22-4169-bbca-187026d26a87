/**
 * @swagger
 * /v1/organizations/{organizationId}:
 *   get:
 *     name: Get specific organization details
 *     summary: Get specific organization details
 *     description: Get specific organization details
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               examples:
 *                 First:
 *                   summary: Client Organization
 *                   description: Client Organization
 *                   value:
 *                    {
 *                        "description": "Description",
 *                        "name": "Organization Name",
 *                        "industry": [
 *                            {
 *                                "id": "6245a4ed9f0bb12168847b53",
 *                                "name": "Aerospace &amp; Defense"
 *                            }
 *                        ],
 *                        "website": "abc.com",
 *                        "phone": "+19876543210",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "billingAddresses": [
 *                            {
 *                                "addressline1": "2613 Jett Lane",
 *                                "addressline2": "Opposite white house",
 *                                "city": "Los Angeles",
 *                                "state": "California",
 *                                "zip": "90017",
 *                                "country": "United States",
 *                                "contact": "+13107546214"
 *                            }
 *                        ],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:32:25.396Z",
 *                        "updatedAt": "2022-04-04T11:38:42.288Z",
 *                        "id": "624ad749c59c559217f34d62",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Client",
 *                                "id": "6222096db401b9512139172a"
 *                            }
 *                        ]
 *                    }
 *                 Second:
 *                   summary: Forensics Organization
 *                   description: Forensics Organization
 *                   value:
 *                    {
 *                        "description": "Forensics description",
 *                        "name": "Forensics Inc.",
 *                        "industry": [
 *                            {
 *                                "id": "6245a4ed9f0bb12168847b53",
 *                                "name": "Aerospace &amp; Defense"
 *                            }
 *                        ],
 *                        "website": "forensics.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "partnerStatus": [
 *                            {
 *                                "id": "6245a87bf41c4d94454e3b45",
 *                                "name": "Established Partner"
 *                            }
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            {
 *                                "id": "6245a914f41c4d94454e3b5d",
 *                                "name": "Alabama"
 *                            },
 *                            {
 *                                "id": "6245aadf3685b986e787fd1d",
 *                                "name": "Alaska"
 *                            },
 *                            {
 *                                "id": "6245ab6e043534be1086dff4",
 *                                "name": "California"
 *                            }
 *                        ],
 *                        "offerings": [
 *                            {
 *                                "id": "507f1f77bcf86cd799439011",
 *                                "name": "Test"
 *                            }
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": [],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:28:47.312Z",
 *                        "updatedAt": "2022-04-04T11:28:47.317Z",
 *                        "id": "624ad66fc59c559217f34d5b",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Forensics",
 *                                "id": "6225df3b52eb2f857a20b3d1"
 *                            }
 *                        ]
 *                    }
 *                 Third:
 *                   summary: Recovery Organization
 *                   description: Recovery Organization
 *                   value:
 *                    {
 *                        "description": "Recovery description",
 *                        "name": "Recovery Inc.",
 *                        "industry": [],
 *                        "website": "recovery.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "partnerStatus": [
 *                            {
 *                                "id": "6245a87bf41c4d94454e3b45",
 *                                "name": "Established Partner"
 *                            }
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            {
 *                                "id": "6245a914f41c4d94454e3b5d",
 *                                "name": "Alabama"
 *                            },
 *                            {
 *                                "id": "6245aadf3685b986e787fd1d",
 *                                "name": "Alaska"
 *                            },
 *                            {
 *                                "id": "6245ab6e043534be1086dff4",
 *                                "name": "California"
 *                            }
 *                        ],
 *                        "offerings": [
 *                            {
 *                                "id": "507f1f77bcf86cd799439011",
 *                                "name": "Test"
 *                            }
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": [],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:27:32.458Z",
 *                        "updatedAt": "2022-04-04T11:27:32.462Z",
 *                        "id": "624ad624c59c559217f34d54",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Recovery",
 *                                "id": "6225df5052eb2f857a20b3d2"
 *                            }
 *                        ]
 *                    }
 *                 Fourth:
 *                   summary: Negotiator Organization
 *                   description: Negotiator Organization
 *                   value:
 *                    {
 *                        "description": "Negotiator description",
 *                        "name": "Negotiator Inc.",
 *                        "industry": [],
 *                        "website": "negotiator.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "partnerStatus": [
 *                            {
 *                                "id": "6245a87bf41c4d94454e3b45",
 *                                "name": "Established Partner"
 *                            }
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            {
 *                                "id": "6245a914f41c4d94454e3b5d",
 *                                "name": "Alabama"
 *                            },
 *                            {
 *                                "id": "6245aadf3685b986e787fd1d",
 *                                "name": "Alaska"
 *                            },
 *                            {
 *                                "id": "6245ab6e043534be1086dff4",
 *                                "name": "California"
 *                            }
 *                        ],
 *                        "offerings": [
 *                            {
 *                                "id": "507f1f77bcf86cd799439011",
 *                                "name": "Test"
 *                            }
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": [],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:24:49.338Z",
 *                        "updatedAt": "2022-04-04T11:24:49.345Z",
 *                        "id": "624ad581c59c559217f34d4d",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Negotiator",
 *                                "id": "6225df6952eb2f857a20b3d3"
 *                            }
 *                        ]
 *                    }
 *                 Fifth:
 *                   summary: Privacy Councel Organization
 *                   description: Privacy Councel Organization
 *                   value:
 *                    {
 *                        "description": "Privacy Counsel description",
 *                        "name": "PrivacyCounsel Inc.",
 *                        "industry": [
 *                            {
 *                                "id": "6245a4ed9f0bb12168847b53",
 *                                "name": "Aerospace &amp; Defense"
 *                            }
 *                        ],
 *                        "website": "privacycounsel.com",
 *                        "phone": "+19876543210",
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:20:10.570Z",
 *                        "updatedAt": "2022-04-04T11:20:10.574Z",
 *                        "id": "624ad46ac59c559217f34d43",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Privacy Counsel",
 *                                "id": "62245c062fe4a628f50da609"
 *                            }
 *                        ]
 *                    }
 *                 Sixth:
 *                   summary: Insurance Carrier Organization
 *                   description: Insurance Carrier Organization
 *                   value:
 *                    {
 *                        "description": "Insurance Carrier description",
 *                        "name": "Insurance Carrier Inc.",
 *                        "industry": [
 *                            {
 *                                "id": "6245a4ed9f0bb12168847b53",
 *                                "name": "Aerospace &amp; Defense"
 *                            }
 *                        ],
 *                        "website": "insurancecarrier.com",
 *                        "phone": "+19876543210",
 *                        "moxfiveHotline": true,
 *                        "hotlineEmail": "<EMAIL>",
 *                        "hotlinePhoneNumber": "+17418529630",
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:18:31.166Z",
 *                        "updatedAt": "2022-04-04T11:18:31.172Z",
 *                        "id": "624ad407c59c559217f34d3c",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Insurance Carrier",
 *                                "id": "62245e402fe4a628f50da616"
 *                            }
 *                        ]
 *                    }
 *                 Seventh:
 *                   summary: Monitoring Counsel Organization
 *                   description: Monitoring Counsel Organization
 *                   value:
 *                    {
 *                        "description": "Monitoring Counsel description",
 *                        "name": "Monitoring Counsel Inc.",
 *                        "industry": [
 *                            {
 *                                "id": "6245a4ed9f0bb12168847b53",
 *                                "name": "Aerospace &amp; Defense"
 *                            }
 *                        ],
 *                        "website": "monitoringcounsel.com",
 *                        "phone": "+19876543210",
 *                        "moxfiveHotline": true,
 *                        "hotlineEmail": "<EMAIL>",
 *                        "hotlinePhoneNumber": "+17318529630",
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:16:51.759Z",
 *                        "updatedAt": "2022-04-04T11:16:51.763Z",
 *                        "id": "624ad3a3c59c559217f34d35",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Monitoring Counsel",
 *                                "id": "622f1b81a3fa22fb674065b8"
 *                            }
 *                        ]
 *                    }
 *                 Eight:
 *                   summary: Multiple organizations support
 *                   description: Multiple organizations support
 *                   value:
 *                    {
 *                        "description": "Mixed description",
 *                        "name": "OT Ids3 Inc.",
 *                        "industry": [
 *                            {
 *                                "id": "6245a4ed9f0bb12168847b53",
 *                                "name": "Aerospace &amp; Defense"
 *                            }
 *                        ],
 *                        "website": "www.Mixed.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "billingAddresses": [
 *                            {
 *                                "addressline1": "1",
 *                                "addressline2": "1",
 *                                "city": "1",
 *                                "state": "1",
 *                                "zip": "01296",
 *                                "country": "1",
 *                                "contact": "+19876543210"
 *                            }
 *                        ],
 *                        "partnerStatus": [
 *                            {
 *                                "id": "6245a87bf41c4d94454e3b45",
 *                                "name": "Established Partner"
 *                            }
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            {
 *                                "id": "6245a914f41c4d94454e3b5d",
 *                                "name": "Alabama"
 *                            },
 *                            {
 *                                "id": "6245aadf3685b986e787fd1d",
 *                                "name": "Alaska"
 *                            }
 *                        ],
 *                        "offerings": [
 *                            {
 *                                "id": "507f1f77bcf86cd799439011",
 *                                "name": "Test"
 *                            }
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": [
 *                            {
 *                                "id": "6245ac16f8da2f965068ddfd",
 *                                "name": "Arabic"
 *                            },
 *                            {
 *                                "id": "6245ac16f8da2f965068ddfe",
 *                                "name": "Burmese"
 *                            }
 *                        ],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T09:01:55.290Z",
 *                        "updatedAt": "2022-04-04T09:01:55.299Z",
 *                        "id": "624ab403352e8bd405800d78",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Client",
 *                                "id": "6222096db401b9512139172a"
 *                            },
 *                            {
 *                                "name": "Negotiator",
 *                                "id": "6225df6952eb2f857a20b3d3"
 *                            }
 *                        ]
 *                    }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization id must be valid",
 *                          "field": "organizationId"
 *                      }
 *                  ]
 *              }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization not found"
 *                      }
 *                  ]
 *              }
 */
