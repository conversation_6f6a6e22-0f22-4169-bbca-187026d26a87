import { query } from "express-validator";

export const getADusersValidation = [
    query("top")
        .if(query("top").exists())
        .isInt({ gt: 0, lt: 1000 })
        .toInt()
        .withMessage("Top parameter must be a positive number and not more than 999."),

    query("search")
        .optional()
        .isLength({ min: 1 }).withMessage("Search term must not be blank."),

    query("skipToken")
        .optional()
        .isLength({ min: 1 }).withMessage("skipToken must not be blank."),
];
