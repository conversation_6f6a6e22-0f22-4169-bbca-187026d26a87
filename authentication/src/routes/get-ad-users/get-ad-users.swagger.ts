/**
 * @swagger
 * /v1/users/ad:
 *   get:
 *     name: Get AD users
 *     summary: Get AD users
 *     description: This API will fetch users from azure active directory.
 *     tags:
 *       - Users
 *     parameters:
 *       - in: query
 *         name: email
 *         required: false
 *         description: email
 *         schema:
 *           type : string
 *       - in: query
 *         name: name
 *         required: false
 *         description: displayName
 *         schema:
 *           type : string
 *       - in: query
 *         name: top
 *         required: false
 *         description: limit
 *         schema:
 *           type : number
 *           format: int64
 *           minimum: 1
 *           maximum: 999
 *       - in: query
 *         name: skipToken
 *         required: false
 *         description: skipToken
 *         schema:
 *           type : string
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Fetched users without any query params
 *                 description: Fetched users without any query params
 *                 value:
 *                   {
 *                     "totalRows": 2,
 *                     "data": [
 *                        {
 *                          "displayName": "<PERSON><PERSON><PERSON>",
 *                          "givenName": "<PERSON><PERSON><PERSON>",
 *                         "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
 *                          "mail": "<EMAIL>",
 *                          "otherMails": [],
 *                          "streetAddress": null,
 *                          "country": null,
 *                          "state": null,
 *                          "city": null,
 *                          "postalCode": null,
 *                          "mobilePhone": null,
 *                          "accountEnabled": true,
 *                          "userPrincipalName": "<EMAIL>",
 *                          "id": "b8e02f0c-5c98-480e-981b-9d295a8f86e4",
 *                          "externalUserState": null,
 *                          "creationType": null
 *                        },
 *                        {
 *                          "displayName": "Aanchal Paregi",
 *                          "givenName": "Aanchal",
 *                          "surname": "Paregi",
 *                          "mail": "<EMAIL>",
 *                          "otherMails": [],
 *                          "streetAddress": null,
 *                          "country": null,
 *                          "state": null,
 *                          "city": null,
 *                          "postalCode": null,
 *                          "mobilePhone": null,
 *                          "accountEnabled": true,
 *                          "userPrincipalName": "<EMAIL>",
 *                          "id": "5ad5c2c1-ff94-42e2-bee9-d758b24bb6f9",
 *                          "externalUserState": null,
 *                          "creationType": null
 *                        },
 *                      ],
 *                      "skipToken": ""
 *                    }
 *               Second:
 *                 summary: Fetched users with query param name="darshan vesatiya"
 *                 description: Fetched users with query param name="darshan vesatiya"
 *                 value:
 *                   {
 *                     "totalRows": 1,
 *                     "data": [
 *                        {
 *                          "displayName": "Darshan vesatiya",
 *                          "givenName": "Darshan",
 *                         "surname": "Vesatiya",
 *                          "mail": "<EMAIL>",
 *                          "otherMails": [],
 *                          "streetAddress": null,
 *                          "country": null,
 *                          "state": null,
 *                          "city": null,
 *                          "postalCode": null,
 *                          "mobilePhone": null,
 *                          "accountEnabled": true,
 *                          "userPrincipalName": "<EMAIL>",
 *                          "id": "d4a4fd99-1225-455f-83d0-0bfd59e47f98",
 *                          "externalUserState": null,
 *                          "creationType": null
 *                        },
 *                      ],
 *                      "skipToken": ""
 *                    }
 *               Third:
 *                 summary: Fetched users with query param email="<EMAIL>"
 *                 description: Fetched users with query param name="<EMAIL>"
 *                 value:
 *                   {
 *                     "totalRows": 1,
 *                     "data": [
 *                        {
 *                          "displayName": "Darshan vesatiya",
 *                          "givenName": "Darshan",
 *                         "surname": "Vesatiya",
 *                          "mail": "<EMAIL>",
 *                          "otherMails": [],
 *                          "streetAddress": null,
 *                          "country": null,
 *                          "state": null,
 *                          "city": null,
 *                          "postalCode": null,
 *                          "mobilePhone": null,
 *                          "accountEnabled": true,
 *                          "userPrincipalName": "<EMAIL>",
 *                          "id": "d4a4fd99-1225-455f-83d0-0bfd59e47f98",
 *                          "externalUserState": null,
 *                          "creationType": null
 *                        },
 *                      ],
 *                      "skipToken": ""
 *                    }
 *               Fifth:
 *                 summary: Fetched users with query param top=2 and skipToken="m~AQDHATs7MzAwMDQ1MDAzMzAwMzUwMDMwMDA0NTAwMzcwMDQzMD"
 *                 description: Fetched users with query param top=2 and skipToken="m~AQDHATs7MzAwMDQ1MDAzMzAwMzUwMDMwMDA0NTAwMzcwMDQzMD"
 *                 value:
 *                   {
 *                     "data": [
 *                        {
 *                          "displayName": "Aaron Petrosky",
 *                          "givenName": "Aaron",
 *                         "surname": "Petrosky",
 *                          "mail": "<EMAIL>",
 *                          "otherMails": [],
 *                          "streetAddress": null,
 *                          "country": null,
 *                          "state": null,
 *                          "city": null,
 *                          "postalCode": null,
 *                          "mobilePhone": null,
 *                          "accountEnabled": true,
 *                          "userPrincipalName": "<EMAIL>",
 *                          "id": "7b5ad400-140a-4dd9-8abf-284a762ec5c9",
 *                          "externalUserState": null,
 *                          "creationType": null
 *                        },
 *                        {
 *                          "displayName": "Aatur Shah",
 *                          "givenName": "Aatur",
 *                          "surname": "Shah",
 *                          "mail": "<EMAIL>",
 *                          "otherMails": [],
 *                          "streetAddress": null,
 *                          "country": null,
 *                          "state": null,
 *                          "city": null,
 *                          "postalCode": null,
 *                          "mobilePhone": null,
 *                          "accountEnabled": true,
 *                          "userPrincipalName": "<EMAIL>",
 *                          "id": "d0b85999-78df-4c64-8605-6a911ef18754",
 *                          "externalUserState": null,
 *                          "creationType": null
 *                        },
 *                      ],
 *                      "skipToken": "m~AQDHATs7MzAwMDQ1MDAzMzAwMzUwMDMwMDA0NTAwMzcwMDQzMDAzMDAwNDUwMDM3MDAzMDAwMzAwMDQ1MDAzOTAwMzkwMDMwMDA0NTAwNDEwMDM3MDAzMDAwMzcwMDMwMDAzMjAwMzAwMDM3MDAzMDAwMzIwMDMwMDA0NTAwMzcwMDQ1MDAzMDAwNDUwMDMwMDAzMjAwMzAwMDQ1MDAzOTAwMzkwMDMwMDA0NTAwMzIwMDMxMDAzMDAwNDUwMDM0MDAzODAwOzE7MDs"
 *                    }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "top must be a number between 0 and 1000!", "field": "top" } ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Something went wrong" } ] }
 */
