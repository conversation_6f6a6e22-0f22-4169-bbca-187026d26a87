import { parse } from "url";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    requireAuth,
    responseHand<PERSON>,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";

import { GetUsersListParams, GetUsersListResponse } from "../../interfaces";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { getADusersValidation } from "./get-ad-users.validation";

const router = express.Router();

router.get(
    "/v1/users/ad",
    responseHandler,
    currentUser,
    requireAuth,
    getADusersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to get all AD users
            const hasPermission = await hasGlobalAction(req, "GetUsersOfActiveDirectory");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { search, top, skipToken } = req.query as GetUsersListParams;

            const accessToken = await microsoftGraphAPI.getAccessToken();

            let usersList: GetUsersListResponse;
            if (search) {
                usersList = await microsoftGraphAPI.searchUsers({ accessToken, search: search, skipToken, top });
            }
            else {
                usersList = await microsoftGraphAPI.getUsers({ accessToken, top, skipToken });
            }

            const respObj = {
                totalRows: usersList.data["@odata.count"],
                data: usersList.data.value,
                skipToken: ""
            };
            if (usersList.data["@odata.nextLink"]) {
                respObj["skipToken"] = parse(usersList.data["@odata.nextLink"], true).query["$skiptoken"] as string;
            }

            res.sendResponse(respObj, {});
        }
        catch (error) {
            console.error("Authentication.GetADUsers");
            console.error(error);
            next(error);
        }
    }
);

export { router as getADUsersRouter };
