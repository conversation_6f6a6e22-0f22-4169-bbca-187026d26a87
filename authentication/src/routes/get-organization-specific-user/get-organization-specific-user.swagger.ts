/**
 * @swagger
 * /v1/organizations/{organizationId}/users/{userId}:
 *   get:
 *     summary: Get User Profile of specific organization
 *     description: This is used to get the user profile for any specific organization
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *       - in: path
 *         name: userId
 *         required: true
 *         description: user id
 *         schema:
 *           type: string
 *     responses:
 *       200: # response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               {
 *               "email": "<EMAIL>",
 *               "isEnabled": true,
 *               "firstName": Romit,
 *               "lastName": Gandhi,
 *               "streetAddress": null,
 *               "country": null,
 *               "state": null,
 *               "city": null,
 *               "postalCode": null,
 *               "officePhone": null,
 *               "alternateEmail": null,
 *               "jobTitle": null,
 *               "companyName": null,
 *               "roleId": "62397eb4faf84ac1d0f5fe4d",
 *               "lastSignIn": '2022-03-25T10:55:22.483Z',
 *               "createdAt": "2022-03-22T07:35:23.063Z",
 *               "updatedAt": "2022-03-22T07:48:15.889Z",
 *               "isOwner": true,
 *               "id": "62397c3b020359bf2f08426a"
 *               }
 *       401:
 *         description: Not Authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             examples:
 *                First:
 *                  summary: Organization not found
 *                  description: Organization not found
 *                  value:
 *                    { "errors": [ { "message": "Organization not found"} ] }
 *                Second:
 *                  summary: User not found
 *                  description: User not found
 *                  value:
 *                    { "errors": [ { "message": "User not found"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "User doesn't exist in the provided organization"} ] }
 */
