/**
 * @swagger
 * /v1/organizations:
 *   post:
 *     name: Create Organization.
 *     summary: Create Organization.
 *     description: this will Create Organization.
 *     tags:
 *       - Organizations
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *           examples:
 *             first:
 *               summary: Client Organization.
 *               value:
 *                {
 *                    "organizationTypes": ["6222096db401b9512139172a"],
 *                    "description": "Validation Client 1",
 *                    "name": "Demo",
 *                    "website": "abc.com",
 *                    "phone": "+19876543210",
 *                    "msaSignatureDate": "2022-03-07T13:17:24Z",
 *                    "billingAddresses": [{
 *                        "addressline1": "1",
 *                        "addressline2": "1",
 *                        "state": "1",
 *                        "city": "1",
 *                        "zip": "01296",
 *                        "country": "1",
 *                        "contact": "+19876543210"
 *                    }],
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        }
 *                    }
 *                }
 *             second:
 *               summary: Forensics Organization.
 *               value:
 *                {
 *                    "organizationTypes": ["6225df3b52eb2f857a20b3d1"],
 *                    "activePartner": true,
 *                    "description": "Forensics description",
 *                    "name": "Forensics Inc.",
 *                    "website": "forensics.com",
 *                    "phone": "+19876543210",
 *                    "executiveSponsor": "executiveSponsor",
 *                    "technicalSponsor": "technicalSponsor",
 *                    "salesSponsor": "salesSponsor",
 *                    "msaSignatureDate": "2022-03-07T13:17:24Z",
 *                    "onboardedDate": "2022-03-07T13:17:24Z",
 *                    "partnerEula": "partnerEula.com",
 *                    "partnerTermsConditions": "partnerTermsConditions.com",
 *                    "inboundRequestInfo": "inboundRequestInfo",
 *                    "pmSponsor": "pmSponsor",
 *                    "taSponsor": "taSponsor",
 *                    "parnterSalesSponsor": "parnterSalesSponsor",
 *                    "numberOfPMs": 1,
 *                    "numberOfLeads": 1,
 *                    "numberOfEngineers": 7,
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        },
 *                        "partnerStatus": {
 *                            "id": "6245a5b0bf392b108efb6662",
 *                            "valueIds": ["6245a87bf41c4d94454e3b45"]
 *                        },
 *                        "coverageStates": {
 *                            "id": "6245a8e2bf392b108efb6751",
 *                            "valueIds": ["6245a914f41c4d94454e3b5d", "6245a914f41c4d94454e3b5d", "6245aadf3685b986e787fd1d", "6245ab6e043534be1086dff4"]
 *                        },
 *                        "offerings": {
 *                            "id": "6245ababbf392b108efb683d",
 *                            "valueIds": ["507f1f77bcf86cd799439011"]
 *                        }
 *                    }
 *                }
 *             third:
 *               summary: Recovery Organization.
 *               value:
 *                {
 *                    "organizationTypes": ["6225df5052eb2f857a20b3d2"],
 *                    "activePartner": true,
 *                    "description": "Recovery description",
 *                    "name": "Recovery Inc.",
 *                    "website": "recovery.com",
 *                    "phone": "+19876543210",
 *                    "executiveSponsor": "executiveSponsor",
 *                    "technicalSponsor": "technicalSponsor",
 *                    "salesSponsor": "salesSponsor",
 *                    "msaSignatureDate": "2022-03-07T13:17:24Z",
 *                    "onboardedDate": "2022-03-07T13:17:24Z",
 *                    "partnerEula": "partnerEula.com",
 *                    "partnerTermsConditions": "partnerTermsConditions.com",
 *                    "inboundRequestInfo": "inboundRequestInfo",
 *                    "pmSponsor": "pmSponsor",
 *                    "taSponsor": "taSponsor",
 *                    "parnterSalesSponsor": "parnterSalesSponsor",
 *                    "numberOfPMs": 1,
 *                    "numberOfLeads": 1,
 *                    "numberOfEngineers": 7,
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        },
 *                        "partnerStatus": {
 *                            "id": "6245a5b0bf392b108efb6662",
 *                            "valueIds": ["6245a87bf41c4d94454e3b45"]
 *                        },
 *                        "coverageStates": {
 *                            "id": "6245a8e2bf392b108efb6751",
 *                            "valueIds": ["6245a914f41c4d94454e3b5d", "6245a914f41c4d94454e3b5d", "6245aadf3685b986e787fd1d", "6245ab6e043534be1086dff4"]
 *                        },
 *                        "offerings": {
 *                            "id": "6245ababbf392b108efb683d",
 *                            "valueIds": ["507f1f77bcf86cd799439011"]
 *                        }
 *                    }
 *                }
 *             fourth:
 *               summary: Negotiator Create Organization.
 *               value:
 *                {
 *                    "organizationTypes": ["6225df6952eb2f857a20b3d3"],
 *                    "activePartner": true,
 *                    "description": "Negotiator description",
 *                    "name": "Negotiator Inc.",
 *                    "website": "negotiator.com",
 *                    "phone": "+19876543210",
 *                    "executiveSponsor": "executiveSponsor",
 *                    "technicalSponsor": "technicalSponsor",
 *                    "salesSponsor": "salesSponsor",
 *                    "msaSignatureDate": "2022-03-07T13:17:24Z",
 *                    "onboardedDate": "2022-03-07T13:17:24Z",
 *                    "partnerEula": "partnerEula.com",
 *                    "partnerTermsConditions": "partnerTermsConditions.com",
 *                    "inboundRequestInfo": "inboundRequestInfo",
 *                    "pmSponsor": "pmSponsor",
 *                    "taSponsor": "taSponsor",
 *                    "parnterSalesSponsor": "parnterSalesSponsor",
 *                    "numberOfPMs": 1,
 *                    "numberOfLeads": 1,
 *                    "numberOfEngineers": 7,
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        },
 *                        "partnerStatus": {
 *                            "id": "6245a5b0bf392b108efb6662",
 *                            "valueIds": ["6245a87bf41c4d94454e3b45"]
 *                        },
 *                        "coverageStates": {
 *                            "id": "6245a8e2bf392b108efb6751",
 *                            "valueIds": ["6245a914f41c4d94454e3b5d", "6245a914f41c4d94454e3b5d", "6245aadf3685b986e787fd1d", "6245ab6e043534be1086dff4"]
 *                        },
 *                        "offerings": {
 *                            "id": "6245ababbf392b108efb683d",
 *                            "valueIds": ["507f1f77bcf86cd799439011"]
 *                        }
 *                    }
 *                }
 *             fifth:
 *               summary: Privacy Councel.
 *               value:
 *                {
 *                    "organizationTypes": ["62245c062fe4a628f50da609"],
 *                    "name": "PrivacyCounsel Inc.",
 *                    "description": "Privacy Counsel description",
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        }
 *                    },
 *                    "website": "privacycounsel.com",
 *                    "phone": "+19876543210"
 *                }
 *             sixth:
 *               summary: Insurance Carrier organization.
 *               value:
 *                {
 *                    "organizationTypes": ["62245e402fe4a628f50da616"],
 *                    "name": "Insurance Carrier Inc.",
 *                    "description": "Insurance Carrier description",
 *                    "website": "insurancecarrier.com",
 *                    "phone": "+19876543210",
 *                    "moxfiveHotline": true,
 *                    "hotlineEmail": "<EMAIL>",
 *                    "hotlinePhoneNumber": "+17418529630",
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        }
 *                    }
 *                }
 *             seventh:
 *               summary: Monitoring Counsel.
 *               value:
 *                {
 *                    "organizationTypes": ["622f1b81a3fa22fb674065b8"],
 *                    "name": "Monitoring Counsel Inc.",
 *                    "description": "Monitoring Counsel description",
 *                    "website": "monitoringcounsel.com",
 *                    "phone": "+19876543210",
 *                    "moxfiveHotline": true,
 *                    "hotlineEmail": "<EMAIL>",
 *                    "hotlinePhoneNumber": "+17318529630",
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        }
 *                    }
 *                }
 *             eight:
 *               summary: Moxfive.
 *               value:
 *                {
 *                    "organizationTypes": ["6222096db401b9512139172a"],
 *                    "description": "Mixed description",
 *                    "name": "OT Ids3 Inc.",
 *                    "website": "www.Mixed.com",
 *                    "phone": "+19876543210",
 *                    "executiveSponsor": "executiveSponsor",
 *                    "technicalSponsor": "technicalSponsor",
 *                    "salesSponsor": "salesSponsor",
 *                    "msaSignatureDate": "2022-03-07T13:17:24Z",
 *                    "onboardedDate": "2022-03-07T13:17:24Z",
 *                    "partnerEula": "partnerEula.com",
 *                    "partnerTermsConditions": "partnerTermsConditions.com",
 *                    "inboundRequestInfo": "inboundRequestInfo",
 *                    "pmSponsor": "pmSponsor",
 *                    "taSponsor": "taSponsor",
 *                    "parnterSalesSponsor": "parnterSalesSponsor",
 *                    "numberOfPMs": 1,
 *                    "numberOfLeads": 1,
 *                    "numberOfEngineers": 7,
 *                    "moxfiveHotline": true,
 *                    "hotlineEmail": "<EMAIL>",
 *                    "hotlinePhoneNumber": "+17418529630",
 *                    "billingAddresses": [{
 *                        "addressline1": "1",
 *                        "addressline2": "1",
 *                        "state": "1",
 *                        "city": "1",
 *                        "zip": "01296",
 *                        "country": "1",
 *                        "contact": "+19876543210"
 *                    }],
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        }
 *                    }
 *                }
 *             nine:
 *               summary: Multiple Organization Type.
 *               value:
 *                {
 *                    "organizationTypes": ["6222096db401b9512139172a", "6225df6952eb2f857a20b3d3"],
 *                    "description": "Mixed description",
 *                    "name": "OT Ids3 Inc.",
 *                    "website": "www.Mixed.com",
 *                    "phone": "+19876543210",
 *                    "executiveSponsor": "executiveSponsor",
 *                    "technicalSponsor": "technicalSponsor",
 *                    "salesSponsor": "salesSponsor",
 *                    "msaSignatureDate": "2022-03-07T13:17:24Z",
 *                    "onboardedDate": "2022-03-07T13:17:24Z",
 *                    "partnerEula": "partnerEula.com",
 *                    "partnerTermsConditions": "partnerTermsConditions.com",
 *                    "inboundRequestInfo": "inboundRequestInfo",
 *                    "pmSponsor": "pmSponsor",
 *                    "taSponsor": "taSponsor",
 *                    "parnterSalesSponsor": "parnterSalesSponsor",
 *                    "numberOfPMs": 1,
 *                    "numberOfLeads": 1,
 *                    "numberOfEngineers": 7,
 *                    "moxfiveHotline": true,
 *                    "hotlineEmail": "<EMAIL>",
 *                    "hotlinePhoneNumber": "+17418529630",
 *                    "billingAddresses": [{
 *                        "addressline1": "1",
 *                        "addressline2": "1",
 *                        "state": "1",
 *                        "city": "1",
 *                        "zip": "01296",
 *                        "country": "1",
 *                        "contact": "+19876543210"
 *                    }],
 *                    "flexibleFields": {
 *                        "industry": {
 *                            "id": "624590e7bf392b108efb5d52",
 *                            "valueIds": ["6245a4ed9f0bb12168847b53"]
 *                        },
 *                        "partnerStatus": {
 *                            "id": "6245a5b0bf392b108efb6662",
 *                            "valueIds": ["6245a87bf41c4d94454e3b45"]
 *                        },
 *                        "coverageStates": {
 *                            "id": "6245a8e2bf392b108efb6751",
 *                            "valueIds": ["6245a914f41c4d94454e3b5d", "6245a914f41c4d94454e3b5d", "6245aadf3685b986e787fd1d", "6245ab6e043534be1086dff4"]
 *                        },
 *                        "offerings": {
 *                            "id": "6245ababbf392b108efb683d",
 *                            "valueIds": ["507f1f77bcf86cd799439011"]
 *                        }
 *                    }
 *                }
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               examples:
 *                 First:
 *                   summary: Client Organization.
 *                   description: Organization Created for client.
 *                   value:
 *                    {
 *                        "description": "Validation Client 1",
 *                        "name": "Demo",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "abc.com",
 *                        "phone": "+19876543210",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "billingAddresses": [
 *                            {
 *                                "addressline1": "1",
 *                                "addressline2": "1",
 *                                "city": "1",
 *                                "state": "1",
 *                                "zip": "01296",
 *                                "country": "1",
 *                                "contact": "+19876543210"
 *                            }
 *                        ],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:32:25.396Z",
 *                        "updatedAt": "2022-04-04T11:32:25.399Z",
 *                        "id": "624ad749c59c559217f34d62",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Client",
 *                                "id": "6222096db401b9512139172a"
 *                            }
 *                        ]
 *                    }
 *                 Second:
 *                   summary: Forensics Organization.
 *                   description: Organization Created for Forensics.
 *                   value:
 *                    {
 *                        "description": "Forensics description",
 *                        "name": "Forensics Inc.",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "forensics.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "partnerStatus": [
 *                            "6245a87bf41c4d94454e3b45"
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            "6245a914f41c4d94454e3b5d",
 *                            "6245aadf3685b986e787fd1d",
 *                            "6245ab6e043534be1086dff4"
 *                        ],
 *                        "offerings": [
 *                            "507f1f77bcf86cd799439011"
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": null,
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:28:47.312Z",
 *                        "updatedAt": "2022-04-04T11:28:47.317Z",
 *                        "id": "624ad66fc59c559217f34d5b",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Forensics",
 *                                "id": "6225df3b52eb2f857a20b3d1"
 *                            }
 *                        ]
 *                    }
 *                 Third:
 *                   summary: Recovery Organization.
 *                   description: Organization Created for Recovery.
 *                   value:
 *                    {
 *                        "description": "Recovery description",
 *                        "name": "Recovery Inc.",
 *                        "industry": [],
 *                        "website": "recovery.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "partnerStatus": [
 *                            "6245a87bf41c4d94454e3b45"
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            "6245a914f41c4d94454e3b5d",
 *                            "6245aadf3685b986e787fd1d",
 *                            "6245ab6e043534be1086dff4"
 *                        ],
 *                        "offerings": [
 *                            "507f1f77bcf86cd799439011"
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": null,
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:27:32.458Z",
 *                        "updatedAt": "2022-04-04T11:27:32.462Z",
 *                        "id": "624ad624c59c559217f34d54",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Recovery",
 *                                "id": "6225df5052eb2f857a20b3d2"
 *                            }
 *                        ]
 *                    }
 *                 Fourth:
 *                   summary: Negotiator Create Organization.
 *                   description: Negotiator Create Organization.
 *                   value:
 *                    {
 *                        "description": "Negotiator description",
 *                        "name": "Negotiator Inc.",
 *                        "industry": [],
 *                        "website": "negotiator.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "partnerStatus": [
 *                            "6245a87bf41c4d94454e3b45"
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            "6245a914f41c4d94454e3b5d",
 *                            "6245aadf3685b986e787fd1d",
 *                            "6245ab6e043534be1086dff4"
 *                        ],
 *                        "offerings": [
 *                            "507f1f77bcf86cd799439011"
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": null,
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:24:49.338Z",
 *                        "updatedAt": "2022-04-04T11:24:49.345Z",
 *                        "id": "624ad581c59c559217f34d4d",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Negotiator",
 *                                "id": "6225df6952eb2f857a20b3d3"
 *                            }
 *                        ]
 *                    }
 *                 Fifth:
 *                   summary: Privacy Councel.
 *                   description: Privacy Councel.
 *                   value:
 *                    {
 *                        "description": "Privacy Counsel description",
 *                        "name": "PrivacyCounsel Inc.",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "privacycounsel.com",
 *                        "phone": "+19876543210",
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:20:10.570Z",
 *                        "updatedAt": "2022-04-04T11:20:10.574Z",
 *                        "id": "624ad46ac59c559217f34d43",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Privacy Counsel",
 *                                "id": "62245c062fe4a628f50da609"
 *                            }
 *                        ]
 *                    }
 *                 Sixth:
 *                   summary: Insurance Carrier organization.
 *                   description: Insurance Carrier organization.
 *                   value:
 *                    {
 *                        "description": "Insurance Carrier description",
 *                        "name": "Insurance Carrier Inc.",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "insurancecarrier.com",
 *                        "phone": "+19876543210",
 *                        "moxfiveHotline": true,
 *                        "hotlineEmail": "<EMAIL>",
 *                        "hotlinePhoneNumber": "+17418529630",
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:18:31.166Z",
 *                        "updatedAt": "2022-04-04T11:18:31.172Z",
 *                        "id": "624ad407c59c559217f34d3c",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Insurance Carrier",
 *                                "id": "62245e402fe4a628f50da616"
 *                            }
 *                        ]
 *                    }
 *                 Seventh:
 *                   summary: Monitoring Counsel.
 *                   description: Monitoring Counsel.
 *                   value:
 *                    {
 *                        "description": "Monitoring Counsel description",
 *                        "name": "Monitoring Counsel Inc.",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "monitoringcounsel.com",
 *                        "phone": "+19876543210",
 *                        "moxfiveHotline": true,
 *                        "hotlineEmail": "<EMAIL>",
 *                        "hotlinePhoneNumber": "+17318529630",
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T11:16:51.759Z",
 *                        "updatedAt": "2022-04-04T11:16:51.763Z",
 *                        "id": "624ad3a3c59c559217f34d35",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Monitoring Counsel",
 *                                "id": "622f1b81a3fa22fb674065b8"
 *                            }
 *                        ]
 *                    }
 *                 eight:
 *                   summary: Moxfive.
 *                   description: Moxfive.
 *                   value:
 *                    {
 *                        "description": "Mixed description",
 *                        "name": "OT Ids3 Inc.",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "www.Mixed.com",
 *                        "phone": "+19876543210",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "billingAddresses": [
 *                            {
 *                                "addressline1": "1",
 *                                "addressline2": "1",
 *                                "city": "1",
 *                                "state": "1",
 *                                "zip": "01296",
 *                                "country": "1",
 *                                "contact": "+19876543210"
 *                            }
 *                        ],
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T10:33:20.711Z",
 *                        "updatedAt": "2022-04-04T10:33:20.723Z",
 *                        "id": "624ac970c59c559217f34d27",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Client",
 *                                "id": "6222096db401b9512139172a"
 *                            }
 *                        ]
 *                    }
 *                 nine:
 *                   summary: Multiple Organization Type.
 *                   description: Multiple Organization Type.
 *                   value:
 *                    {
 *                        "description": "Mixed description",
 *                        "name": "OT Ids3 Inc.",
 *                        "industry": [
 *                            "6245a4ed9f0bb12168847b53"
 *                        ],
 *                        "website": "www.Mixed.com",
 *                        "phone": "+19876543210",
 *                        "executiveSponsor": "executiveSponsor",
 *                        "technicalSponsor": "technicalSponsor",
 *                        "salesSponsor": "salesSponsor",
 *                        "msaSignatureDate": "2022-03-07T13:17:24.000Z",
 *                        "billingAddresses": [
 *                            {
 *                                "addressline1": "1",
 *                                "addressline2": "1",
 *                                "city": "1",
 *                                "state": "1",
 *                                "zip": "01296",
 *                                "country": "1",
 *                                "contact": "+19876543210"
 *                            }
 *                        ],
 *                        "partnerStatus": [
 *                            "6245a87bf41c4d94454e3b45"
 *                        ],
 *                        "onboardedDate": "2022-03-07T13:17:24.000Z",
 *                        "coverageStates": [
 *                            "6245a914f41c4d94454e3b5d",
 *                            "6245aadf3685b986e787fd1d",
 *                            "6245ab6e043534be1086dff4"
 *                        ],
 *                        "offerings": [
 *                            "507f1f77bcf86cd799439011"
 *                        ],
 *                        "partnerEula": "partnerEula.com",
 *                        "partnerTermsConditions": "partnerTermsConditions.com",
 *                        "inboundRequestInfo": "inboundRequestInfo",
 *                        "pmSponsor": "pmSponsor",
 *                        "taSponsor": "taSponsor",
 *                        "parnterSalesSponsor": "parnterSalesSponsor",
 *                        "numberOfPMs": 1,
 *                        "numberOfLeads": 1,
 *                        "numberOfEngineers": 7,
 *                        "languages": null,
 *                        "isEnabled": true,
 *                        "createdAt": "2022-04-04T10:43:53.837Z",
 *                        "updatedAt": "2022-04-04T10:43:53.845Z",
 *                        "id": "624acbe9c59c559217f34d2e",
 *                        "owners": 0,
 *                        "members": 0,
 *                        "organizationTypes": [
 *                            {
 *                                "name": "Client",
 *                                "id": "6222096db401b9512139172a"
 *                            },
 *                            {
 *                                "name": "Negotiator",
 *                                "id": "6225df6952eb2f857a20b3d3"
 *                            }
 *                        ]
 *                    }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Active partner is required",
 *                          "field": "activePartner"
 *                      }
 *                  ]
 *              }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Something went wrong"
 *                      }
 *                  ]
 *              }
 */
