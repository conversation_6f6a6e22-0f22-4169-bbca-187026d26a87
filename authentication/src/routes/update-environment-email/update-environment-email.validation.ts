import { Valida<PERSON><PERSON>hai<PERSON> } from "express-validator";
import { stringValidator } from "../../util/express-validator-wrapper";
import { flexibleFieldReqBodyValidation } from "../../util/incident-flexible-field-req-body-validation";
import { authenticationFields } from "../../util/authentication-fields";
import { param } from "express-validator";

export const UpdateEnvironmentEmailDetailValidation = () => {
    const validations: ValidationChain[] = [
        param("organizationId")
            .isMongoId(),
    ];

    stringValidator([
        {
            name: "emailNotes",
            maxLength: 2000,
            nullable: true,
            message: "Email notes must be string and of max 2000 characters long."
        }
    ], validations);

    flexibleFieldReqBodyValidation(
        authenticationFields.environment.email.filter(f => f.flexibleField).map(f => {
            return {
                name: f.name,
                minValuesLength: 0,
                nullable: true
            };
        }),
        validations
    );

    return validations;

};
