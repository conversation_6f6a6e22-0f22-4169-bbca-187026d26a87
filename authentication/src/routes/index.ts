/* eslint-disable max-len */
import express from "express";
import cors from "cors";
import { loginRouter } from "./login/login";
import { redirectRouter } from "./redirect/redirect";
// import { inviteUserRouter } from "./invite-user/invite-user";
// import { refreshTokenRouter } from "./refresh-token/refresh-token";
import { logOutRouter } from "./log-out/log-out";
// import { verifyCodeRouter } from "./verify-code/verify-code";
import { getOrganizationTypesRouter } from "./get-organization-types/get-organization-types";
// import { createOrganizationRouter } from "./create-organization/create-organization";
// import { addOrganizationMembersRouter } from "./add-organization-members/add-organization-members";
// import { addOrganizationOwnersRouter } from "./add-organization-owners/add-organization-owners";
import { updateOrganizationStatusRouter } from "./update-organization-status/update-organization-status";
// import { removeOrganizationMembersRouter } from "./remove-organization-members/remove-organization-members";
// import { removeOrganizationOwnersRouter } from "./remove-organization-owners/remove-organization-owners";
// import { getAllOrganizationsRouter } from "./get-all-organizations/get-all-organizations";
// import { getSpecificOrganizationRouter } from "./get-specific-organization/get-specific-organization";
// import { updateOrganizationRouter } from "./update-organization/update-organization";
// import { updateOrganizationUsersStatusRouter } from "./update-organization-users-status/update-organization-users-status";
// import { getUserProfileRouter } from "./get-user-profile/get-user-profile";
// import { getOrganizationUsersRouter } from "./get-organization-users/get-organization-users";
// import { updateUserEmailRouter } from "./update-user-email/update-user-email";
import { getOrganizationSpecificUserRouter } from "./get-organization-specific-user/get-organization-specific-user";
// import { updateUserDetailsRouter } from "./update-user-details/update-user-details";
// import { updateOrganizationSpecificUserRouter } from "./update-organization-specific-user/update-organization-specific-user";
// import { updateOrganizationSpecificUserEmailRouter } from "./update-organization-specific-user-email/update-organization-specific-user-email";
import { getOrganizationFlexibleFieldsRouter } from "./get-organization-flexible-fields/get-organization-flexible-fields";
import { addOrganizationFlexibleFieldRouter } from "./add-organization-flexible-fields/add-organization-flexible-fields";
// import { addOrganizationMembersByEmailsRouter } from "./add-organization-members-by-email/add-organization-members-by-emails";
import { addOrganizationOwnersByEmailsRouter } from "./add-organization-owner-by-emails/add-organization-owners-by-emails";
// import { getAllUsersRouter } from "./get-all-users/get-all-users";
import { getLocationsRouter } from "./get-locations/get-locations";
import { getUserPermissionsRouter } from "./get-user-permissions/get-user-permissions";
import { validateSessionRouter } from "./validate-session/validate-session";
// import { updateUsersStatusRouter } from "./update-users-status/update-users-status";
import { getUserFlexibleFields } from "./get-user-flexible-fields/get-user-flexible-fields";
// import { changeOrganizationMembershipRouter } from "./change-organization-membership/change-organization-membership";
import { addUserFlexibleFieldRouter } from "./add-user-flexible-fields/add-user-flexible-fields";
// import { getMembersOfRouter } from "./get-members-of/get-members-of";
import { getUserDetailRouter } from "./get-user-detail/get-user-detail";
// import { userUpdateCallbackRouter } from "./user-update-callback/user-update-callback";
// import { organizationUpdateCallbackRouter } from "./organization-update-callback/organization-update-callback";
import { getOrganizationsByTypesRouter } from "./V2/get-organizations-by-types/get-organizations-by-types";
import { fetchOrganizationUsersFiltersRouter } from "./fetch-organization-users-filters/fetch-organization-users-filters";
import { fetchOrganizationsFiltersRouter } from "./fetch-organization-filters/fetch-organization-filters";
import { fetchUsersFiltersRouter } from "./fetch-users-filters/fetch-users-filters";
import { getEventLogStatisticsRouter } from "./get-event-log-statistics/get-event-log-statistics";
import { UpdateEnvironmentGeneralDetailRouter } from "./update-environment-general/update-environment-general-details";
import { UpdateEnvironmentActiveDirectoryDetailRouter } from "./update-environment-active-directory/update-environment-active-directory";
import { UpdateEnvironmentBackupRouter } from "./update-enviroment-backup/update-environment-backup";
import { UpdateEnvironmentEmailRouter } from "./update-environment-email/update-environment-email";
import { UpdateEnvironmentSolutionsDetailRouter } from "./update-enviroment-solutions/update-enviroment-solutions";
import { exportOrganizationEnvironments } from "./export-organization-environments/export-organization-environment";
import { getEnvironmentGeneralDetailsRouter } from "./get-environment-general-details/get-environment-general-details";
import { getEnvironmentActiveDirectoryRouter } from "./get-environment-active-directory/get-environment-active-directory";
import { getEnvironmentBackupRouter } from "./get-environment-backup/get-environment-backup";
import { getEnvironmentEmailRouter } from "./get-environment-email/get-environment-email";
import { getEnvironmentSolutionsRouter } from "./get-environment-solutions/get-environment-solutions";
import { uplaodLogoAndFavicon } from "./upload-logo-and-favicon/upload-logo-and-favicon";
import { removeLogoOrFavicon } from "./delete-logo-and-favicon/delete-logo-and-favicon";
import {
    getSpecificFlexibleFieldsForOrganizationRouter
} from "./get-specific-flexible-fields-for-organization/get-specific-flexible-fields-for-organization";
import { getOrganizationUsersRouterV2 } from "./V2/get-organization-users/get-organization-users";
// import { getOrganizationProfileFaviconRouter } from "./get-organization-profile-favicon/get-organization-profile-favicon";
import { accessManagementSummaryV2Router } from "./V2/access-management-summary/access-management-summary";
import { getADUsersV2Router } from "./V2/get-ad-users/get-ad-users";
// import { getMembersOfV2Router } from "./V2/get-members-of/get-members-of";
import { getUserPermissionsV2Router } from "./V2/get-user-permissions/get-user-permissions";
import { getUserDetailV2Router } from "./V2/get-user-detail/get-user-detail";
import { getOrganizationSpecificUserV2Router } from "./V2/get-organization-specific-user/get-organization-specific-user";
import { getUserProfileV2Router } from "./V2/get-user-profile/get-user-profile";
// import { updateUserDetailsV2Router } from "./V2/update-user-details/update-user-details";
// import { updateOrganizationSpecificUserV2Router } from "./V2/update-organization-specific-user/update-organization-specific-user";
// import { updateUsersStatusV2Router } from "./V2/update-users-status/update-users-status";
// import { updateOrganizationUsersStatusV2Router } from "./V2/update-organization-users-status/update-organization-users-status";
import { updateOrganizationStatusV2Router } from "./V2/update-organization-status/update-organization-status";
import { getSpecificOrganizationV2Router } from "./V2/get-specific-organization/get-specific-organization";
import { uplaodLogoAndFaviconV2Router } from "./V2/upload-logo-and-favicon/upload-logo-and-favicon";
import { getOrganizationProfileFaviconV2Router } from "./V2/get-organization-profile-favicon/get-organization-profile-favicon";
// import { changeOrganizationMembershipV2Router } from "./V2/change-organization-membership/change-organization-membership";
// import { addOrganizationMembersV2Router } from "./V2/add-organization-members/add-organization-members";
// import { addOrganizationMembersByEmailsV2Router } from "./V2/add-organization-members-by-email/add-organization-members-by-emails";
// import { removeOrganizationMembersV2Router } from "./V2/remove-organization-members/remove-organization-members";
// import { removeOrganizationOwnersV2Router } from "./V2/remove-organization-owners/remove-organization-owners";
// import { createOrganizationV2Router } from "./V2/create-organization/create-organization";
import { getAllUsersV2Router } from "./V2/get-all-users/get-all-users";
import { getAllOrganizationsV2Router } from "./V2/get-all-organizations/get-all-organizations";
import { accessManagementSummaryRouter } from "./access-management-summary/access-management-summary";
import { getADUsersRouter } from "./get-ad-users/get-ad-users";
// import { userUpdateCallbackV2Router } from "./V2/user-update-callback/user-update-callback";
// import { organizationUpdateCallbackV2Router } from "./V2/organization-update-callback/organization-update-callback";
// import { updateOrganizationV2Router } from "./V2/update-organization/update-organization";
import { loginV3Router } from "./v3/authentication/login/login";
import { associateMFAEnrollmentV3Router } from "./v3/authentication/associate-mfa-enrollment/associate-mfa-enrollment";
import { verifyMFAV3Router } from "./v3/authentication/verify-mfa/verify-mfa";
import { refreshTokenV3Router } from "./v3/authentication/refresh-token/refresh-token";
import { listUserLoggedinDevicesV3Router } from "./v3/users/list-user-logged-in-devices/list-user-logged-in-devices";
import { requestForgotPaswordV3Router } from "./v3/authentication/request-forgot-password/request-forgot-password";
import { resetPasswordV3Router } from "./v3/authentication/reset-password/reset-password";
import { verifyForgotPasswordRequestV3Router } from "./v3/authentication/verify-forgot-password-request/verify-forgot-password-request";
import { verifyUserEmailV3Router } from "./v3/authentication/verify-user-email/verify-user-email";
import { setUserPasswordV3Router } from "./v3/authentication/set-user-password/set-user-password";
// import { getConnectionFieldsRouter } from "./v1/meta-data/get-meta-data-connection-fields";
import { validateUserV3Router } from "./v3/authentication/validate-user/validate-user";
import { createVerifyUserEmailRequestV3Router } from "./v3/authentication/create-verify-user-email-request/create-verify-user-email-request";
import { listUserLoggedinDevicesOfOrganizationV3Router } from "./v3/organization/list-organization-specific-user-logged-indevices/list-organization-specific-user-logged-indevices";
import { logoutUserFromLoggedinDevicesV3Router } from "./v3/users/log-out-user-from-logged-in-devices/log-out-user-from-logged-in-devices";
import { logoutUserFromLogedinDevicesOfOrganizationV3Router } from "./v3/organization/log-out-user-from-logged-in-devices-of-organization/log-out-user-from-logged-in-devices-of-organization";
import { listUserMFAEnrollmentsV3Router } from "./v3/users/list-user-mfa-enrollments/list-user-mfa-enrollments";
import { listUserMFAEnrollmentsOfOrganizationV3Router } from "./v3/organization/list-user-mfa-enrollments-of-organization/list-user-mfa-enrollments-of-organization";
import { resetUserMFAEnrollmentsV3Router } from "./v3/users/reset-user-mfa-enrollments/reset-user-mfa-enrollments";
import { resetUserMFAEnrollmentsOfOrganizationV3Router } from "./v3/organization/reset-user-mfa-enrollments-of-organization/reset-user-mfa-enrollments-of-organization";
// import { uploadConnectionEPHTemporaryFilesRouter } from "./v1/files/upload-connection-temporary-file";
// import { removeUserDevicesV3Router } from "./v3/remove-user-devices/remove-user-devices";
// import { listUserEnrollmentsV3Router } from "./v3/list-user-mfa-enrollments/list-user-mfa-enrollments";
// import { removeUserMFAEnrollmentV3Router } from "./v3/remove-user-mfa-enrollment/remove-user-mfa-enrollment";
import { updateUserPasswordV3Router } from "./v3/users/update-user-password/update-user-password";
import { addOrganizationMembersV3Router } from "./v3/organization/add-organization-member/add-organization-member";
import { getUserActionTokenV3Router } from "./v3/users/get-action-token/get-action-token";
import { unlockOrganizationSpecificUserV3Router } from "./v3/organization/unlock-organization-specific-user/unlock-organization-specific-user";
// import { verifyLoginCredentialsRouter } from "./v3/verify-login-credentials/verify-login-credentials";
// import { createUserMFAEnrollmentRouter } from "./v3/create-user-mfa-enrollment/create-user-mfa-enrollment";
// import { verifyMFARouter } from "./v3/verify-mfa/verify-mfa";
// import { listUserDevicesV3Router } from "./v3/list-user-devices/list-user-devices";
// import { createForgotPasswordRequestV3Router } from "./v3/create-forgot-password-request/create-forgot-password-request";
// import { validateForgotPasswordRequestV3Router } from "./v3/validate-forgot-password-request/validate-forgot-password-request";
// import { validateEmailVerificationRequestV3Router } from "./v3/validate-email-verification-request/validate-email-verification-request";
// import { verifyEmailVerificationRequestV3Router } from "./v3/verify-email-verification-request/verify-email-verification-request";
import { getConnectionFieldsRouter } from "./v3/organization/meta-data/get-meta-data-connection-fields/get-meta-data-connection-fields";
import { createConnectionRouter } from "./v3/organization/connections/create-connection/create-connection";
import { uploadConnectionEPHTemporaryFilesRouter } from "./v3/organization/files/upload-connection-temporary-file/upload-connection-temporary-file";
import { updateConnectionRouter } from "./v3/organization/connections/update-connection/update-connection";
import { listConnectionsRouter } from "./v3/organization/connections/list-connections/list-connection";
import { downloadConnectionFileRouter } from "./v3/organization/files/download-connection-file/download-connection-file";
import { getConnectionDetailsRouter } from "./v3/organization/connections/get-connection-details/get-connection-details";
import { logOutV3Router } from "./v3/authentication/log-out/log-out";
import { updateUserEmailV3Router } from "./v3/users/update-user-email/update-user-email";
import { updateUserEmailOfOrganizationV3Router } from "./v3/organization/update-user-email-of-organization/update-user-email-of-organization";
import { getMetaDataConnectionTypesV3Router } from "./v3/organization/meta-data/get-meta-data-connection-types/get-meta-data-connection-types";
import { listAuthenticationLogsRouter } from "./v3/authentication/list-authentication-logs/list-authentication-logs";
import { fetchAuthenticationLogsFiltersRouter } from "./v3/authentication/fetch-authentication-logs-filters/fetch-authentication-logs-filters";
import { createOrganizationV3Router } from "./v3/organization/create-organization/create-organization";
import { removeOrganizationUserV3Router } from "./v3/organization/remove-organization-user/remove-organization-user";
import { updateOrganizationV3Router } from "./v3/organization/update-organization/update-organization";
import { updateOrganizationSpecificUserV3Router } from "./v3/organization/update-organization-specific-user/update-organization-specific-user";
import { updateOrganizationUsersStatusV3Router } from "./v3/organization/update-organization-users-status/update-organization-users-status";
import { updateUserDetailsV3Router } from "./v3/users/update-user-details/update-user-details";
import { updateUsersStatusV3Router } from "./v3/users/update-user-status/update-users-status";
import { removeConnectionRouter } from "./v3/organization/connections/remove-connection/remove-connection";
import { auth0LogsWebhookRouter } from "./v3/webhook/auth0-logs";
import { verifyCodeV3Router } from "./v3/authentication/verify-code/verify-code";
import { redirectV3Router } from "./v3/authentication/redirect/redirect";
import { validateReCaptchaV3Router } from "./v3/recaptcha/latest-recaptcha-verify/latest-recaptcha-verify";
import { validateReCaptchaV2Router } from "./v3/recaptcha/traditional-recaptcha-verify/traditional-recaptcha-verify";
import { verifySetUserPasswordRequestV3Router } from "./v3/authentication/verify-set-user-password-request/verify-set-user-password-request";
import { verifyUserEmailCodeV3Router } from "./v3/authentication/verify-user-email-code/verify-user-email-code";
import { createVerifyUserEmailCodeRequestV3Router } from "./v3/authentication/create-verify-user-email-code-request/create-verify-user-email-code-request";
import { getIDPURLV3Router } from "./v3/authentication/get-idp-url/get-idp-url";

const router = express.Router();
if (process.env.LOCAL_ENVIRONMENT === "true") {
    router.use(cors({
        credentials: true,
        origin: "*"
    }));
}

router.use(loginRouter);
router.use(redirectRouter);
// router.use(inviteUserRouter);
// router.use(refreshTokenRouter);
router.use(logOutRouter);
// router.use(verifyCodeRouter);
router.use(accessManagementSummaryRouter);
router.use(getOrganizationTypesRouter);
// router.use(getAllUsersRouter);
router.use(fetchUsersFiltersRouter);
// router.use(getMembersOfRouter);
// router.use(updateUsersStatusRouter);
router.use(fetchOrganizationsFiltersRouter);
// router.use(createOrganizationRouter);
// router.use(addOrganizationMembersRouter);
// router.use(addOrganizationMembersByEmailsRouter);
// router.use(addOrganizationOwnersRouter);
router.use(addOrganizationOwnersByEmailsRouter);
router.use(addUserFlexibleFieldRouter);
router.use(updateOrganizationStatusRouter);
// router.use(removeOrganizationMembersRouter);
// router.use(removeOrganizationOwnersRouter);
// router.use(getAllOrganizationsRouter);
router.use(getOrganizationFlexibleFieldsRouter);
router.use(getSpecificFlexibleFieldsForOrganizationRouter);
// router.use(getSpecificOrganizationRouter);
router.use(getADUsersRouter);
// router.use(updateOrganizationRouter);
router.use(uplaodLogoAndFavicon);
router.use(removeLogoOrFavicon);
router.use(getEnvironmentBackupRouter);
router.use(getEnvironmentEmailRouter);
router.use(getEnvironmentSolutionsRouter);
router.use(getEnvironmentActiveDirectoryRouter);
router.use(getEnvironmentGeneralDetailsRouter);
router.use(UpdateEnvironmentBackupRouter);
router.use(UpdateEnvironmentEmailRouter);
router.use(UpdateEnvironmentSolutionsDetailRouter);
router.use(UpdateEnvironmentActiveDirectoryDetailRouter);
router.use(UpdateEnvironmentGeneralDetailRouter);
router.use(exportOrganizationEnvironments);
// router.use(updateOrganizationUsersStatusRouter);
// router.use(getUserProfileRouter);
router.use(getUserFlexibleFields);
router.use(getUserDetailRouter);
// router.use(getOrganizationUsersRouter);
router.use(fetchOrganizationUsersFiltersRouter);
// router.use(updateUserEmailRouter);
// router.use(updateUserEmailRouter);
router.use(getOrganizationSpecificUserRouter);
// router.use(updateUserDetailsRouter);
// router.use(updateOrganizationSpecificUserRouter);
// router.use(updateOrganizationSpecificUserEmailRouter);
// router.use(updateOrganizationSpecificUserEmailRouter);
router.use(addOrganizationFlexibleFieldRouter);
// router.use(getAllUsersRouter);
router.use(getLocationsRouter);
router.use(getUserPermissionsRouter);
router.use(validateSessionRouter);
// router.use(changeOrganizationMembershipRouter);
// router.use(userUpdateCallbackRouter);
// router.use(organizationUpdateCallbackRouter);

// Dashboard API
router.use(getOrganizationsByTypesRouter);

// Get Event log statistics
router.use(getEventLogStatisticsRouter);

// router.use(getOrganizationProfileFaviconRouter);
// router.use(getOrganizationProfileFaviconRouter);

//V2
router.use(accessManagementSummaryV2Router);
router.use(getAllUsersV2Router);
router.use(updateOrganizationStatusV2Router);
// router.use(removeOrganizationMembersV2Router);
// router.use(removeOrganizationOwnersV2Router);
router.use(getSpecificOrganizationV2Router);
router.use(getADUsersV2Router);
// router.use(updateOrganizationV2Router);
router.use(uplaodLogoAndFaviconV2Router);
// router.use(getMembersOfV2Router);
// router.use(updateUsersStatusV2Router);
// router.use(createOrganizationV2Router);
router.use(getAllOrganizationsV2Router);
// router.use(addOrganizationMembersV2Router);
// router.use(addOrganizationMembersByEmailsV2Router);
router.use(getUserPermissionsV2Router);
// router.use(updateOrganizationUsersStatusV2Router);
router.use(getUserProfileV2Router);
router.use(getUserDetailV2Router);
router.use(getOrganizationUsersRouterV2);
router.use(getOrganizationSpecificUserV2Router);
// router.use(updateUserDetailsV2Router);
// router.use(updateOrganizationSpecificUserV2Router);
router.use(getOrganizationProfileFaviconV2Router);
// router.use(changeOrganizationMembershipV2Router);
// router.use(userUpdateCallbackV2Router);
// router.use(organizationUpdateCallbackV2Router);
router.use(getOrganizationSpecificUserV2Router);
// router.use(updateUserDetailsV2Router);
// router.use(updateOrganizationSpecificUserV2Router);
router.use(getOrganizationProfileFaviconV2Router);
// router.use(changeOrganizationMembershipV2Router);

// V3
router.use(validateUserV3Router);
router.use(getIDPURLV3Router);
router.use(verifyUserEmailCodeV3Router);
router.use(createVerifyUserEmailCodeRequestV3Router);
router.use(loginV3Router);
router.use(verifyCodeV3Router);
router.use(redirectV3Router);
router.use(associateMFAEnrollmentV3Router);
router.use(verifyMFAV3Router);
router.use(requestForgotPaswordV3Router);
router.use(verifyForgotPasswordRequestV3Router);
router.use(resetPasswordV3Router);
router.use(refreshTokenV3Router);
router.use(createVerifyUserEmailRequestV3Router);
router.use(verifyUserEmailV3Router);
router.use(setUserPasswordV3Router);
router.use(verifySetUserPasswordRequestV3Router);
router.use(logOutV3Router);
router.use(validateReCaptchaV3Router);
router.use(validateReCaptchaV2Router);

// Organizations
// router.use(getConnectionFieldsRouter);
router.use(addOrganizationMembersV3Router);
router.use(listUserLoggedinDevicesOfOrganizationV3Router);
router.use(logoutUserFromLogedinDevicesOfOrganizationV3Router);
router.use(listUserMFAEnrollmentsOfOrganizationV3Router);
router.use(resetUserMFAEnrollmentsOfOrganizationV3Router);
router.use(updateUserEmailOfOrganizationV3Router);
// router.use(uploadConnectionEPHTemporaryFilesRouter);

router.use(unlockOrganizationSpecificUserV3Router);
// router.use(validateEmailVerificationRequestV3Router);
// router.use(verifyEmailVerificationRequestV3Router);
router.use(createOrganizationV3Router);
router.use(removeOrganizationUserV3Router);
router.use(updateOrganizationV3Router);
router.use(updateOrganizationUsersStatusV3Router);
router.use(updateOrganizationSpecificUserV3Router);

// Auth0
router.use(getConnectionFieldsRouter);
router.use(uploadConnectionEPHTemporaryFilesRouter);
router.use(downloadConnectionFileRouter);
router.use(getMetaDataConnectionTypesV3Router);

// Enterprise Connection routes for auth0
router.use(createConnectionRouter);
router.use(updateConnectionRouter);
router.use(getConnectionDetailsRouter);
router.use(listConnectionsRouter);
router.use(removeConnectionRouter);

// Users
router.use(getUserActionTokenV3Router);
router.use(listUserLoggedinDevicesV3Router);
router.use(logoutUserFromLoggedinDevicesV3Router);
router.use(listUserMFAEnrollmentsV3Router);
router.use(resetUserMFAEnrollmentsV3Router);
router.use(updateUserPasswordV3Router);
router.use(updateUserEmailV3Router);
router.use(updateUsersStatusV3Router);
router.use(updateUserDetailsV3Router);

// Authentication logs
router.use(listAuthenticationLogsRouter);
router.use(fetchAuthenticationLogsFiltersRouter);

router.use(auth0LogsWebhookRouter);
export default router;
