import express, { Request, Response, NextFunction } from "express";
import { responseHand<PERSON> } from "@moxfive-llc/common";

const router = express.Router();

router.get(
    "/v1/redirect",
    responseHandler,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "AzureAppWebRedirectURI";

            const code = req.query.code;

            delete req.query.code;
            delete req.query.client_info;
            delete req.query.session_state;

            res.sendResponse({ code }, {});
        }
        catch(error) {
            next(error);
        }
    }
);

export { router as redirectRouter };
