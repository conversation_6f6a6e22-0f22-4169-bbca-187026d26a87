import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    deleteFile,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    validateRequest,
    File
} from "@moxfive-llc/common";
import { logoAndFaviconParamValidation } from "./delete-logo-and-favicon.validations";
import { Organization } from "../../models/organization";
import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";

const router = express.Router();

router.delete("/v1/organizations/:organizationId/upload/logo",
    currentUser,
    requireAuth,
    logoAndFaviconParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasPermission = await hasGlobalAction(req, "RemoveProfileIcon");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;
            const { entity } = req.query as { entity: string };

            const organization = await Organization.findById(organizationId);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const fileDetails = await File.findOne({
                parent: organization.bucketName,
                parentId: organizationId,
                entity: `${(entity ?? "").toLowerCase()}`,
                entityId: organizationId,
            }).lean().exec();

            if (!fileDetails) {
                // TODO ERROR
                throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "File not found.");
            }

            await deleteFile({
                parent: organization.bucketName!,
                parentId: organizationId,
                entity: fileDetails.entity.toLowerCase(),
                entityId: organizationId,
                fileId: fileDetails._id as string
            });

            if (entity.toLowerCase() === "profile") {
                organization.profile = null;
                if (organization.imageAndLogoSame) {
                    organization.favicon = null;
                    organization.imageAndLogoSame = false;
                }
            }
            else if (entity.toLowerCase() === "favicon") {
                organization.favicon = null;
            }

            await organization.save();
            await OrganizationUpdatedPublisherWrapper(organization);

            res.json({
                meta: {
                    message: "File has been deleted successfully."
                }
            });
        }
        catch (error) {
            console.error("Authentication.RemoveOrganizationProfileImage");
            console.error(error);
            next(error);
        }
    });

export { router as removeLogoOrFavicon };
