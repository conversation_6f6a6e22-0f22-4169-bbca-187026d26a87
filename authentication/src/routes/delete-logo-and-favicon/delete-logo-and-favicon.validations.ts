import { mongoIDValidator, stringValidator } from "../../util/express-validator-wrapper";

export const logoAndFaviconParamValidation = [
    ...mongoIDValidator([
        {
            name: "organizationId",
            param: true,
            message: "Organizaiton ID must be valid."
        }
    ]),
    ...stringValidator([
        {
            name: "entity",
            query: true,
            customValidators: [((value: string) => {
                if (!(["profile", "favicon"].includes(value))) {
                    throw new Error("Entity can be any from this list only: profile, favicon.");
                }
                else {
                    return true;
                }
            })],
            message: "entity must be string."
        }
    ])
];
