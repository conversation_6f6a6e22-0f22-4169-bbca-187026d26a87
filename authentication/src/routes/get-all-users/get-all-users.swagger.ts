/**
 * @swagger
 * /v1/users:
 *   get:
 *     summary: Get All Users
 *     description: This is used to get all users of system
 *     tags:
 *       - Users
 *     parameters:
 *       - in: query
 *         name: search
 *         required: false
 *         description: Search term(either name or email)
 *         schema:
 *           type : string
 *       - in: query
 *         name: page
 *         required: false
 *         description: page
 *         schema:
 *           type : number
 *           format: int64
 *           minimum: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         description: limit
 *         schema:
 *           type : number
 *           format: int64
 *           minimum: 1
 *           maximum: 999
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Fetch All users
 *                 description: Fetch All users
 *                 value:
 *                   totalRows: 2
 *                   data:
 *                     - email: <EMAIL>
 *                       isEnabled: true
 *                       firstName: Romit
 *                       lastName: Gandhi
 *                       streetAddress: null
 *                       country: null
 *                       state: null
 *                       city: null
 *                       postalCode: null
 *                       officePhone: null
 *                       alternateEmail: null
 *                       jobTitle: null
 *                       companyName: null
 *                       roleId: 62397eb4faf84ac1d0f5fe4d
 *                       lastSignIn: '2022-03-25T10:55:22.483Z'
 *                       createdAt: '2022-03-22T07:35:23.063Z'
 *                       updatedAt: '2022-03-22T07:48:15.889Z'
 *                       id: 62397c3b020359bf2f08426a
 *                     - email: <EMAIL>
 *                       isEnabled: true
 *                       firstName: null
 *                       lastName: null
 *                       streetAddress: null
 *                       country: null
 *                       state: null
 *                       city: null
 *                       postalCode: null
 *                       officePhone: null
 *                       alternateEmail: null
 *                       jobTitle: null
 *                       companyName: null
 *                       roleId: 62399b66b33821dd1652ef0d
 *                       lastSignIn: '2022-03-25T10:55:22.483Z'
 *                       createdAt: '2022-03-22T09:31:46.687Z'
 *                       updatedAt: '2022-03-22T09:50:26.495Z'
 *                       id: 62399782ab76507a85aad69a
 *               Second:
 *                 summary: Fetch all users with search filter (name)
 *                 description: Fetch all users with search filter (name - Rom)
 *                 value:
 *                   totalRows: 1
 *                   data:
 *                     - email: <EMAIL>
 *                       isEnabled: true
 *                       firstName: Romit
 *                       lastName: Gandhi
 *                       streetAddress: null
 *                       country: null
 *                       state: null
 *                       city: null
 *                       postalCode: null
 *                       officePhone: null
 *                       alternateEmail: null
 *                       jobTitle: null
 *                       companyName: null
 *                       roleId: 62399b66b33821dd1652ef0d
 *                       lastSignIn: '2022-03-25T10:55:22.483Z'
 *                       createdAt: '2022-03-22T09:31:46.687Z'
 *                       updatedAt: '2022-03-22T09:50:26.495Z'
 *                       id: 62399782ab76507a85aad69a
 *               Third:
 *                 summary: Fetch organization users with search filter (email)
 *                 description: Fetch organization users with search filter (email = <EMAIL>)
 *                 value:
 *                   totalRows: 1
 *                   data:
 *                     - email: <EMAIL>
 *                       isEnabled: true
 *                       firstName: Romit
 *                       lastName: Gandhi
 *                       streetAddress: null
 *                       country: null
 *                       state: null
 *                       city: null
 *                       postalCode: null
 *                       officePhone: null
 *                       alternateEmail: null
 *                       jobTitle: null
 *                       companyName: null
 *                       roleId: 62399b66b33821dd1652ef0d
 *                       lastSignIn: '2022-03-25T10:55:22.483Z'
 *                       createdAt: '2022-03-22T09:31:46.687Z'
 *                       updatedAt: '2022-03-22T09:50:26.495Z'
 *                       id: 62399782ab76507a85aad69a
 *               Fourth:
 *                 summary: Fetch organization users with page and limit filter
 *                 description: Fetch organization users with page and limit (page=2 ,limit=1)
 *                 value:
 *                   totalRows: 2
 *                   data:
 *                     - email: <EMAIL>
 *                       isEnabled: true
 *                       firstName: null
 *                       lastName: null
 *                       streetAddress: null
 *                       country: null
 *                       state: null
 *                       city: null
 *                       postalCode: null
 *                       officePhone: null
 *                       alternateEmail: null
 *                       jobTitle: null
 *                       companyName: null
 *                       roleId: 62399b66b33821dd1652ef0d
 *                       lastSignIn: '2022-03-25T10:55:22.483Z'
 *                       createdAt: '2022-03-22T09:31:46.687Z'
 *                       updatedAt: '2022-03-22T09:50:26.495Z'
 *                       id: 62399782ab76507a85aad69a
 *       400:
 *         description: Invalid Query params
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {"message": "Search term must be more than 1 character", "field": "search"},
 *                      {"message": "Page number must be positive integer", "field": "page"},
 *                      {"message": "Limit must be positive integer between 1-999", "field": "limit"},
 *                  ]
 *              }
 *       401:
 *         description: Not Authorized
 *         content:
 *           application/json:
 *             example:
 *               errors:
 *                 - message: Not authorized
 */
