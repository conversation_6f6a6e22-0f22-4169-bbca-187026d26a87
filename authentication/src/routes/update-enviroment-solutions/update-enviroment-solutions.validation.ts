import { Valida<PERSON><PERSON><PERSON><PERSON> } from "express-validator";
import { stringValidator } from "../../util/express-validator-wrapper";
import { flexibleFieldReqBodyValidation } from "../../util/incident-flexible-field-req-body-validation";
import { authenticationFields } from "../../util/authentication-fields";
import { param } from "express-validator";

export const UpdateEnvironmentSolutionsDetailValidation = () => {
    const validations: ValidationChain[] = [
        param("organizationId")
            .isMongoId(),
    ];

    stringValidator([
        {
            name: "firewallNotes",
            maxLength: 2000,
            nullable: true,
            message: "Firewall notes must be string and of max 2000 characters long."
        },
        {
            name: "antivirusNotes",
            maxLength: 2000,
            nullable: true,
            message: "Antivirus notes must be string and of max 2000 characters long."
        },
        {
            name: "edrNotes",
            maxLength: 2000,
            nullable: true,
            message: "EDR notes must be string and of max 2000 characters long."
        },
        {
            name: "remoteAccessNotes",
            maxLength: 2000,
            nullable: true,
            message: "Remote access notes must be string and of max 2000 characters long."
        },
        {
            name: "systemManagementNotes",
            maxLength: 2000,
            nullable: true,
            message: "System management notes must be string and of max 2000 characters long."
        },
        {
            name: "pamSolutionNotes",
            maxLength: 2000,
            nullable: true,
            message: "PAM solution notes must be string and of max 2000 characters long."
        },
        {
            name: "segmentationSolutionNotes",
            maxLength: 2000,
            nullable: true,
            message: "Segmentation solution notes must be string and of max 2000 characters long."
        }
    ], validations);

    flexibleFieldReqBodyValidation(
        authenticationFields.environment.solutions.filter(f => f.flexibleField).map(f => {
            return {
                name: f.name,
                minValuesLength: 0,
                nullable: true
            };
        }),
        validations
    );

    return validations;

};
