import express, { NextFunction, Request, Response } from "express";
import {
    currentUser, hasGlobalAction, InsufficientPrivilagesError, NotFoundCode, requireAuth,
    ResourceNotFoundError, responseHandler, TargetType, validateRequest
} from "@moxfive-llc/common";
import { OrganizationV2 } from "../../models/v2/oragnizations-v2";
import { intersectTwoObjects, pickFromObject } from "../../util";
import { Environment } from "../../models/organization-enviroment";
import { flexibleFieldValidation } from "../../util/flexible-field-validation-middleware";
import { UpdateEnvironmentGeneralDetailValidation } from "./update-environment-general-details.validation";
import { authenticationFields } from "../../util/authentication-fields";
import { AuditLog } from "../../services/audit-log";
import { MakeSingleSectionFieldsFlexibleFields } from "../../util/make-single-section-fields-flexible-fields";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../util/v2/organization-updated-publisher-wrapper";
import { MongoTransaction } from "../../services/mongo-transaction";
import { organizationV2ToV1Sync } from "../../util/organization-v2-to-v1-sync";
import { OperationTypesEnums } from "../../enums/operation-types.enum";

const router = express.Router();

// Fetch all fields and flexible fields
const { fields, flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.environment.general);

router.put("/v1/organizations/:organizationId/environment/general",
    responseHandler,
    currentUser,
    requireAuth,
    UpdateEnvironmentGeneralDetailValidation(),
    validateRequest,
    flexibleFieldValidation(flexibleFieldsNameKey),
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1:  Check if user has a ability
            const hasPermission = await hasGlobalAction(req, "UpdateOrganizationEnvironmentGeneralDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Step 2: Check incident is valid or not
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
            if(!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3 : Get Environment General Record
            let modifiedProperties: any = [];

            const environmentGeneral: any  = await Environment.findOne({ organizationId }).session(mongoTransaction.session);
            // If Environment General not found
            if(!environmentGeneral) {
                // If enviroment not present then create new record
                const data = pickFromObject(req.body, fields);
                await Environment.build({
                    generalInformation: {
                        ...data,
                        createdAt: Date.now(),
                        createdBy: req.currentUser?.id || null
                    },
                    organizationId: organizationId
                }).save({ session: mongoTransaction.session });

                await organization.save({ session: mongoTransaction.session });

                await organizationV2ToV1Sync({
                    organization: organization.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit transaction
                await mongoTransaction.commitTransaction();

                await OrganizationUpdatedPublisherWrapperV2(organization);

                // Prepare data for audit logs
                modifiedProperties = AuditLog.prepareModifiedProperties({
                    data,
                    req,
                    flexibleFieldsNameKey,
                    target: TargetType.ORGANIZATION
                });

                return res.sendResponse({
                    meta: {
                        message: "Environment General updated successfully."
                    }
                }, {
                    targets: [{
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    }],
                    correlation: TargetType.ORGANIZATION,
                    correlationId: organizationId,
                    modifiedProperties
                });
            }

            // Step 4: Fetch only those fields which are chagned and updated that
            const environmentGeneralDetail = pickFromObject(environmentGeneral.generalInformation ?? {}, fields);
            const updatedData = intersectTwoObjects(environmentGeneralDetail, req.body);

            if(Object.keys(updatedData).length) {
                environmentGeneral.generalInformation = environmentGeneral.generalInformation ?? {};
                Object.assign(environmentGeneral.generalInformation, updatedData);
                environmentGeneral.generalInformation.updatedBy = req.currentUser?.id || null;

                await Promise.all([environmentGeneral.save({ session: mongoTransaction.session }), organization.save({ session: mongoTransaction.session })]);

                await organizationV2ToV1Sync({
                    organization: organization.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit transaction
                await mongoTransaction.commitTransaction();

                await OrganizationUpdatedPublisherWrapperV2(organization);

                // Prepare data for audit logs
                const data = { ...updatedData };
                const oldData = { ...environmentGeneralDetail };

                modifiedProperties = AuditLog.prepareModifiedProperties({
                    data,
                    flexibleFieldsNameKey,
                    req,
                    oldData,
                    target: TargetType.ORGANIZATION
                });
            }

            // Step 5:  Send response
            res.sendResponse({
                meta: {
                    message: "Environment General updated successfully."
                }
            }, modifiedProperties.length ? {
                targets: [{
                    type: TargetType.ORGANIZATION,
                    details: {
                        id: organizationId,
                        name: organization.name
                    }
                }],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId,
                modifiedProperties
            } : {});
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateEnvironmentGeneralDetail");
            console.error(error);
            next(error);
        }
    });

export { router as UpdateEnvironmentGeneralDetailRouter };
