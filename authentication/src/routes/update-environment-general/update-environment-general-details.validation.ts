import { Valida<PERSON><PERSON>hai<PERSON> } from "express-validator";
import { numberValidator, stringValidator } from "../../util/express-validator-wrapper";
import { authenticationFields } from "../../util/authentication-fields";
import { flexibleFieldReqBodyValidation } from "../../util/incident-flexible-field-req-body-validation";
import { param } from "express-validator";

export const UpdateEnvironmentGeneralDetailValidation = () => {
    const validations: ValidationChain[] = [
        param("organizationId")
            .isMongoId(),
    ];

    numberValidator([
        {
            name: "numberOfADUsers",
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of AD users must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfPhysicalServers",
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of physical servers must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfVirtualServers",
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of virtual servers must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfWorkstations",
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of workstations must be positive number and of max 16 digits long."
        }
    ], validations);

    stringValidator([
        {
            name: "outOfBandManagementNotes",
            maxLength: 2000,
            nullable: true,
            message: "Out of band nanagement notes must be string and of max 2000 characters long."
        }
    ], validations);

    flexibleFieldReqBodyValidation(
        authenticationFields.environment.general.filter(f => f.flexibleField).map(f => {
            return {
                name: f.name,
                minValuesLength: 0,
                nullable: true
            };
        }),
        validations
    );

    return validations;
};
