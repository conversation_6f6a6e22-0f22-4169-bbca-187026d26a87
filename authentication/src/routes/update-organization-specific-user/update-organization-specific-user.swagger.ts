/**
 * @swagger
 * /v1/organizations/{organizationId}/users/{userId}:
 *   put:
 *     summary: Update User Profile of Organization
 *     description: This is used to update the user profile for the specific organization
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type : string
 *       - in: path
 *         name: userId
 *         required: true
 *         description: user id
 *         schema:
 *           type : string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateUserDetails'
 *     responses:
 *       200:
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *              {
 *                meta: {
 *                  message: "User updated successfully"
 *                }
 *              }
 *       204:
 *         description: No Content (Nothing updated)
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       {"message": "firstName must be string!", "field": "firstName"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"},
 *                       { "message": "User id must be valid", "field": "userId"}
 *                     ]
 *                   }
 *               Third:
 *                 summary: User doesn't exist in the provided organization
 *                 description: User doesn't exist in the provided organization
 *                 value:
 *                   { "errors": [ { "message": "User doesn't exist in the provided organization"} ] }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             examples:
 *               Organization Not Found:
 *                 value:
 *                   { "errors": [ { "message": "Organization not found" } ] }
 *               User Not Found:
 *                 value:
 *                   { "errors": [ { "message": "User not found" } ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Something went wrong" } ] }
 */
