import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    requireAuth, responseHand<PERSON>
} from "@moxfive-llc/common";
import { authenticationFields } from "../../util/authentication-fields";
import { fetchModuleFilterFieldsBasedOnPermission } from "../../util/fetch-module-filter-fields-based-on-permission";

const router = express.Router();

router.get("/v1/organizations/facets",
    responseH<PERSON><PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check permission
            req.action = "ListOrganizationFacets";

            // Step 2: Fetch filterable fields
            const fields = await fetchModuleFilterFieldsBasedOnPermission({
                sections: [
                    {
                        path: authenticationFields.organizations,
                    },
                    {
                        path: authenticationFields.modifiedAt
                    }
                ],
                assignedActions: new Set()
            });

            // Step 3: Send response
            res.sendResponse(fields, {});
        }
        catch (error) {
            console.error("Incident.fetchOrganizationsFilters");
            console.error(error);
            next(error);
        }
    });

export { router as fetchOrganizationsFiltersRouter };
