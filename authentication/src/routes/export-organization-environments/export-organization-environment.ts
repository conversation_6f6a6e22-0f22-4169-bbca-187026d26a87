import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    getAssignedActionsFromList,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";

import { Environment } from "../../models/organization-enviroment";
import { authenticationFields } from "../../util/authentication-fields";
import { organizationPathParamValidation } from "../../validations/general/organizaiton-path-params-validation";
import { getFlexibleFieldsBasedOnActions } from "../../util/get-flexible-fields-based-on-actions";
import { getFlexibleFieldsAllValues } from "../../util/get-flexible-fields-all-values";
import { processAllFlexibleFieldUsingMapForExport } from "../../util/process-all-flexible-field-using-map-for-export";
import { exportService } from "../../services/export";
import { flattenObject } from "../../util";
import { OrganizationV2 } from "../../models/v2/oragnizations-v2";

const router = express.Router();

router.get("/v1/organizations/:organizationId/environment/export",
    responseHandler,
    currentUser,
    requireAuth,
    organizationPathParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check user has permission to get specific organization details
            const hasPermission = await hasGlobalAction(req, "ExportOrganizationEnvironmentDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Step 2: Find Organization
            const organization = await OrganizationV2.findById(organizationId).lean().exec();

            // Step 3: If organization not found then throw error
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: authenticationFields.environment.general,
                    prefix: "environmentgeneral",
                    action: "GetOrganizationEnvironmentGeneralDetail"
                },
                {
                    path: authenticationFields.environment.activeDirectory,
                    prefix: "environmentActivedirectory",
                    action: "GetOrganizationEnvironmentActiveDirectoryDetail",
                },
                {
                    path: authenticationFields.environment.backups,
                    prefix: "environmentbackups",
                    action: "GetOrganizationEnvironmentBackupDetail"
                },
                {
                    path: authenticationFields.environment.email,
                    prefix: "environmentemail",
                    action: "GetOrganizationEnvironmentEmailDetail"
                },
                {
                    path: authenticationFields.environment.solutions,
                    prefix: "environmentsolutions",
                    action: "GetOrganizationEnvironmentSolutionsDetail"
                }
            ];

            const assignedActions = await getAssignedActionsFromList(req, [
                "GetOrganizationEnvironmentGeneralDetail",
                "GetOrganizationEnvironmentActiveDirectoryDetail",
                "GetOrganizationEnvironmentBackupDetail",
                "GetOrganizationEnvironmentEmailDetail",
                "GetOrganizationEnvironmentSolutionsDetail"
            ]);

            const assignedActionsSet = new Set(assignedActions);

            // Step 4: Find Environment
            const environment = await Environment.findOne({ organizationId: organization._id }).lean().exec();

            // Step 5: If Environment not found then return blank array as data.
            if (!environment) {
                await exportService.exportFile({
                    sections,
                    assignedActions: assignedActionsSet,
                    data: [],
                    organizationName: organization.name,
                    module: "environment",
                    res
                });
                return;
            }

            const actionToFlexibleFieldArrayObj = {
                "GetOrganizationEnvironmentGeneralDetail": authenticationFields.environment.general,
                "GetOrganizationEnvironmentActiveDirectoryDetail": authenticationFields.environment.activeDirectory,
                "GetOrganizationEnvironmentBackupDetail": authenticationFields.environment.backups,
                "GetOrganizationEnvironmentEmailDetail": authenticationFields.environment.email,
                "GetOrganizationEnvironmentSolutionsDetail": authenticationFields.environment.solutions
            };

            // Step 6: get flexiblefield name key and abd create a map for it.
            const flexibleFieldsNameKey = getFlexibleFieldsBasedOnActions(assignedActions, actionToFlexibleFieldArrayObj);
            const flexibleFieldsValuesMap = await getFlexibleFieldsAllValues(flexibleFieldsNameKey.map(field => field.key));

            //move all the nested object inside a single object.
            const extractedObjects = flattenObject({ obj: environment });

            // Step 7: process all flexiblefield value.
            const proccessedResp = processAllFlexibleFieldUsingMapForExport(
                flexibleFieldsNameKey.map(field => field.name),
                flexibleFieldsValuesMap,
                extractedObjects
            );
            // eslint-disable-next-line max-len
            proccessedResp["totalNumberOfSystems"] = (proccessedResp.numberOfPhysicalServers ?? 0) + (proccessedResp.numberOfVirtualServers ?? 0) + (proccessedResp.numberOfWorkstations ?? 0);

            // Step 8: Export file
            await exportService.exportFile({
                sections,
                assignedActions: assignedActionsSet,
                data: [proccessedResp],
                organizationName: organization.name,
                module: "environment",
                res
            });
        }
        catch (error) {
            console.error("Authentication.exportOrganizationEnvironment");
            console.error(error);
            next(error);
        }
    });

export { router as exportOrganizationEnvironments };
