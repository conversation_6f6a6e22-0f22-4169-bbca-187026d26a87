import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import { Organization } from "../../models/organization";
import { User } from "../../models/user";
import { GetOrganizationSpecificUserResponse } from "../../interfaces";
import { getUserDetailValidation } from "./get-user-detail.validation";
import { UserFlexibleField } from "../../models/user-flexible-fields";

const router = express.Router();

router.get("/v1/users/:userId",
    responseHandler,
    currentUser,
    requireAuth,
    getUserDetailValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to read users of organization
            const hasPermission = await hasGlobalAction(req, "GetUserDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { userId } = req.params;

            // Step 1: If user is not found then throw NotFoundError
            const user = await User.findById(userId)
                .select("-version");

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Step 2: If user organization not found then throw NotFoundError
            const organization = await Organization.findById(user.organizationId).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3: Check user is exist in that organization, if not exist then throw error
            // let userExistInOrganization = false;
            // let isOwner = false;

            // if (organization.owner.includes(user.azureId)) {
            //     userExistInOrganization = true;
            //     isOwner = true;
            // }
            // else if (organization.member.includes(user.azureId)) {
            //     userExistInOrganization = true;
            //     isOwner = false;
            // }

            // if (!userExistInOrganization) {
            //     throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            // }

            // Step 4: Send response
            const response: GetOrganizationSpecificUserResponse = {
                ...user.toJSON(),
                _id: user._id as string,
            };

            if (response.role) {
                const roleDetails = await UserFlexibleField.findOne({ "values._id": response.role }, { _id: 0, values: { $elemMatch: { _id: response.role } } });
                response.role = roleDetails?.values[0] as { id: string, value: string };
            }
            else {
                response.role = null;
            }

            response.organization = {
                id: organization._id as string,
                name: organization.name,
                favicon: organization.favicon ?? null
            };
            // response.isOwner = isOwner;
            delete response.policyIds;
            // delete response.azureId;
            delete response.keys;

            res.sendResponse(response, {});
        }
        catch (error) {
            console.error("Authentication.GetUserDetail");
            console.error(error);
            next(error);
        }
    });

export { router as getUserDetailRouter };
