/**
 * @swagger
 * /v1/users/me:
 *   get:
 *     name: Get user details
 *     summary: Get user details
 *     description: This API will fetch authenticated user details.
 *     tags:
 *       - Users
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               {
 *                 "streetAddress": null,
 *                 "country": null,
 *                 "state": null,
 *                 "city": null,
 *                 "postalCode": null,
 *                 "officePhone": null,
 *                 "alternateEmail": null,
 *                 "jobTitle": null,
 *                 "companyName": null,
 *                 "azureId": "d4a4fd99-1225-455f-83d0-0bfd59e47f98",
 *                 "email": "<EMAIL>",
 *                 "isEnabled": true,
 *                 "firstName": "Darshan",
 *                 "lastName": "Vesatiya",
 *                 "createdAt": "2022-03-15T13:48:22.269Z",
 *                 "updatedAt": "2022-03-24T06:05:43.639Z",
 *                 "version": 5,
 *                 "roleId": "623042bb540c054da1d312f2",
 *                 "lastSignIn": "2022-03-24T06:05:43.625Z",
 *                 "id": "62309926987d16194ae3547a"
 *               }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "User not found!" } ] }
 */
