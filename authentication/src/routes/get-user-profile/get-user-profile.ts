// /* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
// import {
//     currentUser,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     responseHandler
// } from "@moxfive-llc/common";
// import express, { Request, Response, NextFunction } from "express";
// import { UserDetailsResponse } from "../../interfaces";
// import { Organization } from "../../models/organization";

// import { User } from "../../models/user";
// import { UserFlexibleField } from "../../models/user-flexible-fields";
// import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
// import { OrganizationType } from "../../models/organization-type";

// const router = express.Router();

// router.get(
//     "/v1/users/me",
//     (req, res, next) => {
//         console.info("Signed Cookies", req.signedCookies, req.signedCookies.Token1);
//         console.info("Cookies", JSON.stringify(req.cookies, null, 2).replace(/[\n\r]/g, ""));
//         next();
//     },
//     responseHandler,
//     currentUser,
//     requireAuth,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             req.action = "GetUserProfile";

//             const userId = req.currentUser?.id;
//             const userDetails = await User.findById(userId);
//             if (!userDetails) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
//             }

//             let userOrgDetails = await Organization.findById(userDetails.organizationId);
//             if (!userOrgDetails) {
//                 userOrgDetails = await Organization.findOne({ $or: [{ owner: userDetails.azureId }, { member: userDetails.azureId }] });
//                 userDetails.organizationId = userOrgDetails?._id as string;
//                 await userDetails.save();
//                 await userUpdatedPublisherWrapper(userDetails);
//             }

//             const isOwner = userOrgDetails?.owner.includes(userDetails.azureId);

//             const userDetailsResponse: UserDetailsResponse = {
//                 ...userDetails.toJSON(),
//             };

//             if (userDetailsResponse.role) {
//                 const roleDetails = await UserFlexibleField.findOne({
//                     "values._id": userDetailsResponse.role
//                 },
//                 {
//                     _id: 0, values: { $elemMatch: { _id: userDetailsResponse.role } }
//                 });
//                 userDetailsResponse.role = roleDetails?.values[0] as { id: string, value: string };
//             }
//             else {
//                 userDetailsResponse.role = null;
//             }

//             const userOrgTypesDetails = await OrganizationType.find({ _id: { $in: userOrgDetails?.organizationTypeIds } }).select("name");

//             userDetailsResponse["organization"] = {
//                 id: userOrgDetails?._id as string,
//                 name: userOrgDetails?.name!,
//                 organizationTypes: userOrgTypesDetails?.length ? userOrgTypesDetails.map((data) => data.name) : []
//             };
//             userDetailsResponse["isOwner"] = !!isOwner;
//             delete userDetailsResponse.azureId;
//             delete userDetailsResponse.organizationId;
//             delete userDetailsResponse.version;
//             delete userDetailsResponse.policyIds;
//             delete userDetailsResponse.keys;

//             res.sendResponse(userDetailsResponse, {});
//         }
//         catch (error) {
//             console.error("Authentication.GetUserProfile");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as getUserProfileRouter };
