/**
 * @swagger
 * /v1/logout:
 *   get:
 *     name: Log out
 *     summary: Log out
 *     description: This API will expire logged user session and remove cookies.
 *     tags:
 *       - Credentials
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               {meta: {message: "User has been logged out successfully"}}
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 */
