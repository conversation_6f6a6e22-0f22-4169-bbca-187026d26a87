// import express, { NextFunction, Request, Response } from "express";
// import { UserAzureDetails } from "../../interfaces";
// import { User } from "../../models/user";
// import {
//     UserAzureUpdates,
//     UserAzureUpdatesDoc,
// } from "../../models/user-azure-updates";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { getUserName, intersectTwoObjects } from "../../util";
// import { mapUserDetails } from "../../util";
// import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
// import { Country } from "../../models/country";
// import { State } from "../../models/state";
// import { City } from "../../models/city";

// interface userDetailsObj {
//   data: UserAzureDetails;
// }

// const router = express.Router();

// router.post(
//     "/v1/authentication/userupdate/callback",
//     async (req: Request, res: Response, next: NextFunction):Promise<any> => {
//         try {
//             req.action = "AzureUsersUpdatesWebhook";

//             const { validationToken } = req.query;
//             const { value } = req.body;

//             if (validationToken) {
//                 return res.set("content-type", "text/plain").send(validationToken);
//             }

//             if (Array.isArray(value) && value.length) {
//                 // Send 202 (Accepted) response to acknowledge microsoft azure
//                 res.status(202).send();

//                 // Prepare the data and save it in the db
//                 const preparedObj: any = [];
//                 value.map((item: any) => {
//                     if (
//                         !process.env.CLIENT_STATE || item.clientState === process.env.CLIENT_STATE
//                     ) {
//                         preparedObj.push({
//                             userId: item?.resourceData["id"],
//                             changeType: item?.changeType,
//                             status: "Not Started",
//                         });
//                     }
//                 });

//                 await UserAzureUpdates.insertMany(preparedObj);
//                 const notStartedUsers = await UserAzureUpdates.find({
//                     status: "Not Started",
//                 });

//                 // Get unique records to process them
//                 const uniqueRecords: UserAzureUpdatesDoc[] = [];
//                 notStartedUsers.forEach((user) => {
//                     if (
//                         !uniqueRecords.find(
//                             (record: UserAzureUpdatesDoc) => record.userId === user.userId && record.changeType === user.changeType
//                         )
//                     ) {
//                         uniqueRecords.push(user);
//                     }
//                 });

//                 // Change the started records to in progress
//                 await Promise.all(
//                     notStartedUsers.map(async (user) => {
//                         user.status = "In Progress";
//                         await user.save();
//                     })
//                 );

//                 await Promise.all(
//                     uniqueRecords.map(async (user) => {
//                         try {
//                             // Check user exist in our system, if user don't exists then delete the user azure updates colleciton details
//                             const oldUserDetails = await User.getUserByAzureId({
//                                 azureId: user.userId,
//                             });
//                             if (!oldUserDetails) {
//                                 await UserAzureUpdates.deleteMany({
//                                     userId: user.userId,
//                                     status: "In Progress",
//                                 });
//                                 return false;
//                             }

//                             if (user.changeType === "updated") {
//                                 // If user is valid then update user details
//                                 const accessToken = await microsoftGraphAPI.getAccessToken();
//                                 const newUserDetails: userDetailsObj = await microsoftGraphAPI.getAzureUserDetailsById({
//                                     accessToken,
//                                     userId: user.userId,
//                                 });

//                                 const mappedUserDetails: any = mapUserDetails(newUserDetails.data);
//                                 if (mappedUserDetails.country) {
//                                     const countryDetails = await Country.findOne({ name: { $regex: `^${mappedUserDetails.country}$`, $options: "i" } }).lean().exec();
//                                     mappedUserDetails.country = countryDetails ? String(countryDetails._id) : null;
//                                 }
//                                 if (mappedUserDetails.state) {
//                                     const query = mappedUserDetails.country
//                                         ? { name: { $regex: `^${mappedUserDetails.state}$`, $options: "i" }, countryId: mappedUserDetails.country }
//                                         : { name: { $regex: `^${mappedUserDetails.state}$`, $options: "i" } };
//                                     const stateDetails = await State.findOne(query).lean().exec();
//                                     mappedUserDetails.state = stateDetails ? String(stateDetails._id) : null;
//                                 }
//                                 if (mappedUserDetails.city) {
//                                     const query = mappedUserDetails.state
//                                         ? { name: { $regex: `^${mappedUserDetails.city}$`, $options: "i" }, stateId: mappedUserDetails.state }
//                                         : { name: { $regex: `^${mappedUserDetails.city}$`, $options: "i" } };
//                                     const cityDetails = await City.findOne(query).lean().exec();
//                                     mappedUserDetails.city = cityDetails ? String(cityDetails._id) : null;
//                                 }

//                                 const updatedData = intersectTwoObjects(
//                                     oldUserDetails,
//                                     mappedUserDetails
//                                 );

//                                 if (Object.keys(updatedData).length) {
//                                     // If firstName, lastName or displayName updated then update the name
//                                     if(updatedData.firstName || updatedData.lastName || updatedData.displayName) {
//                                         updatedData.name = getUserName({
//                                             firstName: updatedData.firstName ?? oldUserDetails.firstName,
//                                             lastName: updatedData.lastName ?? oldUserDetails.lastName,
//                                             displayName: updatedData.displayName ?? oldUserDetails.displayName,
//                                         });
//                                     }

//                                     Object.assign(oldUserDetails!, updatedData);
//                                     await oldUserDetails?.save();
//                                     await userUpdatedPublisherWrapper(oldUserDetails!);
//                                 }
//                             }
//                             else {
//                                 // If user is deleted from azure then disable the user in our system
//                                 oldUserDetails.isEnabled = false;
//                                 await oldUserDetails.save();
//                                 await userUpdatedPublisherWrapper(oldUserDetails!);
//                             }

//                             // Delete the records after update
//                             await UserAzureUpdates.deleteMany({
//                                 userId: user.userId,
//                                 status: "In Progress",
//                             });
//                         }
//                         catch (err) {
//                             if (err instanceof Error) {
//                                 await UserAzureUpdates.updateMany(
//                                     { userId: user.userId, status: "In Progress" },
//                                     { $set: { errorMessage: err.message, status: "Not Started" } }
//                                 );
//                             }

//                             // Send an email notification
//                         }
//                     })
//                 );
//             }
//         }
//         catch (error) {
//             console.error("Authetication.UserUpdateCallback");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as userUpdateCallbackRouter };
