import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    requireA<PERSON>,
    response<PERSON><PERSON><PERSON>,
    validateRequest
} from "@moxfive-llc/common";
import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
import { sortByPropertyInObject } from "../../util";
import {
    getSpecificFlexibleFieldsForOrganizationValidation
} from "../../validations/get-specific-flexible-fields-for-organization.validation";

const router = express.Router();

router.post("/v1/organizations/fields/byKeys",
    responseHandler,
    currentUser,
    requireAuth,
    getSpecificFlexibleFieldsForOrganizationValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "ListOrganizationFlexibleFieldsByKeys";

            const { keys } = req.body;

            // Step 2: Fetch incident flexible fields form the keys provided
            const flexibleFields = await OrganizationFlexibleField.find({ key: { $in: keys } }, { createdAt: 0, updatedAt: 0, version: 0 });

            const response = flexibleFields.map(data => {
                return {
                    ...data.toJSON(),
                    values: data.values.sort(sortByPropertyInObject("value"))
                };
            });
            return res.sendResponse(response, {});

        }
        catch (error) {
            console.error("Authentication.GetSpecificFlexibleFieldsForOrganization");
            console.error(error);
            next(error);
        }
    });

export { router as getSpecificFlexibleFieldsForOrganizationRouter };
