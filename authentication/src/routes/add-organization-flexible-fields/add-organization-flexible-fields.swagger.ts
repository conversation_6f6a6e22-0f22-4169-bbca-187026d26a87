/**
 * @swagger
 * /v1/organizations/fields:
 *   post:
 *     name: Add Flexible Fields to Organization.
 *     summary: Add Flexible Fields to Organization.
 *     description: this will Add Flexible Fields to Organization.
 *     tags:
 *       - Organizations
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              required:
 *                - id
 *                - value
 *              properties:
 *                id:
 *                  type: string
 *                  example: "624590e7bf392b108efb5d52"
 *                value:
 *                  type: string
 *                  example: "Maine"
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                    meta: {
 *                        message: "Value added successfully"
 *                    }
 *                }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization Field id must be exist",
 *                          "field": "id"
 *                      },
 *                      {
 *                          "message": "Send Value for a field",
 *                          "field": "value"
 *                      }
 *                  ]
 *              }
 */
