import { body } from "express-validator";

export const addOrganizationFlexibleFieldsValidation = [
    body("id")
        .exists()
        .bail()
        .isMongoId()
        .withMessage("Organization Field id must be valid."),

    body("value")
        .exists()
        .bail()
        .isString()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage("Value must be string and of max 50 characters long."),
];
