import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InvalidResourceIdBadRequestError,
    requireA<PERSON>,
    ResourceAlreadyExistBadR<PERSON>questError, response<PERSON><PERSON><PERSON>,
    ResourceValueUnacceptableConflictError,
    TargetType,
    validateRequest,
} from "@moxfive-llc/common";

import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
import { addOrganizationFlexibleFieldsValidation } from "./add-organization-flexible-fields.validation";
import mongoose from "mongoose";
import { OrganizationFlexibleFieldUpdatedPublisher } from "../../events/publishers/organization-flexible-field-updated-publisher";
import { natsWrapper } from "../../nats-wrapper";
import { OrganizationFlexibleFields } from "../../enums/organization-flexible-field.enum";

const router = express.Router();

router.post(
    "/v1/organizations/fields",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    addOrganizationFlexibleFieldsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to add organization
            const hasPermission = await hasGlobalAction(
                req,
                "AddOrganizationFlexibleField"
            );
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { id, value } = req.body;

            const field = await OrganizationFlexibleField.findOne({ _id: String(id) });

            // Check field is present or not and can this field is creatable
            if (!field) {
                throw new InvalidResourceIdBadRequestError([
                    { name: "id", value: id, message: "Value not found." },
                ]);
            }
            if (!field.creatable) {
                throw new ResourceValueUnacceptableConflictError([{ name: field.name, value: id, message: "This field cannot accept new value." }]);
            }

            const valueAlreadyExist = await OrganizationFlexibleField.findOne({
                _id: String(id),
                "values.value": String(value),
            });
            if (valueAlreadyExist) {
                throw new ResourceAlreadyExistBadRequestError(
                    "value",
                    value,
                    "This field is already having the specified value."
                );
            }
            const newValue = {
                _id: String(new mongoose.Types.ObjectId()),
                value,
            };

            const oldValues = field.values.map(value => {
                return {
                    id: String(value._id),
                    value: value.value
                };
            });

            field.values.push(newValue);
            await field.save();

            const newValues = [...oldValues, {
                id: String(newValue._id),
                value: newValue.value
            }];

            // If field is industry or Service Lines then publish event
            if (field.key === OrganizationFlexibleFields.Industry
                || field.key === OrganizationFlexibleFields.ServiceLines
                || field.key.includes(OrganizationFlexibleFields.incidentManagementEnvironment)
            ) {
                const dataToPublish = {
                    id: id,
                    key: field.key,
                    newValue: {
                        id: newValue._id,
                        value: newValue.value,
                    },
                    version: field.version,
                };

                await new OrganizationFlexibleFieldUpdatedPublisher(
                    natsWrapper.client
                ).publish(dataToPublish);
            }

            res.sendResponse({
                meta: {
                    message: "Value added successfully",
                    newValue: {
                        id: String(newValue._id),
                        value: newValue.value,
                    },
                },
            }, {
                targets: [
                    {
                        type: TargetType.FLEXIBLEFIELD,
                        details: {
                            id: id,
                            name: field.name
                        }
                    }
                ],
                modifiedProperties: [
                    {
                        target: TargetType.FLEXIBLEFIELD,
                        propertyName: "values",
                        oldValue: JSON.stringify(oldValues),
                        newValue: JSON.stringify(newValues)
                    }
                ]
            });
        }
        catch (error) {
            console.error("Authentication.AddOrganizationFlexibleField");
            console.error(error);
            next(error);
        }
    }
);

export { router as addOrganizationFlexibleFieldRouter };
