import { currentUser, requireAuth, responseHandler } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { OrganizationType } from "../../models/organization-type";

const router = express.Router();

router.get(
    "/v1/organizations/types",
    responseH<PERSON><PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "ListOrganizationTypes";

            const organizationTypes = await OrganizationType.find({ name: { $ne: "MOXFIVE" } }).select("name");
            res.sendResponse(organizationTypes, {});
        }
        catch (error) {
            console.error("Authentication.GetOrganizationTypes");
            console.error(error);
            next(error);
        }
    }
);

export { router as getOrganizationTypesRouter };
