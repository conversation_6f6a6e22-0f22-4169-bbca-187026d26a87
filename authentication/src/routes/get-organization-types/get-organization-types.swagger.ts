/**
 * @swagger
 * /v1/organizations/types:
 *   get:
 *     name: Fetch Organization Types.
 *     summary: Get Organization Types list.
 *     description: this will send list of Organization Types.
 *     tags:
 *       - Organizations
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                [
 *                    {
 *                        "name": "Client",
 *                        "id": "6222096db401b9512139172a"
 *                    },
 *                    {
 *                        "name": "Privacy Counsel",
 *                        "id": "62245c062fe4a628f50da609"
 *                    },
 *                    {
 *                        "name": "Insurance Carrier",
 *                        "id": "62245e402fe4a628f50da616"
 *                    },
 *                    {
 *                        "name": "Forensics",
 *                        "id": "6225df3b52eb2f857a20b3d1"
 *                    },
 *                    {
 *                        "name": "Recovery",
 *                        "id": "6225df5052eb2f857a20b3d2"
 *                    },
 *                    {
 *                        "name": "Negotiator",
 *                        "id": "6225df6952eb2f857a20b3d3"
 *                    },
 *                    {
 *                        "name": "Monitoring Counsel",
 *                        "id": "622f1b81a3fa22fb674065b8"
 *                    },
 *                    {
 *                        "name": "MOXFIVE",
 *                        "id": "622f1b9aa3fa22fb674065b9"
 *                    }
 *                ]
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 */
