/**
 * @swagger
 * /v1/users/invite:
 *   post:
 *     name: Invite user
 *     summary: Invite user
 *     description: This API will invite the user in azure active directory.
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               invitationMessage:
 *                 type: string
 *           example:
 *             { "email": "<EMAIL>", "invitationMessage": "Hey, accept the invitation!" }
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               {
 *                 "streetAddress": null,
 *                 "country": null,
 *                 "state": null,
 *                 "city": null,
 *                 "postalCode": null,
 *                 "officePhone": null,
 *                 "alternateEmail": null,
 *                 "jobTitle": null,
 *                 "companyName": null,
 *                 "azureId": "d4a4fd99-1225-455f-83d0-0bfd59e47f99",
 *                 "email": "<EMAIL>",
 *                 "isEnabled": true,
 *                 "firstName": null,
 *                 "lastName": null,
 *                 "createdAt": "2022-03-15T13:48:22.269Z",
 *                 "updatedAt": "2022-03-15T13:48:22.269Z",
 *                 "version": 0,
 *                 "policyIds": null,
 *                 "lastSignIn": null,
 *                 "id": "62309926987d16194ae3547a"
 *               }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   {
 *                     "errors": [
 *                       {
 *                          "message": "Email must be valid",
 *                          "field": "email"
 *                       },
 *                       {
 *                          "message": "invitationMessage must be a string!",
 *                          "field": "invitationMessage"
 *                       }
 *                     ]
 *                   }
 *               Second:
 *                 summary: if user is already exists in AD
 *                 description: if user is already exists in AD
 *                 value:
 *                   {
 *                     "errors": [
 *                       {
 *                          "message": "User already exist in AD with this emailId!"
 *                       }
 *                     ]
 *                   }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Something went wrong" } ] }
 */
