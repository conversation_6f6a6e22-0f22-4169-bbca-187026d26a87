import { body } from "express-validator";

export const inviteUserValidation = [
    body("email")
        .exists().bail()
        .isEmail().withMessage("Email is invalid"),

    body("displayName")
        .exists().bail()
        .isString().trim().isLength({ min: 1, max: 256 }).withMessage("Diaplay Name must be string between 1-256 characters"),

    body("invitationMessage")
        .optional()
        .isString().trim().blacklist("<>").isLength({ max: 500 }).withMessage("invitationMessage must be max 500 characters long")
];
