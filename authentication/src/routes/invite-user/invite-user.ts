// import express, { Request, Response, NextFunction } from "express";
// import {
//     currentUser,
//     requireAuth,
//     validateRequest,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     responseHandler
// } from "@moxfive-llc/common";

// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { inviteUserValidation } from "./invite-user.validation";
// import { UserInviteNotificationPublisher } from "../../events/publishers/user-invite-notification";
// import { natsWrapper } from "../../nats-wrapper";
// import { queueGroupName } from "../../events/queue-group-name";

// const router = express.Router();

// router.post(
//     "/v1/users/invite",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     inviteUserValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // Check user has permission to create user
//             const hasPermission = await hasGlobalAction(req, "InviteUser");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { email, invitationMessage, displayName } = req.body;

//             // Get microsoft access token
//             const accessToken = await microsoftGraphAPI.getAccessToken();

//             // Invite requested user
//             const inviteUserDetails = await microsoftGraphAPI.InviteUser({ accessToken, email, invitationMessage, displayName });

//             // User Invite notification publisher event
//             const data = {
//                 email,
//                 inviteRedeemUrl: inviteUserDetails.inviteRedeemUrl,
//                 displayName,
//                 serviceName: queueGroupName
//             };

//             await new UserInviteNotificationPublisher(natsWrapper.client).publish(data);

//             res.sendResponse({
//                 azureId: inviteUserDetails.azureId,
//                 email,
//                 displayName
//             }, {});
//         }
//         catch (error) {
//             console.error("Authentication.InviteUser");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as inviteUserRouter };
