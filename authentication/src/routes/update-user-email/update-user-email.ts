// import express, { NextFunction, Request, Response } from "express";
// import {
//     currentUser,
//     EmailAlreadyExistBadRequestError,
//     generateSearchKeys, hasGlobalAction,
//     InsufficientPrivilagesError,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     responseHandler,
//     TargetType,
//     validateRequest
// } from "@moxfive-llc/common";
// import { User } from "../../models/user";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
// import { updateUserEmailValidation } from "./update-user-email.validation";
// import { getSearchFields, getUserName } from "../../util";
//
// const router = express.Router();
//
// router.put("/v1/users/:userId/email",
//     responseHand<PERSON>,
//     currentUser,
//     requireAuth,
//     updateUserEmailValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             await hasGlobalAction(req, "UpdateUserEmail");
//
//             const { email }: { email: string } = req.body;
//             const { userId } = req.params;
//
//             // Step 1: Check is logged user is updating his own data or not
//             if (req.currentUser?.id !== userId) {
//                 throw new InsufficientPrivilagesError();
//             }
//
//             // Step 2: Find user and if it's not present then throw NotFoundError
//             const user = await User.getUserById({ id: userId });
//             if (!user) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found");
//             }
//
//             // Step 3: If user email is same as given then throw error
//             const oldEmail = user.email;
//             if (user.email === email) {
//                 res.sendResponse({
//                     meta: {
//                         message: "User email updated successfully"
//                     }
//                 }, {});
//             }
//
//             // Step 4: Check given email is used by any other user
//             const isUserExistWithEmail = await User.getUserByEmail({ email });
//             if (isUserExistWithEmail) {
//                 throw new EmailAlreadyExistBadRequestError();
//             }
//
//             // Step 5: Get Access token, update user email and reinvite the user in our application
//             const accessToken = await microsoftGraphAPI.getAccessToken();
//             await microsoftGraphAPI.updateUserEmail({ accessToken, azureId: user.azureId, email });
//             await microsoftGraphAPI.reinviteUser({ accessToken, azureId: user.azureId, email });
//
//             const searchFields = getSearchFields(user.firstName, user.lastName, user.displayName, email);
//             const searchKeys = generateSearchKeys(searchFields);
//             user.keys = searchKeys as string[];
//
//             // Step 6: Update user email
//             user.email = email;
//             await user.save();
//             await userUpdatedPublisherWrapper(user);
//
//             // Step 7: Send Response
//             res.sendResponse({
//                 meta: {
//                     message: "User email updated successfully"
//                 }
//             }, {
//                 targets: [
//                     {
//                         type: TargetType.USER,
//                         details: {
//                             id: userId,
//                             name: getUserName({
//                                 firstName: user.firstName,
//                                 lastName: user.lastName,
//                                 displayName: user.displayName
//                             }),
//                             email,
//                             azureId: user.azureId
//                         }
//                     }
//                 ],
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: String(user.organizationId),
//                 modifiedProperties: [
//                     {
//                         target: TargetType.USER,
//                         propertyName: "email",
//                         oldValue: oldEmail,
//                         newValue: email
//                     }
//                 ]
//             });
//         }
//         catch (error) {
//             console.error("Authentication.UpdateUserEmail");
//             console.error(error);
//             next(error);
//         }
//     });
//
// export { router as updateUserEmailRouter };
