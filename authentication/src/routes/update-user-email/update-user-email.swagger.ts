/**
 * @swagger
 * /v1/users/{userId}/email:
 *   put:
 *     name: Update User Email.
 *     summary: Update User Email.
 *     description: this will Update User Email.
 *     tags:
 *       - Users
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: user id
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateUserEmail'
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                  meta: {
 *                    message: "User email updated successfully"
 *                  }
 *                }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       {"message": "email must be valid", "field": "email"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: Email is already in use
 *                 description: Email is already in use
 *                 value:
 *                   { "errors": [ { "message": "This email is already in use"} ] }
 *       204:
 *         description: No Content (Already same email)
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "User not found!" } ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Something went wrong"
 *                      }
 *                  ]
 *              }
 */
