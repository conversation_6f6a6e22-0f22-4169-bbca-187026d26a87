// /* eslint-disable max-statements */
// import { currentUser, has<PERSON><PERSON><PERSON><PERSON>ction, requireA<PERSON>, responseH<PERSON><PERSON>, validateRequest } from "@moxfive-llc/common";
// import express, { Request, Response, NextFunction } from "express";
// import { User } from "../../models/user";
// import { getMembersOfValidation } from "./get-members-of.validation";

// const router = express.Router();

// router.post(
//     "/v1/users/membersOf",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     getMembersOfValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             req.action = "GetUserMembershipDetail";

//             const { users }: { users: string[]  } = req.body;

//             // Step 1: Fetch details of the user
//             const usersDetails = await User.find({ azureId: { $in: users } });

//             // Step 2: map user azure Id and it's organization Id
//             const userMemberOf = new Map();
//             usersDetails.forEach(user => {
//                 userMemberOf.set(user.azureId, user.organizationId);
//             });

//             // Step 3: Map user with it's organization id
//             const membersOf = users.map(user => {
//                 return {
//                     user,
//                     organization: userMemberOf.get(user) || null
//                 };
//             });

//             // Step 4: Send response
//             res.sendResponse(membersOf, {});
//         }
//         catch (error) {
//             console.error("Authentication.GetMembersOf");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as getMembersOfRouter };
