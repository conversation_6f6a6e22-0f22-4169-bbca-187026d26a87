/**
 * @swagger
 * /v1/login:
 *   get:
 *     name: Log In
 *     summary: Log In
 *     description: This API will be used to retrieve the code.
 *     tags:
 *       - Credentials
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               "https://login.microsoftonline.com/5260ca83-794f-4236-9409-32e44c6d6781/oauth2/v2.0/authorize?client_id=cfd1e807-2c5c-447c-a572-a34aa8cecd82&scope=user.read%20openid%20profile%20offline_access&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fapi%2Fv1%2Fredirect&client-request-id=e34d4112-29f3-4269-b97d-ca2891e9162b&response_mode=query&response_type=code&x-client-SKU=msal.js.node&x-client-VER=1.7.0&x-client-OS=win32&x-client-CPU=x64&client_info=1"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Something went wrong" } ] }
 */
