// import express, { Request, Response, NextFunction } from "express";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { responseHandler } from "@moxfive-llc/common";

// const router = express.Router();

// router.get(
//     "/v1/login",
//     responseHandler,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             req.action = "LogIn";

//             const response = await microsoftGraphAPI.login();
//             res.sendResponse({
//                 url: response
//             }, {});
//         }
//         catch(error) {
//             next(error);
//         }
//     }
// );

// export { router as loginRouter };
