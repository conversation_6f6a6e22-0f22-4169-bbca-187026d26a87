import express, { NextFunction, Request, Response } from "express";
import {
    currentUser, hasGlobalAction, InsufficientPrivilagesError, NotFoundCode, requireAuth,
    ResourceNotFoundError, responseHandler, TargetType, validateRequest
} from "@moxfive-llc/common";
import { OrganizationV2 } from "../../models/v2/oragnizations-v2";
import { intersectTwoObjects, pickFromObject } from "../../util";
import { Environment } from "../../models/organization-enviroment";
import { flexibleFieldValidation } from "../../util/flexible-field-validation-middleware";
import { UpdateEnvironmentActiveDirectoryDetailValidation } from "./update-environment-active-directory.validation";
import { authenticationFields } from "../../util/authentication-fields";
import { AuditLog } from "../../services/audit-log";
import { MakeSingleSectionFieldsFlexibleFields } from "../../util/make-single-section-fields-flexible-fields";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../util/v2/organization-updated-publisher-wrapper";
import { MongoTransaction } from "../../services/mongo-transaction";
import { organizationV2ToV1Sync } from "../../util/organization-v2-to-v1-sync";
import { OperationTypesEnums } from "../../enums/operation-types.enum";

const router = express.Router();

// Fetch all fields and flexible fields
const { fields, flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.environment.activeDirectory);

router.put("/v1/organizations/:organizationId/environment/activeDirectory",
    responseHandler,
    currentUser,
    requireAuth,
    UpdateEnvironmentActiveDirectoryDetailValidation(),
    validateRequest,
    flexibleFieldValidation(flexibleFieldsNameKey),
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1:  Check if user has a ability
            const hasPermission = await hasGlobalAction(req, "UpdateOrganizationEnvironmentActiveDirectoryDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }
            const { organizationId } = req.params;

            // Step 2: Check Organization is valid or not
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3 : Get Environment Active Directory Record
            let modifiedProperties: any = [];

            const environmentActiveDirectory: any = await Environment.findOne({ organizationId }).session(mongoTransaction.session);
            // If Environment Active Directory not found
            if (!environmentActiveDirectory) {
                // If Organization is in draft mode then create new record
                const data = pickFromObject(req.body, fields);
                await Environment.build({
                    activeDirectory: {
                        ...data,
                        createdAt: Date.now(),
                        createdBy: req.currentUser?.id || null
                    },
                    organizationId
                }).save({ session: mongoTransaction.session });

                await organization.save({ session: mongoTransaction.session });

                await organizationV2ToV1Sync({
                    organization: organization.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit transaction
                await mongoTransaction.commitTransaction();

                await OrganizationUpdatedPublisherWrapperV2(organization);

                // Prepare data for audit logs
                modifiedProperties = AuditLog.prepareModifiedProperties({
                    data,
                    req,
                    flexibleFieldsNameKey,
                    target: TargetType.ORGANIZATION
                });

                return res.sendResponse({
                    meta: {
                        message: "Environment active directory updated successfully."
                    }
                }, {
                    targets: [{
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    }],
                    correlation: TargetType.ORGANIZATION,
                    correlationId: organizationId,
                    modifiedProperties
                });
            }

            // Step 4: Fetch only those fields which are chagned and updated that
            const environmentActiveDirectoryDetail = pickFromObject(environmentActiveDirectory.activeDirectory ?? {}, fields);

            const updatedData = intersectTwoObjects(environmentActiveDirectoryDetail, req.body);

            if (Object.keys(updatedData).length) {
                environmentActiveDirectory.activeDirectory = environmentActiveDirectory.activeDirectory ?? {};

                Object.assign(environmentActiveDirectory.activeDirectory, updatedData);
                environmentActiveDirectory.activeDirectory.updatedBy = req.currentUser?.id || null;
                await Promise.all([environmentActiveDirectory.save({ session: mongoTransaction.session }), organization.save({ session: mongoTransaction.session })]);

                await organizationV2ToV1Sync({
                    organization: organization.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit transaction
                await mongoTransaction.commitTransaction();

                await OrganizationUpdatedPublisherWrapperV2(organization);

                // Prepare data for audit logs
                const data = { ...updatedData };
                const oldData = { ...environmentActiveDirectoryDetail };

                modifiedProperties = AuditLog.prepareModifiedProperties({
                    data,
                    flexibleFieldsNameKey,
                    req,
                    oldData,
                    target: TargetType.ORGANIZATION
                });
            }

            // Step 5:  Send response
            res.sendResponse({
                meta: {
                    message: "Environment active directory updated successfully."
                }
            }, modifiedProperties.length ? {
                targets: [{
                    type: TargetType.ORGANIZATION,
                    details: {
                        id: organizationId,
                        name: organization.name
                    }
                }],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId,
                modifiedProperties
            } : {});
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateEnvironmentActiveDirectoryDetail");
            console.error(error);
            next(error);
        }
    });

export { router as UpdateEnvironmentActiveDirectoryDetailRouter };
