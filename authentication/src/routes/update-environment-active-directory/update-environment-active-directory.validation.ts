import { Valida<PERSON><PERSON>hai<PERSON> } from "express-validator";
import { booleanValidator, numberValidator, stringValidator } from "../../util/express-validator-wrapper";
import { flexibleFieldReqBodyValidation } from "../../util/incident-flexible-field-req-body-validation";
import { authenticationFields } from "../../util/authentication-fields";
import { param } from "express-validator";

export const UpdateEnvironmentActiveDirectoryDetailValidation = () => {
    const validations: ValidationChain[] = [
        param("organizationId")
            .isMongoId(),
    ];

    numberValidator([
        {
            name: "numberOfDCs",
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of DCs must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfDomains",
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of Domains must be positive number and of max 16 digits long."
        }
    ], validations);

    booleanValidator([
        {
            name: "azureAD",
            nullable: true,
            message: "Azure active directory must be boolean."
        },
        {
            name: "azurePasswordWriteback",
            nullable: true,
            message: "Azure active directory writeback must be boolean."
        }
    ], validations);

    stringValidator([
        {
            name: "adNotes",
            maxLength: 2000,
            nullable: true,
            message: "AD notes must be string and of max 2000 characters long."
        }
    ], validations);

    flexibleFieldReqBodyValidation(
        authenticationFields.environment.activeDirectory.filter(f => f.flexibleField).map(f => {
            return {
                name: f.name,
                minValuesLength: 0,
                nullable: true
            };
        }),
        validations
    );

    return validations;
};
