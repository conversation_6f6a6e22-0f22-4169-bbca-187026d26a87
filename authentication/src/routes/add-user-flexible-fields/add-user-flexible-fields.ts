import express, { NextFunction, Request, Response } from "express";
import {
    currentUser, hasGlobalAction, InsufficientPrivilagesError, InvalidResourceIdBadRequestError, requireAuth,
    ResourceAlreadyExistBadRequestError, responseHandler, TargetType, validateRequest
} from "@moxfive-llc/common";
import { addUserFlexibleFieldsValidation } from "./add-user-flexible-fields.validation";
import { UserFlexibleField } from "../../models/user-flexible-fields";
import mongoose from "mongoose";

const router = express.Router();

router.post("/v1/users/fields",
    responseHandler,
    currentUser,
    requireAuth,
    addUserFlexibleFieldsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to add organization
            const hasPermission = await hasGlobalAction(req, "AddUserFlexibleField");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { id, value } = req.body;

            // Step 1: Check Flexible field exist
            const field = await UserFlexibleField.findById(String(id));
            if (!field) {
                throw new InvalidResourceIdBadRequestError([{ name: "id", value: id, message: "Value not found." }]);
            }

            // Step 2: Check value already exist
            const existingValueRecord = field.values.find(field => {
                return field.value === value;
            });
            if (existingValueRecord) {
                throw new ResourceAlreadyExistBadRequestError("value", value, "This field is already having the specified value.");
            }

            // Step 3: add value
            const newValue = {
                _id: String(new mongoose.Types.ObjectId),
                value
            };

            const oldValues = field.values.map(value => {
                return {
                    id: String(value._id),
                    value: value.value
                };
            });

            field.values.push(newValue);
            await field.save();

            const newValues = [...oldValues, { id: String(newValue._id),
                value: newValue.value
            }];

            // Step 4: Send response
            res.sendResponse({
                meta: {
                    message: "Value added successfully",
                    newValue: {
                        id: newValue._id,
                        value: newValue.value
                    },
                }
            }, {
                targets: [
                    {
                        type: TargetType.FLEXIBLEFIELD,
                        details: {
                            id: id,
                            name: field.name
                        }
                    }
                ],
                modifiedProperties: [
                    {
                        target: TargetType.FLEXIBLEFIELD,
                        propertyName: "values",
                        oldValue: JSON.stringify(oldValues),
                        newValue: JSON.stringify(newValues)
                    }
                ]
            });
        }
        catch (error) {
            console.error("Authentication.AddUserFlexibleField");
            console.error(error);
            next(error);
        }
    });

export { router as addUserFlexibleFieldRouter };
