import { body } from "express-validator";

export const verifyCodeValidation = [
    body("code")
        .exists().bail()
        .isString().trim().blacklist("<>")
        .withMessage("Code parameter must be string."),

    body("codeVerifier")
        .optional()
        .isString().trim().blacklist("<>")
        .isLength({ min: 43, max: 43 }).withMessage("Code Verifier must be string and of max 43 characters long."),
];
