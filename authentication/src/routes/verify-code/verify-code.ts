// import {
//     ResourceUnauthorizedError,
//     responseHandler,
//     UserOrganizationDisabledError,
//     validateRequest
// } from "@moxfive-llc/common";
// import express, { NextFunction, Request, Response } from "express";
// import app from "../../app";
// import { GetUserDetailsResponse, VerifyCodeResponse } from "../../interfaces";
// import { Organization } from "../../models/organization";
// import { User } from "../../models/user";
// import { jsonWebToken } from "../../services/json-web-token";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { verifyCodeValidation } from "./verify-code.validation";
// import { MongoTransaction } from "../../services/mongo-transaction";
// import { UserV2 } from "../../models/v2/users-v2";

// const router = express.Router();

// router.post(
//     "/v1/verifyCode",
//     (req: Request, res: Response, next: NextFunction) => {
//         // CASE: Referer is setting as blank string as in the referer code is exposed and in the logs we can see the logs
//         req.headers.referer = req.headers.referer?.split("?")[0] ?? "";
//         next();
//     },
//     responseHandler,
//     verifyCodeValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         // Start Mongo Transaction
//         const mongoTransaction = new MongoTransaction();
//         mongoTransaction.startTransaction();

//         try {
//             req.action = "GetAccessToken";

//             const { code, codeVerifier } = req.body;

//             // Step: 1 Call azure API to verify code and get user details
//             const authenticationResp: VerifyCodeResponse = await microsoftGraphAPI.verifyCodeAndGetDetails({ code, codeVerifier });
//             const userAzureDetails: GetUserDetailsResponse = await microsoftGraphAPI.getUserDetails({ accessToken: authenticationResp.data.access_token });

//             // Step: 2 Get org details of a user
//             const orgDetails = await Organization.findOne({
//                 $or: [
//                     { owner: userAzureDetails.data.id },
//                     { member: userAzureDetails.data.id }
//                 ]
//             }).session(mongoTransaction.session).lean().exec();

//             if (!orgDetails) {
//                 throw new ResourceUnauthorizedError();
//             }
//             if (!orgDetails.isEnabled) {
//                 throw new UserOrganizationDisabledError();
//             }

//             // Step: 3 Find the user from our DB, if its not exist then throw error
//             const userDetails = await UserV2.findOne({ azureId: userAzureDetails.data.id }).session(mongoTransaction.session);
//             let userId;
//             let userEmail;
//             if (userDetails) {
//                 userId = userDetails._id;
//                 userEmail = userDetails.email;

//                 const lastSignIn = new Date().toISOString();
//                 await UserV2.findByIdAndUpdate(userId, { lastSignIn }, { session: mongoTransaction.session });
//                 await User.findByIdAndUpdate(userId, { lastSignIn }, { session: mongoTransaction.session });
//             }
//             else {
//                 throw new ResourceUnauthorizedError();
//             }

//             // Commit transaction
//             await mongoTransaction.commitTransaction();

//             // Step: 4 Generate JWT access token and refresh token
//             const { accessToken } = await jsonWebToken.getAccessToken({
//                 id: userId as string,
//                 email: userEmail as string,
//                 organizationId: String(orgDetails._id),
//                 accessToken: authenticationResp.data.access_token
//             });
//             const { refreshToken } = await jsonWebToken.getRefreshToken({
//                 refreshToken: authenticationResp.data.refresh_token,
//                 pkceEnabled: !!codeVerifier
//             });

//             // Step: Set cookies
//             res.cookie("Token1", accessToken, app.locals.cookieOptions);
//             res.cookie("Token2", refreshToken, app.locals.cookieOptions);

//             // res.cookie("Token1A", accessToken, app.locals.cookieOptions1);
//             // res.cookie("Token2B", refreshToken, app.locals.cookieOptions1);

//             res.sendResponse({
//                 meta: {
//                     message: "User authenticated successfully"
//                 }
//             }, {});
//         }
//         catch (error) {
//             // Abort transaction
//             await mongoTransaction.abortTransaction();

//             console.error("Authentication.VerifyCode");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as verifyCodeRouter };
