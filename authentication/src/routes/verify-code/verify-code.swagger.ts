/**
 * @swagger
 * /v1/verifyCode:
 *   post:
 *     name: Verify code
 *     summary: Verify code
 *     description: This API will verify code and set authentication tokens in cookies.
 *     tags:
 *       - Credentials
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *           example:
 *             { "code": "0.AXIAg8pgUk95NkKUCTLkTG1ngQfo0c9cLHxEpXKjSqjOzYJyAJ8.AQABAAIAAAD--DLA3VO7QrddgJ" }
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               {meta: {"message": "User authenticated successfully" }}
 *         headers:
 *           Set-Cookie:
 *             schema:
 *               type: string
 *               example: Token1=abcde12345; Path=/; HttpOnly
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Please provide code in body!",
 *                          "field": "code"
 *                      },
 *                      {
 *                          "message": "Code value must be string!",
 *                          "field": "code"
 *                      }
 *                  ]
 *              }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "User not found in our application."} ] }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Something went wrong"
 *                      }
 *                  ]
 *              }
 */
