import { currentUser, requireAuth, responseHand<PERSON> } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";

const router = express.Router();

router.get(
    "/v1/session/validate",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            res.sendResponse("", {});
        }
        catch (error) {
            next(error);
        }
    }
);
export { router as validateSessionRouter };
