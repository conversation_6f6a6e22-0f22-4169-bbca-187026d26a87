/**
 * @swagger
 * /v1/session/validate:
 *   post:
 *     name: Validate the session
 *     summary: Validate the session
 *     description: This API validate the session and return 200 if session is valid
 *     tags:
 *       - Credentials
 *     responses:
 *       200: #response type
 *         description: OK
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 */
