// import express, { NextFunction, Request, Response } from "express";
// // import { BadRequestError, currentUser, hasGlo<PERSON><PERSON>ction, InternalServerError, NotAuthorizedError, NotFoundError, requireAuth, validateRequest } from "@moxfive-llc/common";
// // import { User } from "../../models/user";
// // import { Organization } from "../../models/organization";
// // import { GetUsersListResponse, IdBatchRequestStatus, UserAzureDetails } from "../../interfaces";
// // import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// // import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
// // import { InviteMultipleUsersResponse } from "../../interfaces/invite-multiple-users-response";
// // import { addOrganizationOwnerByEmailsValidation } from "./add-organization-owner-by-emails.validation";

// const router = express.Router();

// router.post("/v1/organizations/:organizationId/owners/emails",
//     // currentUser,
//     // requireAuth,
//     // addOrganizationOwnerByEmailsValidation,
//     // validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // // Check user has permission to add owners of organization
//             // const hasPermission = await hasGlobalAction(req, "AddOwnerToOrganizationUsingEmail");
//             // if (!hasPermission) {
//             //   throw new NotAuthorizedError();
//             // }

//             // const { emails, invitationMessage }: { emails: string[], invitationMessage: string | undefined } = req.body;
//             // const { organizationId } = req.params;

//             // const owners: UserAzureDetails[] = [];
//             // const usersToBeInvited: string[] = [];
//             // const notInsertedOwners: string[] = [];

//             // // Step 1: Find organization and if it's not present then throw NotFoundError
//             // const organization = await Organization.findById(organizationId);
//             // if (!organization) {
//             //   throw new NotFoundError("Organization not found");
//             // }

//             // // Step 2: Search users in AD
//             // const accessToken = await microsoftGraphAPI.getAccessToken();
//             // const usersList: GetUsersListResponse = await microsoftGraphAPI.searchUsersByMultipleEmails({ accessToken, emails });
//             // emails.forEach(email => {
//             //   const user = usersList.data.value.find((user) => user.mail === email);
//             //   if (!user) {
//             //     usersToBeInvited.push(email);
//             //   } else {
//             //     owners.push({ id: user.id, mail: user.mail });
//             //   }
//             // });

//             // // Step 3: Invite new users
//             // if (usersToBeInvited.length > 0) {
//             //   const inviteMultipleUsersResponse: InviteMultipleUsersResponse[] = await microsoftGraphAPI.inviteMultipleUsers(
//             //     {
//             //       token: accessToken,
//             //       emails: usersToBeInvited,
//             //       invitationMessage
//             //     });
//             //   inviteMultipleUsersResponse.forEach(user => {
//             //     if (!user.status) {
//             //       notInsertedOwners.push(user.email);
//             //     } else {
//             //       owners.push({ id: user.id, mail: user.email });
//             //     }
//             //   });

//             //   // Throw error if no owners present to insert
//             //   if (!owners.length) {
//             //     throw new InternalServerError();
//             //   }
//             // }

//             // // Step 2: Find which members are part of same org and which aren't
//             // const usersInSameOrgAsMember: string[] = [];
//             // const usersNotInSameOrg: string[] = [];

//             // owners.forEach(owner => {
//             //   if (!organization.owner.includes(owner.id)) {
//             //     if (organization.member.includes(owner.id)) {
//             //       usersInSameOrgAsMember.push(owner.id);
//             //     } else {
//             //       usersNotInSameOrg.push(owner.id);
//             //     }
//             //   }
//             // });

//             // if (usersInSameOrgAsMember.length !== 0 || usersNotInSameOrg.length !== 0) {
//             //   // Step 3: Check provided owners are already associated with other Org as owner or member
//             //   if (usersNotInSameOrg.length > 0) {
//             //     const existingUser = await Organization.findUserAlreadyInAnyOrganization(usersNotInSameOrg);

//             //     if (existingUser) {
//             //       throw new BadRequestError("Please make sure provided owners must not added as either user or owner");
//             //     }
//             //   }

//             //   // Step 4: Get the authorization token
//             //   const token = await microsoftGraphAPI.getAccessToken();

//             //   // Step 5 : Add owners in the group microsoft azure
//             //   const addOwnersResponse: IdBatchRequestStatus = await microsoftGraphAPI.addOwnersInOrganization(
//             //     {
//             //       token,
//             //       orgId: organization.azureId,
//             //       owners: [...usersInSameOrgAsMember, ...usersNotInSameOrg]
//             //     });

//             //   // Step 7: Filter out inserted owners and not inserted owners
//             //   const usersExistInSameOrg = new Set(usersInSameOrgAsMember);

//             //   const insertedOwners: UserAzureDetails[] = [];
//             //   const insertedExistingMembers: string[] = [];

//             //   owners.forEach(owner => {
//             //     if (addOwnersResponse.hasOwnProperty(owner.id)) {
//             //       // eslint-disable-next-line no-nested-ternary
//             //       addOwnersResponse[owner.id] ?
//             //         usersExistInSameOrg.has(owner.id) ?
//             //           insertedExistingMembers.push(owner.id) :
//             //           insertedOwners.push(owner)
//             //         : notInsertedOwners.push(owner.mail);
//             //     }
//             //   });

//             //   // If no owner is added then throw internal server error
//             //   if (insertedOwners.length === 0 && insertedExistingMembers.length === 0) {
//             //     throw new InternalServerError();
//             //   }

//             //   const insertedOwnersIds = insertedOwners.map(owner => owner.id);

//             //   // Step 8 : Add users in group DB
//             //   organization.owner = organization.owner.concat([...insertedOwnersIds, ...insertedExistingMembers]);
//             //   await organization.save();
//             //   await OrganizationUpdatedPublisherWrapper(organization);

//             //   // Step 9: Insert users if not exist
//             //   await User.insertUsersIfNotExist(insertedOwners, organization.id);
//             // }

//             // // Step 10: If some users are not inserted then throw internal server error with email ids which are not inserted
//             // if (notInsertedOwners.length > 0) {
//             //   throw new InternalServerError(`${notInsertedOwners} didn't inserted, please try again`);
//             // }

//             // // Step 11: Send Response
//             // res.json({
//             //   meta: {
//             //     message: "Organization owners added successfully"
//             //   }
//             // });
//             res.json({});
//         }
//         catch (error) {
//             console.error("Authentication.AddOrganizationOwnersByEmails");
//             console.error(error);
//             next(error);
//         }
//     });

// export { router as addOrganizationOwnersByEmailsRouter };
