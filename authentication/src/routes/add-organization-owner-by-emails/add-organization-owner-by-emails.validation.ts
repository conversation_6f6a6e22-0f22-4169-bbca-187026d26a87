// import { body, param } from "express-validator";

// export const addOrganizationOwnerByEmailsValidation = [
//     param("organizationId")
//         .exists().withMessage("Organization id must be exist").bail()
//         .isMongoId().withMessage("Organization id must be valid"),

//     body("emails")
//         .isArray({ min: 1, max: 20 }).withMessage("Emails must be valid array with min 1 and max 20 elements"),

//     body("emails.*")
//         .exists().withMessage("Owner email must be valid").bail()
//         .isEmail().normalizeEmail().withMessage("Owner email must be valid"),

//     body("invitationMessage")
//         .optional()
//         .isString().trim().blacklist("<>").withMessage("invitationMessage must be a string").bail()
//         .isLength({ max: 500 }).withMessage("invitationMessage must be max 500 characters long")
// ];
