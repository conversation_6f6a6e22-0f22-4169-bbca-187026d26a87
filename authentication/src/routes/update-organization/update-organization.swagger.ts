/**
 * @swagger
 * /v1/organizations/{organizationId}:
 *   post:
 *     name: Update Organization.
 *     summary: Update Organization.
 *     description: this will Update Organization.
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *           example:
 *            {
 *                "name": "Organization Name",
 *                "description": "Description",
 *                "billingAddresses": [{
 *                    "addressline1": "2613 Jett Lane",
 *                    "addressline2": "Opposite white house",
 *                    "state": "California",
 *                    "city": "Los Angeles",
 *                    "zip": "90017",
 *                    "country": "United States",
 *                    "contact": "+13107546214"
 *                }]
 *            }
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                    "meta": {
 *                        "message": "Organization updated successfully"
 *                    }
 *                }
 *       204: #response type
 *         description: No Content
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization not found"
 *                      }
 *                  ]
 *              }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Name should only contians characters, numbers, space, dot(.), hyphen(-)",
 *                          "field": "name"
 *                      }
 *                  ]
 *              }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Something went wrong"
 *                      }
 *                  ]
 *              }
 */
