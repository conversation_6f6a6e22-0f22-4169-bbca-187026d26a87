// /* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
// import express, { NextFunction, Request, Response } from "express";
// import {
//     currentUser,
//     ExternalServerError,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     InvalidActionError,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     responseHandler,
//     SucceededPartially,
//     TargetType,
//     validateRequest
// } from "@moxfive-llc/common";
// import { User, UserDoc } from "../../models/user";
// import { Organization } from "../../models/organization";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
// import { removeOrganizationMembersValidation } from "./remove-organization-members.validation";
// import { getUserName } from "../../util";

// const router = express.Router();

// router.delete("/v1/organizations/:organizationId/members",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     removeOrganizationMembersValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // Check user has permission to remove members from organization
//             const hasPermission = await hasGlobalAction(req, "RemoveMembersOfOrganization");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { members }: { members: string[] } = req.body;
//             const { organizationId } = req.params;

//             if (members.includes(req.currentUser?.id!)) {
//                 throw new InvalidActionError("You cannot remove yourself from the organization.");
//             }

//             // Step 1: Find organization and if it's not present then throw NotFoundError, if not enabled then throw BadRequestError
//             const organization = await Organization.findById(organizationId);

//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }

//             // Step 2: Fetch user details from user ids, if any id is not valid throw new error
//             const usersDetails = await User.find({
//                 _id: { $in: members }
//             });
//             if (!usersDetails || (usersDetails && !usersDetails.length)) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
//             }

//             // Prepare user details map
//             const userDetailsMap = new Map();
//             const userIds: string[] = [];

//             usersDetails.forEach(user => {
//                 userIds.push(String(user._id));

//                 userDetailsMap.set(String(user.azureId), {
//                     id: String(user._id),
//                     name: getUserName({
//                         firstName: user.firstName,
//                         lastName: user.lastName,
//                         displayName: user.displayName,
//                     }),
//                     email: user.email,
//                     azureId: user.azureId
//                 });
//             });

//             const validUsers: UserDoc[] = [];
//             const inValidUserIds: string[] = [];

//             members.forEach(userId => {
//                 if (userIds.includes(userId)) {
//                     validUsers.push(usersDetails.find(user => user.id === userId)!);
//                 }
//                 else {
//                     inValidUserIds.push(userId);
//                 }
//             });

//             // Make azure Ids
//             const userAzureIds = validUsers.map(user => {
//                 return user.azureId;
//             });

//             // Step 3: Check whether this members are there as memebers in that org
//             const org = await Organization.findOne({
//                 _id: organizationId,
//                 member: { $all: userAzureIds }
//             }).lean().exec();

//             if (!org) {
//                 // throw new BadRequestError("All the members provided are not in organization");
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
//             }

//             // Step 4: Get the authorization token
//             const token = await microsoftGraphAPI.getAccessToken();

//             // Step 5 : Remove members from organization microsoft azure
//             const removeUsersResponse = await microsoftGraphAPI.removeUsersFromOrganization({ token, orgId: organization.azureId, users: userAzureIds });

//             // Step 6:  Filter out removed members azureIds and not removed members
//             const removedUserAzureIds: string[] = [];
//             const notRemovedUsers: any = [];

//             validUsers.forEach(user => {
//                 if (removeUsersResponse[user.azureId].status) {
//                     removedUserAzureIds.push(user.azureId);
//                 }
//                 else {
//                     notRemovedUsers.push(removeUsersResponse[user.azureId].body?.error!);
//                 }
//             });

//             // if (removedUserAzureIds.length === 0) {
//             //   throw new InternalServerError("Something went wrong...please try again");
//             // }
//             if (notRemovedUsers.length === members.length) {
//                 throw new ExternalServerError(notRemovedUsers);
//             }

//             // Step 7 : Remove members from organization
//             organization.member = organization.member.filter(member => {
//                 return !removedUserAzureIds.includes(member);
//             });

//             await organization.save();
//             await OrganizationUpdatedPublisherWrapper(organization);

//             await Promise.all(removedUserAzureIds.map(async azureId => {
//                 const user = await User.getUserByAzureId({ azureId });
//                 if (user) {
//                     // const data = {
//                     //     id: user.id
//                     // };
//                     await user.deleteOne();
//                     // await userDeletedPublisherWrapper(data);
//                 }
//             }));

//             // Step 8: If some members are not removed then throw internal server error
//             const errors = [];
//             if (inValidUserIds.length) {
//                 errors.push({
//                     attributes: inValidUserIds,
//                     message: "Users do not exist."
//                 });
//             }

//             if (notRemovedUsers.length) {
//                 notRemovedUsers.forEach((err: any) => {
//                     errors.push(err);
//                 });
//             }

//             if (errors.length) {
//                 throw new SucceededPartially([{
//                     parameters: errors
//                 }], "One or more members are failed to remove from the specified organization.");
//             }

//             // Step 9: Send response
//             res.sendResponse({
//                 meta: {
//                     message: "Organization members removed successfully"
//                 }
//             }, {
//                 targets: removedUserAzureIds.map(azureId => {
//                     const user = userDetailsMap.get(azureId);

//                     return {
//                         type: TargetType.USER,
//                         details: user || {}
//                     };
//                 }),
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: organizationId
//             });
//         }
//         catch (error) {
//             console.error("Authentication.RemoveOrganizationMembers");
//             console.error(error);
//             next(error);
//         }
//     });

// export { router as removeOrganizationMembersRouter };
