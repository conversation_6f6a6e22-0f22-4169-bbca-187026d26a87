import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../util";

export const removeOrganizationMembersValidation = [
    param("organizationId")
        .exists().bail()
        .isMongoId(),

    body("members")
        .isArray({ min: 1, max: 20 })
        .withMessage("At a given time max 20 members can be removed from an organization.").bail()
        .custom((members: string[]) => {
            return members.every(member => {
                return isValidMongoObjectId(member);
            });
        }).withMessage("These user ids are invalid.")
];
