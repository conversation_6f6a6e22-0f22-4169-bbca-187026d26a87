/**
 * @swagger
 * /v1/organizations/{organizationId}/members:
 *   delete:
 *     summary: Remove users from organization
 *     description: Remove users from organization
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                users:
 *                  type: array
 *                  items:
 *                    type: string
 *           example:
 *            {
 *                "members": ["6239ff4af2cd78120fa94d47", "6239ff4af2cd78120fa94d48"]
 *            }
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                    meta: {
 *                        message: "Organization members removed successfully"
 *                    }
 *                }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Members must be valid array with min 1 and max 20 elements", "field": "members"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"},
 *                     ]
 *                   }
 *               Third:
 *                 summary: Invalid user Ids
 *                 description: Invalid user Ids
 *                 value:
 *                   { "errors": [ { "message": "Provide valid member Ids"} ] }
 *               Fourth:
 *                 summary: All provided members not in organization
 *                 description: All the members provided are not in organization
 *                 value:
 *                   { "errors": [ { "message": "All the members provided are not in organization"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization not found"
 *                      }
 *                  ]
 *              }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: None of members removed
 *                 description: None of members removed
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 *               Second:
 *                 summary: Partial members removed
 *                 description: Partial members removed
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "<EMAIL> didn't removed, please try again"
 *                          }
 *                      ]
 *                  }
 *               Third:
 *                 summary: Internal server error.
 *                 description: Internal server error.
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 */
