// import express, { NextFunction, Request, Response } from "express";
// import {
//     validateRequest,
//     currentUser,
//     requireAuth,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     ResourceNotFoundError,
//     NotFoundCode,
//     EmailAlreadyExistBadRequestError,
//     generateSearchKeys,
//     responseHandler,
//     TargetType
// } from "@moxfive-llc/common";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { User } from "../../models/user";
// import { Organization } from "../../models/organization";
// import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
// import { updateOrganizationSpecificUserEmailValidation } from "./update-organization-specific-user-email.validation";
// import { getSearchFields, getUserName } from "../../util";
//
// const router = express.Router();
//
// router.put(
//     "/v1/organizations/:organizationId/users/:userId/email",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     updateOrganizationSpecificUserEmailValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // Check user has permission to update user email of organization
//             const hasPermission = await hasGlobalAction(req, "UpdateUserEmailOfAnOrganization");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }
//
//             const { organizationId, userId } = req.params;
//             const { email } = req.body;
//
//             // Step 1: If organization not found then throw NotFoundError
//             const organization = await Organization.findById(organizationId).lean().exec();
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }
//
//             // Step 2: If user is not found then throw NotFoundError
//             const user = await User.findById(userId);
//             if (!user) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
//             }
//
//             // Step 3: Check user has been in the organization or not
//             const isMember = organization.owner.includes(user.azureId) || organization.member.includes(user.azureId);
//             if (!isMember) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
//             }
//
//             // Step 4: Check given email is used by any other user
//             const isUserExistWithEmail = await User.getUserByEmail({ email });
//             if (isUserExistWithEmail) {
//                 throw new EmailAlreadyExistBadRequestError();
//             }
//
//             // Step 4: Check email is same as current email and if it's same then throw BadRequestError
//             const oldEmail = user.email;
//
//             if (user.email === email) {
//                 return res.sendResponse({
//                     meta: {
//                         message: "User email updated successfully"
//                     }
//                 }, {});
//             }
//
//             // Step 5:  Update user email and reinvite in azure
//             const accessToken = await microsoftGraphAPI.getAccessToken();
//             await microsoftGraphAPI.updateUserEmail({ accessToken, azureId: user.azureId, email });
//             await microsoftGraphAPI.reinviteUser({ accessToken, azureId: user.azureId, email });
//
//             const searchFields = getSearchFields(user.firstName, user.lastName, user.displayName, email);
//             const searchKeys = generateSearchKeys(searchFields);
//             user.keys = searchKeys as string[];
//
//             // Step 6: Update email in the DB
//             user.email = email;
//             await user.save();
//             await userUpdatedPublisherWrapper(user);
//
//             // Step 7: Send response
//             res.sendResponse({
//                 meta: {
//                     message: "User email updated successfully"
//                 }
//             }, {
//                 targets: [
//                     {
//                         type: TargetType.USER,
//                         details: {
//                             id: userId,
//                             name: getUserName({
//                                 firstName: user.firstName,
//                                 lastName: user.lastName,
//                                 displayName: user.displayName
//                             }),
//                             email,
//                             azureId: user.azureId
//                         }
//                     }
//                 ],
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: organizationId,
//                 modifiedProperties: [
//                     {
//                         target: TargetType.USER,
//                         propertyName: "email",
//                         oldValue: oldEmail,
//                         newValue: email
//                     }
//                 ]
//             });
//         }
//         catch (error) {
//             console.error("Authentication.UpdateOrganizationSpecificUserEmail");
//             console.error(error);
//             next(error);
//         }
//     }
// );
//
// export { router as updateOrganizationSpecificUserEmailRouter };
