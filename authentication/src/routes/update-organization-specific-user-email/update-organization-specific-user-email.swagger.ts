/**
 * @swagger
 * /v1/organizations/{organizationId}/users/{userId}/email:
 *   put:
 *     summary: Update user email of organization
 *     description: Update user email of organization
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type : string
 *       - in: path
 *         name: userId
 *         required: true
 *         description: user id
 *         schema:
 *           type : string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateUserEmail'
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                  {
 *                    "meta": {
 *                      "message": "User email updated successfully"
 *                    }
 *                  }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       {"message": "email must be valid", "field": "email"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"},
 *                       { "message": "User id must be valid", "field": "userId"}
 *                     ]
 *                   }
 *               Third:
 *                 summary: User doesn't exist in the provided organization
 *                 description: User doesn't exist in the provided organization
 *                 value:
 *                   { "errors": [ { "message": "User doesn't exist in the provided organization"} ] }
 *               Fourth:
 *                 summary: Email is already in use
 *                 description: Email is already in use
 *                 value:
 *                   { "errors": [ { "message": "This email is already in use"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             examples:
 *                First:
 *                  summary: Organization not found
 *                  description: Organization not found
 *                  value:
 *                    { "errors": [ { "message": "Organization not found"} ] }
 *                Second:
 *                  summary: User not found
 *                  description: User not found
 *                  value:
 *                    { "errors": [ { "message": "User not found"} ] }
 *       204:
 *         description: No Content (Already same email)
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Something went wrong"} ] }
 */
