import { body, Meta } from "express-validator";
import { isValidMongoObjectId } from "../../util";

const isUserLocationExists = (_: any, meta: Meta) => {
    console.info("meta.req.body.userLocation", !!meta.req.body.userLocation);
    return !!meta.req.body.userLocation;
};

export const updateUserDetailsValidation = [
    body("firstName")
        .optional()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("First Name must be string and of max 64 characters long."),

    body("displayName")
        .optional()
        .isString().trim().blacklist("<>").isLength({ min: 1, max: 128 }).withMessage("Display Name must be string and of max 128 characters long."),

    body("lastName")
        .optional()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Last Name must be string and of max 64 characters long."),

    body("userLocation")
        .optional({ nullable: true })
        .isObject().withMessage("User location must be an object."),

    body("userLocation.addressline1")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 512 }).withMessage("User Locations addressline1 must be string and can be of max 512 characters long."),

    body("userLocation.addressline2")
        .if(isUserLocationExists)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 512 }).withMessage("User Locations addressline2 must be string and can be of max 512 characters long."),

    body("userLocation.country")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("User Location country can only contain letters, spaces, and can be of max 64 characters long."),

    body("userLocation.countryShortName")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("User Locations country short name can only contain letters and can be of max 64 characters long."),

    body("userLocation.state")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("User Locations state can only contain letters, spaces, and can be of max 64 characters long."),

    body("userLocation.stateShortName")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("User Locations state short name can only contain letters and can be of max 64 characters long."),

    body("userLocation.city")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("User Locations city can only contain letters, spaces, and can be of max 64 characters long."),

    body("userLocation.cityShortName")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("User Locations city short name can only contain letters and can be of max 64 characters long."),

    body("userLocation.zip")
        .if(isUserLocationExists)
        .exists().bail()
        .isString().trim()
        .isPostalCode("US").withMessage("Postal Code must be valid US zip."),

    body("userLocation.latitude")
        .if(isUserLocationExists)
        .exists().bail()
        .isFloat({ gt: -90.1, lt: 90.1 }).trim()
        .withMessage("User Locations latitude should be valid and range between -90 to 90 degrees"),

    body("userLocation.longitude")
        .if(isUserLocationExists)
        .exists().bail()
        .isFloat({ gt: -180.1, lt: 180.1 }).trim()
        .withMessage("User Locations longitude should be valid and range between -180 to 180 degrees"),

    body("officePhone")
        .optional({ nullable: true })
        .isString().trim()
        .isMobilePhone("en-US").withMessage("Office Phone must be valid US number."),

    body("jobTitle")
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Job Title must be string and can be of max 64 characters long."),

    body("role.id")
        .if(body("role").exists())
        .exists().bail()
        .isMongoId().withMessage("Role not found."),

    body("role.valueIds")
        .if(body("role").exists())
        .exists().bail()
        .isArray({ max: 1 }).withMessage("Role values must be valid array with max 1 element.").bail()
        .custom((valueIds: string[]) => {
            return valueIds.every(valueId => {
                return isValidMongoObjectId(valueId);
            });
        }).withMessage("Role values must be valid array with max 1 element."),
];
