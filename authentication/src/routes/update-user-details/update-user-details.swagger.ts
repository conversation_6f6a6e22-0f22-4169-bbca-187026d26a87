/**
 * @swagger
 * /v1/users/{userId}:
 *   put:
 *     name: Update user details
 *     summary: Update user details
 *     description: This API will update user details.
 *     tags:
 *       - Users
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: user id.
 *         schema:
 *           type : string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateUserDetails'
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                 {
 *                   meta: {
 *                     message: "User updated successfully"
 *                   }
 *                 }
 *       204:
 *         description: No Content (Nothing updated)
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "firstName must be string", "field": "firstName" } ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "User not found!" } ] }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Something went wrong" } ] }
 */
