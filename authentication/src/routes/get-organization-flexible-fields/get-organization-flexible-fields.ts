import express, { NextFunction, Request, Response } from "express";
import { currentUser, requireAuth, response<PERSON><PERSON><PERSON> } from "@moxfive-llc/common";

import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
import { sortByPropertyInObject } from "../../util/sort-by-property-in-object";

const router = express.Router();

router.get("/v1/organizations/fields",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "ListOrganizationFlexibleFields";

            const fieldsData = await OrganizationFlexibleField.find({ }, { name: 1, key: 1, values: 1 });
            const response = fieldsData.map(data => {
                return {
                    ...data.toJSON(),
                    values: data.values.sort(sortByPropertyInObject("value"))
                };
            });
            res.sendResponse(response, {});
        }
        catch (error) {
            console.error("Authentication.GetOrganizationFlexibleFields");
            console.error(error);
            next(error);
        }
    });

export { router as getOrganizationFlexibleFieldsRouter };
