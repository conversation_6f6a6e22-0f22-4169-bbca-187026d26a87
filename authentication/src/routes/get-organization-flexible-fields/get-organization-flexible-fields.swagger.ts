/**
 * @swagger
 * /v1/organizations/fields:
 *   get:
 *     name: Get Flexible Fields to Organization.
 *     summary: Get Flexible Fields to Organization.
 *     description: this will Return Flexible Fields to Organization.
 *     tags:
 *       - Organizations
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                [
 *                  {
 *                    "id": "624590e7bf392b108efb5d52",
 *                    "name": "Locations",
 *                    "values": [
 *                      {
 *                        "value": "Maine",
 *                        "id": "6245967921f3af86a64dedc2"
 *                      }
 *                    ],
 *                  }
 *                ]
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 */
