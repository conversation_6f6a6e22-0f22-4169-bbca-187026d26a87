import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import { organizationPathParamValidation } from "../../validations/general/organizaiton-path-params-validation";
import { Environment } from "../../models/organization-enviroment";
import { processResponseForFlexibleFields } from "../../util/process-response-for-flexible-fields";
import { authenticationFields } from "../../util/authentication-fields";
import { MakeSingleSectionFieldsFlexibleFields } from "../../util/make-single-section-fields-flexible-fields";
import { OrganizationV2 } from "../../models/v2/oragnizations-v2";

const { flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.environment.solutions);

const router = express.Router();

router.get("/v1/organizations/:organizationId/environment/solutions",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    organizationPathParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1:  Check if user has a ability
            const hasPermission = await hasGlobalAction(req, "GetOrganizationEnvironmentSolutionsDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Step 2: Check organization is valid or not
            const organization = await OrganizationV2.findById(organizationId).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3: Get organiztion organization
            const environment = await Environment.findOne({ organizationId }).lean().exec();
            if (!environment || !environment.itAndSecurityControls) {
                // TODO ERROR CODE FOR ORGANIZATION
                throw new ResourceNotFoundError(NotFoundCode.INCIDENT_SECTION_DETAILS_NOT_FOUND, "Environment IT and Security Controls details not found.");
            }

            // Step 4: Get the flexible field values
            const finalResp = await processResponseForFlexibleFields(flexibleFieldsNameKey, { ...environment.itAndSecurityControls });

            // Step 5: Send Response
            res.sendResponse(finalResp, {});

        }
        catch (error) {
            console.error("Authentication.GetEnvironmentSolutionsDetails");
            console.error(error);
            next(error);
        }
    });

export { router as getEnvironmentSolutionsRouter };
