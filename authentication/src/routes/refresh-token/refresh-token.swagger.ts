/**
 * @swagger
 * /v1/refreshToken:
 *   get:
 *     name: Refresh token
 *     summary: Refresh token
 *     description: This API will update access token of authenticated user and set it into cookies.
 *     tags:
 *       - Credentials
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *             example:
 *               { meta: { message: "Access token has been refreshed successfully"}}
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized" } ] }
 */
