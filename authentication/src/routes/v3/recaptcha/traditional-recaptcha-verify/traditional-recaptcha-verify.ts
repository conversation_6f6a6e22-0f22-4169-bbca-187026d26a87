import express, { NextFunction, Request } from "express";
import { InvalidCaptchaTokenError, responseHandler, validateRequest } from "@moxfive-llc/common";
import { reCaptchaValidation } from "../../../../validations/v3/recaptcha/recaptcha.validation";
import { ReCaptchaService } from "../../../../services/recaptcha";

const router = express.Router();

router.post(
    "/v3/authentication/recaptcha/traditional",
    responseHandler,
    reCaptchaValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            req.action = "ValidateReCaptchaV2";

            const { token } = req.body;
            const clientIP = req.ip || req.headers["x-forwarded-for"] as string || "";

            // Validate the reCAPTCHA token
            const validationResult = await ReCaptchaService.validateV2Token(token, clientIP);

            if (!validationResult.success) {
                throw new InvalidCaptchaTokenError(); //TODO: Change to a more specific error
            }

            return res.sendResponse({
                success: validationResult.success,
                hostname: validationResult.hostname,
                challengeTs: validationResult.challengeTs,
                errorCodes: validationResult.errorCodes
            });
        }
        catch (error) {
            console.error("Authentication.TraditionalRecaptchaVerify", error);
            next(error);
        }
    }
);

export { router as validateReCaptchaV2Router };
