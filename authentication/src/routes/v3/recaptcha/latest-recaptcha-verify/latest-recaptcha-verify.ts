import express, { NextFunction, Request } from "express";
import { InvalidCaptchaTokenError, responseHandler, validateRequest } from "@moxfive-llc/common";
import { reCaptchaValidation } from "../../../../validations/v3/recaptcha/recaptcha.validation";
import { ReCaptchaService } from "../../../../services/recaptcha";

const router = express.Router();

router.post(
    "/v3/authentication/recaptcha/latest",
    responseHandler,
    reCaptchaValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            req.action = "ValidateReCaptchaV3";

            const { token } = req.body;
            const clientIP = req.ip || req.headers["x-forwarded-for"] as string || "";

            // Validate the reCAPTCHA token
            const validationResult = await ReCaptchaService.validateV3Token(token, clientIP);

            if (!(validationResult && validationResult.success)) {
                throw new InvalidCaptchaTokenError(); //TODO: Change to a more specific error
            }

            console.info("ReCaptchaV3 Validation Result: ");
            console.info(JSON.stringify(validationResult, null, 2));
            console.info("-----------------------------------------------------------");

            if ((validationResult.score || 0) < 0.5) {
                throw new InvalidCaptchaTokenError();
            }

            return res.sendResponse({
                success: validationResult.success,
                score: validationResult.score,
                hostname: validationResult.hostname,
                challengeTs: validationResult.challengeTs,
                errorCodes: validationResult.errorCodes
            });
        }
        catch (error) {
            console.error("Authentication.LatestRecaptchaVerify", error);
            next(error);
        }
    }
);

export { router as validateReCaptchaV3Router };
