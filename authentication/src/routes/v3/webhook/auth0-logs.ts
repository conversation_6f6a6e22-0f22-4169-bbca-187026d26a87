import express, { Request, NextFunction } from "express";
import { responseHand<PERSON>, sanitizeIP } from "@moxfive-llc/common";
import { AuthLog, AuthLogAttrs } from "../../../models/auth-log.model";
import { UserV2, UserV2Doc } from "../../../models/v2/users-v2";
import { AUTH0_EVENTS } from "../../../util/v2/auth0-events";
import { isValidMongoObjectId } from "../../../util";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";

const router = express.Router();

// Create a Set of all tracked event codes for quick lookups
const REQUIRED_EVENT_TYPES = new Set(Object.keys(AUTH0_EVENTS));

// Webhook endpoint for Auth0 logs
router.post(
    "/v3/authentication/auth0/logs-webhook",
    responseHandler,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Extract log details from the request body
            req.action = "Auth0LogsWebhook";

            // Check if the authorization header is valid, if not then throw an error
            const authorizationHeader = req.headers.authorization;

            if((authorizationHeader ?? "").split(" ")[1] !== process.env.AUTH0_WEBHOOK_TOKEN) {
                return res.status(204).sendResponse("", {});
            }

            // Get logs from the request body - handle both direct and nested formats
            let logs = req.body.logs || [];

            // Check if logs have a 'data' property structure (nested format)
            const isNestedFormat = logs.length > 0 && logs[0].data;

            // Extract the actual log data if in nested format
            if (isNestedFormat) {
                logs = logs.map((item: any) => {
                    return {
                        ...item.data,
                        log_id: item.log_id || item.data.log_id
                    };
                });
            }

            if (!logs || !Array.isArray(logs) || logs.length === 0) {
                return res.status(204).sendResponse("", {});
            }

            // Process each log entry
            const logsToSave: Array<AuthLogAttrs> = [];

            // First, collect all emails to find users in a batch
            const userIds = logs
                .filter(log => log.user_id)
                .map(log => log.user_id);

            // Find all users in a single query
            const uniqueUserIds = [...new Set(userIds)];
            const users = await UserV2.find({ $or: [
                { externalAuth0LoginUserId: { $in: uniqueUserIds } },
                { externalIDPLoginUserId: { $in: uniqueUserIds } },
            ] }).lean().exec();

            // Create a map of email to user data for quick lookup
            const idToUserMap: Map<string, UserV2Doc> = new Map();
            users.forEach(user => {
                if(user.externalAuth0LoginUserId) {
                    idToUserMap.set(user.externalAuth0LoginUserId, user);
                }
                else if(user.externalIDPLoginUserId) {
                    idToUserMap.set(user.externalIDPLoginUserId, user);
                }
            });

            // Process only the required event types
            for (const log of logs) {
                // Extract necessary details
                const {
                    type,
                    description,
                    date,
                    client_name: clientName,
                    client_id: clientId,
                    ip,
                    user_agent: userAgent,
                    user_id: userId,
                    details,
                    connection,
                    connection_id: connectionId,
                    hostname,
                    log_id: logId,
                    _id: id
                } = log;

                // Skip if no user_id or user_name (might be system logs)
                if (!userId) {
                    continue;
                }

                // Skip if not one of our required event types
                if (!REQUIRED_EVENT_TYPES.has(type)) {
                    continue;
                }

                // Get user from our map instead of querying DB in a loop
                const user = idToUserMap.get(userId);

                // Get event information from our mapping
                const eventInfo = AUTH0_EVENTS[String(type)] || { name: type, category: "authentication", isFailure: false };

                let organization = user ? user.organization : null;
                if(!organization && connection && isValidMongoObjectId(connection)) {
                    // eslint-disable-next-line no-await-in-loop
                    const organizationInfo = await OrganizationV2.findById(String(connection), {
                        name: 1
                    }).lean().exec();

                    if(organizationInfo) {
                        organization = {
                            id: String(organizationInfo._id),
                            value: organizationInfo.name
                        };
                    }
                }

                // Create the auth log entry
                const authLog = AuthLog.build({
                    type: eventInfo.name,
                    eventCode: type,
                    user: user ? {
                        id: String(user._id),
                        value: user.name
                    } : null,
                    email: user?.email ?? "",
                    ipAddress: ip ? sanitizeIP(ip) as string : "",
                    userAgent: userAgent ?? null,
                    timestamp: new Date(date),
                    details: {
                        ...details,
                        clientName,
                        clientId,
                        logId: logId || id,
                        connection,
                        connectionId,
                        hostname,
                        category: eventInfo.category,
                        rawLog: log
                    },
                    isFailure: eventInfo.isFailure,
                    description: description ?? null,
                    organization: user ? user.organization : null
                });

                logsToSave.push(authLog);
            }

            // Save all logs at once instead of in a loop
            if (logsToSave.length > 0) {
                await AuthLog.insertMany(logsToSave);
            }

            // Return success response
            return res.status(204).sendResponse("", {});
        }
        catch (error) {
            console.error("Authentication.Auth0LogsWebhook");
            console.error(error);
            next(error);
        }
    }
);

export { router as auth0LogsWebhookRouter };
