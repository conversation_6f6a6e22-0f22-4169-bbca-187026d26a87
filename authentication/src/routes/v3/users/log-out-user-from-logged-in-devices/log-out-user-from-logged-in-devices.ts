import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, InsufficientPrivilagesError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler, TargetType, validateRequest } from "@moxfive-llc/common";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { userPathParamValidation } from "../../../../validations/general/user-path-params-validation";
import { LogoutUserFromLoggedinDevicesValidation } from "../../../../validations/v3/general/logout-user-from-logged-in-devices.validation";

const router = express.Router();

router.put(
    "/v3/authentication/users/:userId/devices/logout",
    responseHandler,
    currentUser,
    requireAuth,
    userPathParamValidation,
    LogoutUserFromLoggedinDevicesValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 2: Check whether user has permission or not
            req.action = "LogoutUserFromLoggedinDevices";

            // Step 3: Check whether user is present or not, if not then throw an error
            const { userId } = req.params;

            // If provided userId is not same as logged in user then throw an error
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            const { signOutFromAllDevices, devices }: {
                signOutFromAllDevices?: boolean,
                devices?: string[]
            } = req.body;

            const user = await UserV2.findById(userId).session(mongoTransaction.session);

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            let isUpdate = false;
            const oldDevices = [...(user.toJSON()).devices];
            // Step 4: If user selects sign out from all Devices then clear all devices otherwise remove specified devices
            if (signOutFromAllDevices) {
                UserServiceV3.destroyAllUserActiveSessions(user);

                isUpdate = true;
            }
            else {
                // Remove that device from devices and allowedLoginTokens
                const devicesSet = new Set(devices);

                // Check if there are any devices that needs to be removed
                isUpdate = user.devices.some(device => devicesSet.has(String(device.id)));

                // If isUpdate is true then remove entry from devices and allowedLoginTokens
                if (isUpdate) {
                    user.devices = user.devices.filter(device => {
                        return !devicesSet.has(String(device.id));
                    });

                    user.allowedLoginTokens = user.allowedLoginTokens.filter(token => {
                        return !devicesSet.has(String(token));
                    }) as string[];
                }
            }

            // Step 4: If there is any update then save details and sync
            if (isUpdate) {
                // Save user details, sync details to v1
                await user.save({ session: mongoTransaction.session });

                // Add user entry in v1 as well
                await userV2ToV1Sync({
                    user: user.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });
            }

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // If there is any update then publish NATS Event
            if (isUpdate) {
                await userUpdatedPublisherV2Wrapper(user);
            }

            // Step 5: Send response
            return res.sendResponse({
                meta: {
                    message: "User signed out from selected devices."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "devices",
                    oldValue: JSON.stringify(oldDevices),
                    newValue: JSON.stringify(user.devices)
                }]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.LogoutUserFromLoggedinDevices");
            console.error(error);
            next(error);
        }
    }
);

export { router as logoutUserFromLoggedinDevicesV3Router };
