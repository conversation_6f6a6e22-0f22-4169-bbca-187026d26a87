import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import {
    currentUser, InsufficientPrivilagesError, InternalServerError, InvalidActionError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { userPathParamValidation } from "../../../../validations/general/user-path-params-validation";
import { aesDecrypt } from "../../../../util/aes-decrypt";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { UpdateUserPasswordValidation } from "../../../../validations/v3/users/update-user-password.validation";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { PasswordChangeSuccessNotificationPublisher } from "../../../../events/publishers/password-change-success-notification";
import { queueGroupName } from "../../../../events/queue-group-name";
import { natsWrapper } from "../../../../nats-wrapper";

const router = express.Router();

router.put(
    "/v3/authentication/users/:userId/password",
    responseHandler,
    currentUser,
    requireAuth,
    userPathParamValidation,
    UpdateUserPasswordValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "UpdateUserPassword";

            // Step 2: Check user exist or not, if not then throw an error. Also if user is not from Auth0 then also throw an error
            const { userId } = req.params;

            // If provided userId is not same as logged in user then throw an error
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            const user = await UserV2.findById(userId).session(mongoTransaction.session);

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            if (!user.externalAuth0LoginUserId) {
                throw new InvalidActionError("Your account is not registered in Auth0. If you believe this is an error, <NAME_EMAIL>.");
            }

            // Step 3: Fetch password and decrypt the password
            const { password, token }: { password: string, token: string } = req.body;

            const decryptedPassword = aesDecrypt(password, process.env.RESPONSE_ENCRYPTION_KEY!);
            if (!decryptedPassword) {
                throw new InternalServerError();
            }

            // Step 4: Check whether token is verified or not
            const { tokenExpired } = await UserServiceV3.verifyToken({
                userId: String(user._id),
                user,
                token,
                type: TokenTypesEnum.ACTION_TOKEN,
                tokenExpiredError: false,
                encrypt: true
            });

            if(tokenExpired) {
                throw new InsufficientPrivilagesError();
            }

            // Step 5: Update user's password
            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            await auth0Service.updateUserDetails({
                accessToken,
                userId: user.externalAuth0LoginUserId,
                data: {
                    password: decryptedPassword
                }
            });

            const devices = [...(user.toJSON()).devices];

            // Step 6: Save user details, sync details to v1
            UserServiceV3.destroyAllUserActiveSessions(user);
            await user.save({ session: mongoTransaction.session });

            // Add user entry in v1 as well
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // If there is any update then publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            try {
                await new PasswordChangeSuccessNotificationPublisher(natsWrapper.client).publish({
                    name: user.name,
                    email: user.email,
                    serviceName: queueGroupName
                });
            }
            catch(error) {
                console.error("Error in sending an email", error);
            }

            // Step 7: Send response
            return res.sendResponse({
                "meta": {
                    "message": "User password updated successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "devices",
                    oldValue: JSON.stringify(devices),
                    newValue: JSON.stringify(user.devices)
                }]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateUserPasswordV3");
            console.error(error);
            next(error);
        }
    }
);

export { router as updateUserPasswordV3Router };
