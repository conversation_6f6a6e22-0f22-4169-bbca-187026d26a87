
import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import {
    currentUser, InsufficientPrivilagesError, MissingParametersError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler,
    TargetType, validateRequest
} from "@moxfive-llc/common";
import { userPathParamValidation } from "../../../../validations/general/user-path-params-validation";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { UpdateUserEmailValidation } from "../../../../validations/v3/users/update-user-email.validation";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { EmailChangeNotificationPublisher } from "../../../../events/publishers/email-change-notification";
import { natsWrapper } from "../../../../nats-wrapper";
import { queueGroupName } from "../../../../events/queue-group-name";
import { WelcomeEmailNotificationPublisher } from "../../../../events/publishers/welcome-email-notification";

const router = express.Router();

router.put(
    "/v3/authentication/users/:userId/email",
    responseHandler,
    currentUser,
    requireAuth,
    userPathParamValidation,
    UpdateUserEmailValidation,
    validateRequest,

    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "UpdateUserEmail";

            // Step 2: Check user exist or not, if not then throw an error.
            const { userId } = req.params;

            // If provided userId is not same as logged in user then throw an error
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            const user = await UserV2.findById(userId).session(mongoTransaction.session);

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            const { email, token }: { email: string, token: string | null } = req.body;

            // If email is same as current one then send response
            if(user.email === email) {
                // Commit transaction
                await mongoTransaction.commitTransaction();

                return res.sendResponse({
                    "meta": {
                        "message": "User email updated successfully."
                    }
                }, {});
            }
            const oldEmail = user.email;

            // Step 3: If user is part of Auth0 then check whether token is valid or not
            if(user.externalAuth0LoginUserId) {
                // If token is not passed then throw an error
                if (!/^.{2,}$/.test(String(token || ""))) {
                    throw new MissingParametersError(["token"]);
                }

                // Chcek token is valid or not, if it is expired then throw an error
                const { tokenExpired } = await UserServiceV3.verifyToken({
                    userId: String(user._id),
                    user,
                    token: token ?? "",
                    type: TokenTypesEnum.ACTION_TOKEN,
                    tokenExpiredError: false,
                    encrypt: true
                });

                if(tokenExpired) {
                    throw new InsufficientPrivilagesError();
                }
            }

            const devices = [...(user.toJSON()).devices];

            // Step 4: Update user email address
            const { url }  = await UserServiceV3.changeUserEmail({ user, email, req });

            // Step 5: Save user details, sync details to v1
            await user.save({ session: mongoTransaction.session });

            // Add user entry in v1 as well
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // If there is any update then publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            // Send email
            try {
                if(user.isEmailVerified) {
                    await new EmailChangeNotificationPublisher(natsWrapper.client).publish({
                        name: user.name,
                        oldEmail: oldEmail,
                        newEmail: email,
                        url,
                        serviceName: queueGroupName
                    });
                }
                else {
                    await new WelcomeEmailNotificationPublisher(natsWrapper.client).publish({
                        email,
                        name: user.name,
                        url: url ?? "",
                        setPassword: Boolean(user.externalAuth0LoginUserId),
                        serviceName: queueGroupName,
                    });
                }
            }
            catch(error) {
                console.error("Error in sending an email", error);
            }

            // Step 6: Send response
            return res.sendResponse({
                "meta": {
                    "message": "User email updated successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "email",
                    oldValue: JSON.stringify(oldEmail),
                    newValue: JSON.stringify(email)
                }, {
                    target: TargetType.USER,
                    propertyName: "devices",
                    oldValue: JSON.stringify(devices),
                    newValue: JSON.stringify(user.devices)
                }]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateUserEmailV3");
            console.error(error);
            next(error);
        }
    }
);

export { router as updateUserEmailV3Router };
