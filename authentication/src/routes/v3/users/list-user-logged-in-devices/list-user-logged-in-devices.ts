import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, InsufficientPrivilagesError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler, validateRequest } from "@moxfive-llc/common";
import { userPathParamValidation } from "../../../../validations/general/user-path-params-validation";

const router = express.Router();

router.get(
    "/v3/authentication/users/:userId/devices",
    responseHandler,
    currentUser,
    requireAuth,
    userPathParamValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Check Action
            req.action = "ListUserLoggedinDevices";

            // Step 2: Fetch User Devices
            const { userId } = req.params;

            // If provided userId is not same as logged in user then throw an error
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            const user = await UserV2.findById(userId, {
                "devices.id": 1, "devices.device": 1, "devices.loginDateTime": 1,
            }).lean().exec();

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Step 4: In the devices list add isCurrentDevice flag and send response
            return res.sendResponse({
                totalRows: user.devices.length,
                data: user.devices.map(device => {
                    return {
                        ...device,
                        isCurrentDevice: String(req.currentUser?.deviceId) === String(device.id)
                    };
                })
            }, {});
        }
        catch (error) {
            console.error("Authentication.ListUserLoggedinDevices");
            console.error(error);
            next(error);
        }
    }
);

export { router as listUserLoggedinDevicesV3Router };
