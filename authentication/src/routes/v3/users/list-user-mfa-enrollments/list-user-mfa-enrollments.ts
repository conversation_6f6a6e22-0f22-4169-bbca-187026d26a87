import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, InsufficientPrivilagesError, InvalidActionError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler,
    validateRequest } from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { userPathParamValidation } from "../../../../validations/general/user-path-params-validation";

const router = express.Router();

router.get(
    "/v3/authentication/users/:userId/mfa/enrollments",
    responseHandler,
    currentUser,
    requireAuth,
    userPathParamValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Check Action
            req.action = "ListUserMFAEnrollments";

            // Step 2: Check user exist or not, if not then throw an error. Also if user is not from Auth0 then also throw an error
            const { userId } = req.params;

            // If provided userId is not same as logged in user then throw an error
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            const user = await UserV2.findById(userId, {
                externalAuth0LoginUserId: 1
            }).lean().exec();

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            if (!user.externalAuth0LoginUserId) {
                throw new InvalidActionError("Your account is not registered in Auth0. If you believe this is an error, <NAME_EMAIL>.");
            }

            // Step 3:Fetch user MFA enrollments
            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            const mfaEnrollments = await auth0Service.listUserMFAEnrollments({
                accessToken,
                userId: user.externalAuth0LoginUserId
            });

            // Step 4: Send response
            return res.sendResponse(mfaEnrollments, {});
        }
        catch (error) {
            console.error("Authentication.ListUserMFAEnrollmentsV3");
            console.error(error);
            next(error);
        }
    }
);

export { router as listUserMFAEnrollmentsV3Router };
