import express, { Request, NextFunction } from "express";
import { currentUser, InternalServerError, requireAuth, responseHand<PERSON> } from "@moxfive-llc/common";
import { UserV2 } from "../../../../models/v2/users-v2";
import { aesDecrypt } from "../../../../util/aes-decrypt";

const router = express.Router();

router.get(
    "/v3/authentication/users/actionToken",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Extract mfaToken and type from the body
            req.action = "GetUserActionToken";

            if(!req.currentUser) {
                throw new InternalServerError();
            }

            // Step 2: Fetch user info
            const user = await UserV2.findById(req.currentUser.id).lean().exec();
            if(!user) {
                throw new InternalServerError();
            }

            let token = user.actionToken ?? null;
            if(token) {
                if((user.actionTokenExpiry as Date) < new Date()) {
                    token = null;
                }
            }
            // Step 3: Send response
            return res.sendResponse({
                token: token ? (aesDecrypt(token, process.env.RESPONSE_ENCRYPTION_KEY!) ?? null) : null
            });

        }
        catch(error) {
            console.error("Authentication.GetUserActionTokenV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as getUserActionTokenV3Router };
