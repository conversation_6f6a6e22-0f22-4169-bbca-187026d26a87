import express, { Request, NextFunction } from "express";
import { Auth0Service } from "../../../../services/auth0";
import { MFATypesEnum } from "../../../../enums/mfa-types";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { currentUser, InternalServerError, NotFoundCode, ResourceNotFoundError, responseHandler, validateRequest } from "@moxfive-llc/common";
import { VerifyMFAValidation } from "../../../../validations/v3/authentication/verify-mfa.validation";
import jwt from "jsonwebtoken";
import { UserV2 } from "../../../../models/v2/users-v2";
import { generateTemporaryToken } from "../../../../util";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { aesEncrypt } from "../../../../util/aes-encypt";

const router = express.Router();

router.post(
    "/v3/authentication/mfa/verify",
    responseHandler,
    currentUser,
    VerifyMFAValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Extract mfaToken, type, otp and recoveryCode from the body
            req.action = "VerifyMFAEnrollment";
            const { mfaToken, type, otp, recoveryCode }: {
                mfaToken: string, type: keyof typeof MFATypesEnum, otp?: string, recoveryCode?: string
            } = req.body;

            const login = !req.currentUser;
            let isMFAEnabled = true;

            const auth0Service = new Auth0Service(req);

            // If it is not login
            if(login) {
                // Check MFA enrollments and if there aren't any then set isMFAEnabled to false
                const mfaEnrollments = await auth0Service.listUserMFAAuthenticatorsUsingMFAToken(mfaToken);
                if(!mfaEnrollments.length) {
                    isMFAEnabled = false;
                }
            }

            // Step 2: Verify MFA
            const tokens = await auth0Service.verifyMFA({
                mfaToken,
                otp,
                recoveryCode,
                login
            });

            if(!tokens) {
                throw new InternalServerError();
            }

            let newRecoveryCode = tokens.recoveryCode;

            // Step 3 : If we need to login the user then process the user login otherwise save actionToken for that user
            // If need to login then process login
            if (login) {
                const userService = new UserServiceV3({});
                await userService.processUserLogin({
                    idToken: tokens.idToken,
                    externalRefreshToken: tokens.refreshToken,
                    pkceEnabled: false,
                    passwordLogin: true,
                    response: res,
                    request: req
                });

                // IF MFA is not enabled then regenerate the recovery code
                if(!isMFAEnabled) {
                    // Decode token and ensure sub is prsent
                    const tokenDetails: any = jwt.decode(tokens.idToken);
                    if (!tokenDetails || !tokenDetails.sub) {
                        throw new InternalServerError();
                    }

                    const { accessToken } = await auth0Service.fetchApplicationAccessToken();

                    newRecoveryCode = await auth0Service.regenerateRecoveryCode({
                        accessToken,
                        userId: tokenDetails.sub
                    });
                }
            }
            // Otherwise generate action token and save it
            else {
                const { token: temporaryToken, expiry } = generateTemporaryToken({}); // Default is token of length 42 with expiry of 20 minutes.

                // Decode token and ensure email is prsent
                const tokenDetails: any = jwt.decode(tokens.idToken);
                if (!tokenDetails || !tokenDetails.email) {
                    throw new InternalServerError();
                }

                const mongoTransaction = new MongoTransaction();
                await mongoTransaction.startTransaction();
                try {
                    // Fetch user and check whether it's exists or not
                    const user = await UserV2.findOne({ email: tokenDetails.email }).session(mongoTransaction.session);
                    if (!user) {
                        throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not fonud.");
                    }

                    user.actionToken = aesEncrypt(temporaryToken, process.env.RESPONSE_ENCRYPTION_KEY!);
                    user.actionTokenExpiry = expiry;

                    await user.save({ session: mongoTransaction.session });

                    // Update user's details to v1
                    await userV2ToV1Sync({
                        user: user.toObject(),
                        operationType: OperationTypesEnums.UPDATE,
                        session: mongoTransaction.session
                    });

                    // Commit the transaction
                    await mongoTransaction.commitTransaction();

                    // Publish NATS Event
                    await userUpdatedPublisherV2Wrapper(user);
                }
                catch (error) {
                    await mongoTransaction.abortTransaction();

                    throw new Error(error as any);
                }
            }

            // Step 4: Encrypt the response and send the response
            return res.sendResponse(
                {
                    meta: {
                        "message": "You've been authenticated successfully."
                    },
                    type,
                    recoveryCode: newRecoveryCode ? aesEncrypt(newRecoveryCode, process.env.RESPONSE_ENCRYPTION_KEY!) : null,
                }
            );
        }
        catch (error) {
            console.error("Authentication.VerifyMFAEnrollment.");
            console.error(error);
            next(error);
        }
    }
);

export { router as verifyMFAV3Router };
