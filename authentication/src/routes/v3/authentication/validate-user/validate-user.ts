import express, { Request, NextFunction } from "express";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { responseHandler, validateRequest } from "@moxfive-llc/common";
import { ValidateUserValidation } from "../../../../validations/v3/authentication/validate-user.validation";
import { Auth0Service } from "../../../../services/auth0";

const router = express.Router();

router.post(
    "/v3/authentication/validateUser",
    responseHandler,
    ValidateUserValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Extract Email from the body
            req.action = "ValidateUser";

            const { email, internalLogin = false }: { email: string, internalLogin: boolean } = req.body;

            // Step 2: Get the login options
            const loginOptions = await UserServiceV3.getUserLoginOptions(email, internalLogin);

            // If user's email is not verified then send email to that user
            if(loginOptions.verified === false) {
                const auth0Service = new Auth0Service(req);
                await auth0Service.sendEmailVerificationCode({ email });
            }

            // Step 3: Send response
            res.sendResponse(loginOptions, {});
        }
        catch (error) {
            console.error("Authentication.ValidateUser.");
            console.error(error);
            next(error);
        }
    }
);

export { router as validateUserV3Router };
