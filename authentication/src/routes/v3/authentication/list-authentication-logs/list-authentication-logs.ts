import {
    convertToObjectId,
    createPagination,
    currentUser,
    FilterQuery,
    getPage,
    hasGlobalAction,
    InsufficientPrivilagesError,
    isValidMongoObjectId,
    PageResponseObj,
    PaginationEntity,
    requireAuth,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { authenticationFields } from "../../../../util/authentication-fields";
import { SortQuery } from "../../../../interfaces";
import { fetchDefaultSortObject, unSanitizeIP } from "../../../../util";
import { fetchFiltersFromMultipleSectionsBasedOnPermission } from "../../../../util/fetch-filters-from-multiple-sections-based-on-permission";
import { fetchSortFromMultipleSectionsBasedOnPermission } from "../../../../util/fetch-sort-from-multiple-sections-based-on-permission";
import { AuthLog, AuthLogDoc } from "../../../../models/auth-log.model";

const router = express.Router();

router.get("/v3/authentication/logs",
    responseHandler,
    currentUser,
    requireAuth,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction): Promise<any> => {
        try {
            // Step 1: Check user has permission to get all organizations
            const hasPermission = await hasGlobalAction(req, "ListAuthenticationLogs");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };

            // eslint-disable-next-line prefer-const
            let { filter, sort, skipToken } = req.query;

            // Step 2: If we are getting skipToken in request then just fetch the requested page and return the response
            if (skipToken) {
                const response: PageResponseObj | null = await getPage(req, PaginationEntity.LIST_AUTH0_LOGS);
                if (response) {
                    return res.sendResponse({
                        "links": {
                            "next": response.meta.nextPageLink ?? "",
                            "previous": response.meta.previousPageLink ?? ""
                        },
                        "values": response.data
                    }, {});
                }
                else {
                    return res.sendResponse({}, {});
                }
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: authenticationFields.authenticationLogs.commonBase
                }
            ];

            const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);
            const filtersQuery: FilterQuery = {};

            // If filter is provided then prepare filter query
            if (filter) {
                const filters = fetchFiltersFromMultipleSectionsBasedOnPermission({
                    filter: filter as string,
                    sections
                });

                if (filters["type"] && filters["type"] === "all") {
                    delete filters["type"];
                }
                if (filters["organization"]) {
                    if (isValidMongoObjectId(filters["organization"])) {
                        filters["organization.id"] = convertToObjectId(filters["organization"]);
                        delete filters["organization"];
                    }
                    else {
                        delete filters["organization"];
                    }
                }
                if (filters["user"]) {
                    if (isValidMongoObjectId(filters["user"])) {
                        filters["user.id"] = convertToObjectId(filters["user"]);
                        delete filters["user"];
                    }
                    else {
                        delete filters["user"];
                    }
                }
                Object.assign(filtersQuery, filters);
            }

            // If sort is provided then prepare sort query
            if (sort) {
                const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
                    sort: sort as string,
                    sections
                });

                if (sortObj["organization"]) {
                    sortObj["organization.value"] = sortObj["organization"];
                    delete sortObj["organization"];

                }

                if (sortObj["user"]) {
                    sortObj["user.value"] = sortObj["user"];
                    delete sortObj["user"];
                }

                Object.assign(sortQuery, sortObj);
            }

            // Step 3: Fetch all organizations
            let aggregateQuery: any = [
                { $match: filtersQuery }
            ];

            if (Object.keys(sortQuery).length) {
                aggregateQuery.push({ $sort: sortQuery });
            }

            aggregateQuery = aggregateQuery.concat([
                {
                    $project: {
                        type: 1,
                        description: 1,
                        user: 1,
                        email: 1,
                        ipAddress: 1,
                        userAgent: 1,
                        timestamp: 1,
                        isFailure: 1,
                        organization: 1
                    }
                }
            ]);

            const authenticationLogsDetail: AuthLogDoc[] = await AuthLog.aggregate(aggregateQuery, {
                collation: {
                    locale: "en",
                    numericOrdering: true
                }
            });

            if (!authenticationLogsDetail || !authenticationLogsDetail.length) {
                return res.sendResponse({
                    totalRows: 0,
                    data: []
                }, {});
            }

            // Step 6: Format the response
            const result = authenticationLogsDetail.map(log => {
                const detail: any = { ...log };
                detail["id"] = String(log._id);
                delete detail._id;

                if (detail.ipAddress.length) {
                    detail.ipAddress = unSanitizeIP(detail.ipAddress);
                }
                return detail;
            });

            const response: PageResponseObj | null = await createPagination(req,
                PaginationEntity.LIST_AUTH0_LOGS,
                [],
                {},
                result);

            if (response) {
                // Step 7: Send response
                return res.sendResponse({
                    "links": {
                        "next": response.meta.nextPageLink ?? "",
                        "previous": response.meta.previousPageLink ?? ""
                    },
                    "values": response.data
                }, {});
            }
            return res.sendResponse({}, {});
        }
        catch (error) {
            console.error("Authentication.ListAuthenticationLogs");
            console.error(error);
            next(error);
        }
    });

export { router as listAuthenticationLogsRouter };
