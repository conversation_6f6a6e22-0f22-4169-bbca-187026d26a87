import express, { Request, NextFunction } from "express";
import { NotFoundCode, ResourceNotFoundError, responseHandler, TargetType, validateRequest } from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { UserV2 } from "../../../../models/v2/users-v2";
import jwt from "jsonwebtoken";
import { generateTemporaryToken, scryptHash } from "../../../../util";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { aesEncrypt } from "../../../../util/aes-encypt";
import { VerifyUserEmailCodeValidation } from "../../../../validations/v3/authentication/verify-user-email-code.validation";

const router = express.Router();

router.post(
    "/v3/authentication/verifyEmail/code",
    responseHandler,
    VerifyUserEmailCodeValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1: Extract Email from the body
            req.action = "VerifyUserEmailCode";

            const { email, code }: { email: string, code: string } = req.body;

            // Step 2: Fetch the user
            const user = await UserV2.findOne({ email: String(email) });
            if(!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            if(user.isEmailVerified) {
                return res.status(204).sendResponse("", {}); // TODO: Need error message
            }

            // Step 2: Get the login options
            const auth0Service = new Auth0Service(req);
            const response = await auth0Service.verifyUserEmailCodeOTP({ email, code });

            const decodedTokenDetails: any = jwt.decode(response.idToken);

            if(decodedTokenDetails.sub) {
                // Fetch Auth0 Management API Access Token and remove the user
                const { accessToken } = await auth0Service.fetchApplicationAccessToken();
                await auth0Service.removeUser({
                    userId: decodedTokenDetails.sub,
                    accessToken
                });
            }

            const setPassword = Boolean(user.externalAuth0LoginUserId);
            let sessionGUID: string | null = null;

            // If user need to set password then generate temporary token and hash it and save it in user's document
            if(setPassword) {
                const { token, expiry } = generateTemporaryToken({
                    minutes: 24 * 60
                });

                sessionGUID = token;
                const hashedToken = await scryptHash(token);

                user.passwordSetupToken = hashedToken;
                user.passwordSetupTokenExpiry = expiry;
            }

            // Set user's email verified to true and if user not need to set password then set account setup done to true
            user.isEmailVerified = true;
            user.emailVerificationToken = null;
            user.emailVerificationTokenExpiry = null;

            if(!setPassword) {
                user.isAccountSetupDone = true;
            }

            // Save user details
            await user.save({ session: mongoTransaction.session });

            // Sync user details to v1
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            // Step 3: Send response
            res.sendResponse({
                meta: {
                    message: "User verified successfully."
                },
                setPassword,
                sessionGUID: setPassword ? aesEncrypt(sessionGUID, process.env.RESPONSE_ENCRYPTION_KEY!) : null,
                u: setPassword ? String(user._id) : null
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user.id),
                            name: user.name,
                            email: user.email
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [
                    {
                        target: TargetType.USER,
                        propertyName: "isEmailVerified",
                        oldValue: false,
                        newValue: true
                    }
                ]
            });
        }
        catch (error) {
            console.error("Authentication.VerifyUserEmailCode.");
            console.error(error);
            next(error);
        }
    }
);

export { router as verifyUserEmailCodeV3Router };
