import express, { Request, NextFunction } from "express";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { responseHandler, validateRequest } from "@moxfive-llc/common";
import { Connections } from "../../../../models/connections";

const router = express.Router();

router.post(
    "/v3/authentication/getIDPURL",
    responseHandler,
    // ValidateUserValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Extract Email from the body
            req.action = "GetIDPURL";

            const { iss }: { iss: string } = req.body;

            const connection = await Connections.findOne({ idpDomain: String(iss) }).lean().exec();

            if(!connection) {
                return res.status(204).sendResponse("", {});
            }

            const loginUrl = UserServiceV3.getEnterpriseConnectionURL({
                connection: connection.connectionName ?? "",
                internalLogin: false
            });

            return res.sendResponse({
                enterpriseConnection: loginUrl
            }, {});
        }
        catch (error) {
            console.error("Authentication.GetIDPURL.");
            console.error(error);
            next(error);
        }
    }
);

export { router as getIDPURLV3Router };
