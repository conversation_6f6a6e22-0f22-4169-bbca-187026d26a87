import express, { Request, NextFunction } from "express";
import { InternalServerError, responseHandler } from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { UserServiceV3 } from "../../../../services/v3/user.service";

const router = express.Router();

router.get(
    "/v3/authentication/redirect",
    responseHandler,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            req.action = "Redirect";

            const { code } = req.query;

            // Step 2: Exchange authorization code for tokens with Auth0
            const auth0Service = new Auth0Service(req);
            const tokens = await auth0Service.exchangeAuthorizationCode({
                code: code as string,
                codeVerifier: null
            });

            if (!tokens) {
                throw new InternalServerError();
            }

            // Step 3: Process user login
            const userService = new UserServiceV3({});
            await userService.processUserLogin({
                idToken: tokens.idToken,
                externalRefreshToken: tokens.refreshToken,
                pkceEnabled: true,
                passwordLogin: false,
                response: res,
                request: req
            });

            res.cookie("directIDPLogin", "true", {
                httpOnly: false
            });

            // Step 4: Send the response
            return res.redirect(process.env.DOMAIN_URL!);
        }
        catch (error) {
            console.error("Authentication.Redirect.");
            console.error(error);
            next(error);
        }
    }
);

export { router as redirectV3Router };
