import express, { NextFunction, Request } from "express";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { UserV2 } from "../../../../models/v2/users-v2";
import { countLastXMinutesDateRecordsFromArray, generateTemporaryToken, scryptHash, sleep } from "../../../../util";
import { User } from "../../../../models/user";
import { responseHandler, TargetType, TooManyRequestsError, TooManyRequestsErrorCode, validateRequest } from "@moxfive-llc/common";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { CreateEmailVerificationRequestValidation } from "../../../../validations/v3/authentication/create-email-verification-request.validation";
import { WelcomeEmailNotificationPublisher } from "../../../../events/publishers/welcome-email-notification";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { natsWrapper } from "../../../../nats-wrapper";
import { queueGroupName } from "../../../../events/queue-group-name";

const router = express.Router();

router.post(
    "/v3/authentication/verifyEmail/request",
    responseHandler,
    CreateEmailVerificationRequestValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "CreateVerifyUserEmailRequest";

            // Step 2: Check whether user present or not
            const { email } = req.body;
            const user = await UserV2.findOne({ email: String(email) }).session(mongoTransaction.session);

            // Step 3: If User is not found either in the platform or in Auth0 or user's email is already verified then send response
            if(!user || !user.externalAuth0LoginUserId || user.isEmailVerified) {
                // NOTE: Putting sleep because user should not be getting idea that user does not exist due to early response as per OWASP
                await sleep(300);

                return res.json({
                    "meta": {
                        "message": "Email verification link sent to your email address."
                    }
                });
            }

            // Step 4: Check whether user has exceeds the limit of request or not. If yes, then throw an error
            const count = countLastXMinutesDateRecordsFromArray(user.emailVerificationRequests as string[], 24 * 60);
            if(count >= 3) {
                throw new TooManyRequestsError("Too many email verification requests. Please try again later.",
                    TooManyRequestsErrorCode.TOO_MANY_EMAIL_VERIFICATION_REQUEST
                );
            }

            // Step 5: Generate token and it's hash
            const { token, expiry } = generateTemporaryToken({
                minutes: 24 * 60
            });
            const hashedToken = await scryptHash(token);

            // Step 5: Update user's email verification token with expiry also sync that details with user v1
            user.emailVerificationToken = hashedToken;
            user.emailVerificationTokenExpiry = expiry;

            // If emailVerificationRequests length is 10 then remove first request.
            if(user.emailVerificationRequests.length === 10) {
                user.emailVerificationRequests.shift();
            }
            // Add current DateTime in emailVerificationRequests
            (user.emailVerificationRequests as number[]).push(Date.now());

            await user.save({ session: mongoTransaction.session });
            await User.findByIdAndUpdate(String(user._id), { version: user.version }, { session: mongoTransaction.session });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            // Step 6: Send an email to user
            try {
                await new WelcomeEmailNotificationPublisher(natsWrapper.client).publish({
                    name: user.name,
                    email: user.email,
                    setPassword: Boolean(user.externalAuth0LoginUserId),
                    url: UserServiceV3.generateWelcomeEmailURL(token, String(user._id)),
                    serviceName: queueGroupName
                });
            }
            catch(error) {
                console.error("Error in sending an email", error);
            }

            // Step 7: Send Response
            res.sendResponse({
                "meta": {
                    "message": "Email verification link sent to your email address."
                },
                token,
                u: String(user._id)
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: []
            });
        }
        catch(error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.CreateVerifyUserEmailRequestV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as createVerifyUserEmailRequestV3Router };
