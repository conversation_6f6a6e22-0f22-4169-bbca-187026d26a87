import express, { NextFunction, Request } from "express";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { Auth0Service } from "../../../../services/auth0";
import { aesDecrypt } from "../../../../util/aes-decrypt";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { SetUserPasswordValidation } from "../../../../validations/v3/authentication/verify-email-verification-request.validation";
import { InternalServerError, responseHandler, TargetType, validateRequest } from "@moxfive-llc/common";
import { User } from "../../../../models/user";

const router = express.Router();

router.post(
    "/v3/authentication/setPassword",
    response<PERSON><PERSON><PERSON>,
    SetUserPasswordValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "SetUserPassword";

            // Step 2: Verify Token
            const { sessionGUID, password, u }: { sessionGUID: string, password: string, u: string } = req.body;

            const { user } = await UserServiceV3.verifyToken({
                session: mongoTransaction.session,
                userId: u,
                token: sessionGUID,
                type: TokenTypesEnum.PASSWORD_SETUP
            });

            // Step 3: Update user's password in Auth0
            const decryptedPassword = aesDecrypt(password, process.env.RESPONSE_ENCRYPTION_KEY!);
            if(!decryptedPassword) {
                throw new InternalServerError();
            }

            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            await auth0Service.updateUserDetails({
                accessToken,
                userId: user.externalAuth0LoginUserId ?? "",
                data: {
                    password: decryptedPassword
                }
            });

            // Step 4: Update user's password setup token and expiry as null also sync that details with user v1
            user.passwordSetupToken = null;
            user.passwordSetupTokenExpiry = null;
            user.isAccountSetupDone = true;

            await user.save({ session: mongoTransaction.session });
            await User.findByIdAndUpdate(String(user._id), { version: user.version }, { session: mongoTransaction.session });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS event
            await userUpdatedPublisherV2Wrapper(user);

            // Step 5: Send Response
            res.sendResponse({
                "meta": {
                    "message": "Email verified successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user.id),
                            name: user.name,
                            email: user.email,
                            // azureId: user.azureId
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [
                    {
                        target: TargetType.USER,
                        propertyName: "isAccountSetupDone",
                        oldValue: false,
                        newValue: true
                    }
                ]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.SetUserPasswordV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as setUserPasswordV3Router };
