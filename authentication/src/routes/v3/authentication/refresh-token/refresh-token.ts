import { InvalidTokenError, responseHandler, TargetType } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { JsonWebTokenV2Service } from "../../../../services/v2/json-web-token";
import { Auth0Service } from "../../../../services/auth0";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { UserV2 } from "../../../../models/v2/users-v2";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";

const router = express.Router();

router.get(
    "/v3/authentication/refreshToken",
    responseHandler,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "RefreshToken";

            // Step 1: If we don't get refresh token then throw an error
            const signedRefreshToken = req.signedCookies.Token2;
            if (!signedRefreshToken) {
                console.error("!signedRefreshToken");
                throw new InvalidTokenError();
            }

            // Verify Refresh Token
            const data = JsonWebTokenV2Service.getRefreshTokenDetails({ refreshToken: signedRefreshToken });

            // Verify user exists, it is enabled and deviceId is also valid.
            const user = await UserV2.findById(data.id, {
                isEnabled: 1,
                organization: 1,
                allowedLoginTokens: 1
            }).lean().exec();
            if (!user || !user.isEnabled) {
                console.info("Error From user");
                throw new InvalidTokenError();
            }

            if (!user.allowedLoginTokens.map(String).includes(String(data.deviceId))) {
                console.info("Error From allowedLoginTokens");
                throw new InvalidTokenError();
            }

            // Verify User's organization is enabled
            const organization = await OrganizationV2.findById(user.organization?.id, {
                isEnabled: 1
            }).lean().exec();
            if (!organization || !organization.isEnabled) {
                throw new InvalidTokenError();
            }

            // Refresh Token from Auth0
            const auth0Service = new Auth0Service(req);
            const tokens = await auth0Service.refreshToken(data);

            // Step 3 : Generate Access Token and refresh token for our platform
            const userService = new UserServiceV3({});
            await userService.processUserLogin({
                idToken: tokens.idToken,
                externalRefreshToken: tokens.refreshToken,
                pkceEnabled: data.pkceEnabled,
                passwordLogin: data.passwordLogin,
                response: res,
                request: req,
                refreshTokenData: data
            });

            res.sendResponse({
                meta: {
                    message: "Access token has been refreshed successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: []
            });
        }
        catch (error) {
            console.error("Authentication.RefreshTokenV3.");
            console.error(error);
            next(error);
        }
    }
);
export { router as refreshTokenV3Router };
