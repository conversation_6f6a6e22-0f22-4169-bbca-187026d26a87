import express, { NextFunction, Request } from "express";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { Auth0Service } from "../../../../services/auth0";
import { aesDecrypt } from "../../../../util/aes-decrypt";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { ResetPasswordValidation } from "../../../../validations/v3/authentication/reset-password.validation";
import { InternalServerError, responseHandler, TargetType, validateRequest } from "@moxfive-llc/common";
import { User } from "../../../../models/user";
import { PasswordChangeSuccessNotificationPublisher } from "../../../../events/publishers/password-change-success-notification";
import { natsWrapper } from "../../../../nats-wrapper";
import { queueGroupName } from "../../../../events/queue-group-name";

const router = express.Router();

router.post(
    "/v3/authentication/forgotPassword/reset",
    responseHandler,
    ResetPasswordValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "ResetPassword";

            // Step 2: Verify Token
            const { token, password, u }: { token: string, password: string, u: string } = req.body;

            const { user } = await UserServiceV3.verifyToken({
                session: mongoTransaction.session,
                userId: u,
                token,
                type: TokenTypesEnum.FORGOT_PASSWORD
            });

            // Step 3: Update user's password in Auth0
            const decryptedPassword = aesDecrypt(password, process.env.RESPONSE_ENCRYPTION_KEY!);
            if(!decryptedPassword) {
                throw new InternalServerError();
            }
            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            await auth0Service.updateUserDetails({
                accessToken,
                userId: user.externalAuth0LoginUserId ?? "",
                data: {
                    password: decryptedPassword
                }
            });

            // Step 4: Update user's forgot password token and expiry as null also sync that details with user v1
            user.forgotPasswordToken = null;
            user.forgotPasswordTokenExpiry = null;
            user.forgotPasswordRequests = [];

            /*
             * It's a special case that suppose user is added in the platform and welcome email with email verification + set passwrod is sent to user
             * Now, user clicks on link and verifies email but didn't set the password.
             * So, here account setup done will be false. Hence, on forgot password request validation if email verificatino is true and account setup done is false then we need to set it as true
             */
            if(user.isEmailVerified && !user.isAccountSetupDone) {
                user.isAccountSetupDone = true;
            }

            // CASE: When user change his/her password, all the active sessions will be destroyed.
            UserServiceV3.destroyAllUserActiveSessions(user);

            // CASE: If user's account is locked but if that user reset the password then, account will be unlocked.
            user.isAccountLocked = false;
            user.accountLockedAt = null;
            user.failedLoginAttempts = [];
            user.lockType = null;

            await user.save({ session: mongoTransaction.session });
            await User.findByIdAndUpdate(String(user._id), { version: user.version }, { session: mongoTransaction.session });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS event
            await userUpdatedPublisherV2Wrapper(user);

            try {
                await new PasswordChangeSuccessNotificationPublisher(natsWrapper.client).publish({
                    name: user.name,
                    email: user.email,
                    serviceName: queueGroupName
                });
            }
            catch(error) {
                console.error("Error in sending an email", error);
            }

            // Step 5: Send Response
            res.sendResponse({
                "meta": {
                    "message": "Password changed successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: []
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.VerifyForgotPasswordRequestV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as resetPasswordV3Router };
