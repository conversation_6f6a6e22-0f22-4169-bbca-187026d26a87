import express, { Request, NextFunction } from "express";
import { Auth0Service } from "../../../../services/auth0";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { InternalServerError, responseHandler, validateRequest } from "@moxfive-llc/common";
import { VerifyCodeValidation } from "../../../../validations/v3/authentication/verify-code.validation";

const router = express.Router();

router.post(
    "/v3/authentication/verifyCode",
    responseHandler,
    VerifyCodeValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            req.action = "VerifyCode";

            // Step 1: Extract code and codeVerifier from the body
            const { code, codeVerifier }: {
                code: string, codeVerifier: string | null
            } = req.body;

            // Step 2: Exchange authorization code for tokens with Auth0
            const auth0Service = new Auth0Service(req);
            const tokens = await auth0Service.exchangeAuthorizationCode({
                code,
                codeVerifier
            });

            if (!tokens) {
                throw new InternalServerError();
            }

            // Step 3: Process user login
            const userService = new UserServiceV3({});
            await userService.processUserLogin({
                idToken: tokens.idToken,
                externalRefreshToken: tokens.refreshToken,
                pkceEnabled: true,
                passwordLogin: false,
                response: res,
                request: req
            });

            // Step 4: Send the response
            return res.sendResponse(
                {
                    meta: {
                        "message": "You've been authenticated successfully."
                    },
                }
            );
        }
        catch (error) {
            console.error("Authentication.VerifyCode.");
            console.error(error);
            next(error);
        }
    }
);

export { router as verifyCodeV3Router };
