import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    requireAuth, response<PERSON>and<PERSON>
} from "@moxfive-llc/common";
import { authenticationFields } from "../../../../util/authentication-fields";
import { fetchModuleFilterFieldsBasedOnPermission } from "../../../../util/fetch-module-filter-fields-based-on-permission";
import { ValueTypesEnum } from "@moxfive-llc/common/build/enums/value-types.enum";

const router = express.Router();

router.get("/v3/authentication/logs/facets",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check permission
            req.action = "ListAuthenticationLogsFacets";

            // Step 2: Fetch filterable fields
            const fields = await fetchModuleFilterFieldsBasedOnPermission({
                sections: [
                    {
                        path: authenticationFields.authenticationLogs.commonBase,
                    }
                ],
                assignedActions: new Set()
            });

            fields.forEach(field => {
                if (field.type === ValueTypesEnum.STRING_WITH_EQ && field.values) {
                    field.values = [{
                        "id": "all",
                        "value": "All"
                    }, ...field.values];

                    field["default"] = "all";
                }
            });

            // Step 3: Send response
            res.sendResponse(fields, {});
        }
        catch (error) {
            console.error("Incident.fetchAuthenticationLogsFilters");
            console.error(error);
            next(error);
        }
    });

export { router as fetchAuthenticationLogsFiltersRouter };
