import express, { NextFunction, Request } from "express";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { responseHandler, TargetType, validateRequest } from "@moxfive-llc/common";
import { VerifyEmailValidationValidation } from "../../../../validations/v3/authentication/verify-user-email.validation";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { scryptHash } from "../../../../util";
import { User } from "@moxfive-llc/common/build/models/user.model";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { Connections } from "../../../../models/connections";

const router = express.Router();

router.post(
    "/v3/authentication/verifyEmail",
    responseH<PERSON><PERSON>,
    VerifyEmailValidationValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "VerifyUserEmail";

            // Step 1: Verify Token
            const { token, u, sessionGUID }: { token: string, u: string, sessionGUID: string } = req.body;

            const { user } = await UserServiceV3.verifyToken({
                userId: u,
                token,
                type: TokenTypesEnum.EMAIL_VERIFICATION,
                session: mongoTransaction.session
            });

            // Step 2: Hash sessionGUID which will be used for setting up the password
            const hashedToken = await scryptHash(sessionGUID);

            // Step 3: Update email verification token and expirty to null and set password setup token with expiry, also set isEmailVerified to true
            user.passwordSetupToken = hashedToken;
            user.passwordSetupTokenExpiry = user.emailVerificationTokenExpiry;
            user.emailVerificationToken = null;
            user.emailVerificationTokenExpiry = null;
            user.isEmailVerified = true;

            // CASE: Here, for IDP login users they need to setup the password. So, once email is verified account setup is also done
            if(!user.externalAuth0LoginUserId) {
                user.isAccountSetupDone = true;
            }

            // Step 4: Save user details, sync it to v1 collection and commit the transaction
            await user.save({ session: mongoTransaction.session });
            await User.findByIdAndUpdate(String(user._id), { version: user.version }, { session: mongoTransaction.session });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            // Fetch connection
            const connection = await Connections.findOne({ "organization.id": user.organization?.id }, {
                connectionName: 1
            }).lean().exec();

            // Step 5: Send Response
            // If the user is Auth0 user then send token that will use to set the new password othertwise send the login URL
            if(user.externalAuth0LoginUserId) {
                res.sendResponse({
                    meta: {
                        message: "User verified successfully."
                    },
                    setPassword: true
                },  {
                    targets: [
                        {
                            type: TargetType.USER,
                            details: {
                                id: String(user.id),
                                name: user.name,
                                email: user.email,
                                // azureId: user.azureId
                            }
                        }
                    ],
                    correlation: TargetType.ORGANIZATION,
                    correlationId: String(user.organization?.id),
                    modifiedProperties: [
                        {
                            target: TargetType.USER,
                            propertyName: "isEmailVerified",
                            oldValue: false,
                            newValue: true
                        }
                    ]
                });
            }
            else {
                res.sendResponse({
                    meta: {
                        message: "User verified successfully."
                    },
                    loginURL: UserServiceV3.getEnterpriseConnectionURL({
                        connection: connection ? connection.connectionName : "",
                        internalLogin: false
                    })
                },   {
                    targets: [
                        {
                            type: TargetType.USER,
                            details: {
                                id: String(user.id),
                                name: user.name,
                                email: user.email,
                                // azureId: user.azureId
                            }
                        }
                    ],
                    correlation: TargetType.ORGANIZATION,
                    correlationId: String(user.organization?.id),
                    modifiedProperties: [
                        {
                            target: TargetType.USER,
                            propertyName: "isEmailVerified",
                            oldValue: false,
                            newValue: true
                        },
                        {
                            target: TargetType.USER,
                            propertyName: "isAccountSetupDone",
                            oldValue: false,
                            newValue: true
                        }
                    ]
                });
            }

        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.VerifyUserEmailV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as verifyUserEmailV3Router };
