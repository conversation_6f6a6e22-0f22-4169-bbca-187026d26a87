import express, { NextFunction, Request, Response } from "express";
import { currentUser, requireAuth, responseHand<PERSON> } from "@moxfive-llc/common";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { UserV2 } from "../../../../models/v2/users-v2";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";

const router = express.Router();

router.get(
    "/v3/authentication/logout",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "LogOut";

            // If there is deviceId for logged in user
            if(req.currentUser?.deviceId) {
                const mongoTransaction = new MongoTransaction();
                mongoTransaction.startTransaction();

                try {
                    // Fetch the user details
                    const user = await UserV2.findById(req.currentUser.id).session(mongoTransaction.session);
                    if(user) {
                        // Remvove entry from devices and allowedLoginTokens
                        user.devices = user.devices.filter(device => {
                            return String(device.id) !== String(req.currentUser?.deviceId);
                        });

                        user.allowedLoginTokens = user.allowedLoginTokens.filter(token => {
                            return String(token) !== String(req.currentUser?.deviceId);
                        }) as string[];

                        // Save user details and sync data to v1
                        await user.save({ session: mongoTransaction.session });

                        // Add user entry in v1 as well
                        await userV2ToV1Sync({
                            user: user.toObject(),
                            operationType: OperationTypesEnums.UPDATE,
                            session: mongoTransaction.session
                        });

                        // Commit transaction
                        await mongoTransaction.commitTransaction();

                        await userUpdatedPublisherV2Wrapper(user);
                    }
                }
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                catch(err) {
                    // Abort transaction
                    await mongoTransaction.abortTransaction();
                }
            }

            // Remove Cookies
            res.clearCookie("Token1");
            res.clearCookie("Token2");

            // Send response
            res.sendResponse({
                meta: {
                    message: "User has been logged out successfully!"
                }
            }, {});
        }
        catch(error) {
            console.error("Authentication.LogOutV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as logOutV3Router };
