import express, { Request, NextFunction } from "express";
import { response<PERSON><PERSON><PERSON>, TargetType, validateRequest } from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { UserV2 } from "../../../../models/v2/users-v2";
import { CreateVerifyUserEmailCodeRequestValidation } from "../../../../validations/v3/authentication/create-verify-user-email-code-request.validation";

const router = express.Router();

router.post(
    "/v3/authentication/verifyEmail/code/request",
    response<PERSON><PERSON><PERSON>,
    CreateVerifyUserEmailCodeRequestValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Extract Email from the body
            req.action = "CreateVerifyUserEmailCodeRequest";

            const { email }: { email: string } = req.body;

            const user = await UserV2.findOne({ email: String(email) }, {
                isEmailVerified: 1
            }).lean().exec();
            if(!user || user.isEmailVerified) {
                return res.status(204).sendResponse("", {});
            }
            // Step 2: Resend the verification code
            const auth0Service = new Auth0Service(req);
            await auth0Service.sendEmailVerificationCode({ email });

            // Step 3: Send response
            res.sendResponse({
                "meta": {
                    "message": "Verification code has been sent to your email address."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: []
            });
        }
        catch (error) {
            console.error("Authentication.CreateVerifyUserEmailCodeRequest.");
            console.error(error);
            next(error);
        }
    }
);

export { router as createVerifyUserEmailCodeRequestV3Router };
