import express, { Request, NextFunction } from "express";
import { Auth0Service } from "../../../../services/auth0";
import { MFATypesEnum } from "../../../../enums/mfa-types";
import { AssociateMFAEnrollmentValidation } from "../../../../validations/v3/authentication/associate-mfa-enrollment.validation";
import { responseHandler, validateRequest } from "@moxfive-llc/common";
import { aesEncrypt } from "../../../../util/aes-encypt";

const router = express.Router();

router.post(
    "/v3/authentication/mfa/associate",
    responseHand<PERSON>,
    AssociateMFAEnrollmentValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Extract mfaToken and type from the body
            req.action = "AssociateMFAEnrollment";

            const { mfaToken, type }: {
                mfaToken: string, type: keyof typeof MFATypesEnum
            } = req.body;

            // Step 2: Create User Enrollment
            const auth0Service = new Auth0Service(req);
            const mfaEnrollmentSecrets = await auth0Service.createUserMFAEnrollment({
                mfaToken,
                authenticatorTypes: [type]
            });

            // Step 3: Encrypt the response and send the response
            return res.sendResponse(
                aesEncrypt(JSON.stringify(mfaEnrollmentSecrets), process.env.RESPONSE_ENCRYPTION_KEY!),
                {}
            );
            // return res.json(mfaEnrollmentSecrets);
        }
        catch(error) {
            console.error("Authentication.AssociateMFAEnrollment.");
            console.error(error);
            next(error);
        }
    }
);

export { router as associateMFAEnrollmentV3Router };
