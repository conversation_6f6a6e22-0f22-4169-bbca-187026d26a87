import express, { NextFunction, Request } from "express";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { responseHand<PERSON>, validateRequest } from "@moxfive-llc/common";
import { VerifyForgotPasswordRequestValidation } from "../../../../validations/v3/authentication/verify-forgot-password-request.validation";

const router = express.Router();

router.post(
    "/v3/authentication/forgotPassword/verify",
    responseHand<PERSON>,
    VerifyForgotPasswordRequestValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {

        try {
            req.action = "VerifyForgotPasswordRequest";

            // Step 1: Verify Token
            const { token, u }: { token: string, password: string, u: string } = req.body;

            await UserServiceV3.verifyToken({
                userId: u,
                token,
                type: TokenTypesEnum.FORGOT_PASSWORD,
                tokenExpiredError: true
            });

            // Step 2: Send Response
            res.status(204).sendResponse();

        }
        catch (error) {
            console.error("Authentication.VerifyForgotPasswordRequestV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as verifyForgotPasswordRequestV3Router };
