import express, { NextFunction, Request } from "express";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { UserV2 } from "../../../../models/v2/users-v2";
import { countLastXMinutesDateRecordsFromArray, generateTemporaryToken, scryptHash, sleep } from "../../../../util";
import { User } from "../../../../models/user";
import { responseHandler, TargetType, TooManyRequestsError, TooManyRequestsErrorCode, validateRequest } from "@moxfive-llc/common";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { RequestForgotPasswordValidation } from "../../../../validations/v3/authentication/request-forgot-password.validation";
import { ForgotPasswordNotificationPublisher } from "../../../../events/publishers/forgot-password-notification";
import { natsWrapper } from "../../../../nats-wrapper";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { queueGroupName } from "../../../../events/queue-group-name";

const router = express.Router();

router.post(
    "/v3/authentication/forgotPassword/request",
    responseHandler,
    RequestForgotPasswordValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            req.action = "RequestForgotPasword";

            // Step 2: Check whether user present or not
            const { email } = req.body;
            const user = await UserV2.findOne({ email: String(email) }).session(mongoTransaction.session);

            // Step 3: If User is not found either in the platform or in Auth0 or user's email is not verified then send response
            if(!user || !user.externalAuth0LoginUserId || !user.isEmailVerified || !user.isEnabled) {
                // NOTE: Putting sleep because user should not be getting idea that user does not exist due to early response as per OWASP
                await sleep(300);

                return res.json({
                    "meta": {
                        "message": "Reset password link sent to your email address."
                    }
                });
            }

            // Step 4: Check whether user has exceeds the limit of request or not. If yes, then throw an error
            const count = countLastXMinutesDateRecordsFromArray(user.forgotPasswordRequests as string[], 60);
            if(count >= 5) {
                throw new TooManyRequestsError("Too many password reset attempts. Please try again later.",
                    TooManyRequestsErrorCode.TOO_MANY_PASSWORD_RESET_REQUEST
                );
            }

            // Step 5: Generate token and it's hash
            const { token, expiry } = generateTemporaryToken({ minutes: 20 });
            const hashedToken = await scryptHash(token);

            // Step 5: Update user's forgot password token with expiry also sync that details with user v1
            user.forgotPasswordToken = hashedToken;
            user.forgotPasswordTokenExpiry = expiry;

            // If forgotPasswordRequests length is 10 then remove first request.
            if(user.forgotPasswordRequests.length === 10) {
                user.forgotPasswordRequests.shift();
            }
            // Add current DateTime in forgotPasswordRequests
            (user.forgotPasswordRequests as number[]).push(Date.now());

            await user.save({ session: mongoTransaction.session });
            await User.findByIdAndUpdate(String(user._id), { version: user.version }, { session: mongoTransaction.session });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            // Step 6: Send an email to user
            await new ForgotPasswordNotificationPublisher(natsWrapper.client).publish({
                name: user.name,
                email: user.email,
                url: UserServiceV3.generateForgotPasswordURL(token, String(user._id)),
                serviceName: queueGroupName
            });

            // Step 7: Send Response
            res.sendResponse({
                "meta": {
                    "message": "Reset password link sent to your email address."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: []
            });
        }
        catch(error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.RequestForgotPaswordV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as requestForgotPaswordV3Router };
