import express, { Request, NextFunction } from "express";
import { InternalServerError, InvalidCredentialsError, ResourceLockedError, ResourceLockedErrorCodes, responseHandler, TargetType, validateRequest } from "@moxfive-llc/common";
import { aesDecrypt } from "../../../../util/aes-decrypt";
import { Auth0Service } from "../../../../services/auth0";
// import { aesEncrypt } from "../../../util/aes-encypt";
import { UserV2 } from "../../../../models/v2/users-v2";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { MFATypesEnum } from "../../../../enums/mfa-types";
import { LoginValidation } from "../../../../validations/v3/authentication/login.validation";
import { countLastXMinutesDateRecordsFromArray } from "../../../../util";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
// import { aesEncrypt } from "../../../util/aes-encypt";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { LockTypesEnum } from "../../../../enums/lock-types.enum";

const router = express.Router();

router.post(
    "/v3/authentication/login",
    responseHandler,
    LoginValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1: Extract Email and Password from the body
            req.action = "Login";

            const { email, password }: {
                email: string, password: string
            } = req.body;

            // if(email) {
            //     return res.send({
            //         password: aesEncrypt("Demo@1234", process.env.RESPONSE_ENCRYPTION_KEY!)
            //     });
            // }

            /*
             * Step 2:
             *  If user
             *      Does not present
             *      User is not enabled
             *      Organization is not enabled
             *      In auth0 that user doesn't exist
             *      User Email is not verified
             *      User Account setup is not done
             *  Then throw an error
             *
             * If user account is locked
             *  Then throw an error
             */
            const user = await UserV2.findOne({ email: String(email) });
            if (!user || !user.isEnabled || !user.organization || !user.externalAuth0LoginUserId || !user.isEmailVerified || !user.isAccountSetupDone) {
                throw new InvalidCredentialsError();
            }

            if(user.isAccountLocked) {
                throw new ResourceLockedError("Your account is locked. If you believe this is an error, <NAME_EMAIL>.",
                    ResourceLockedErrorCodes.USER_ACCOUNT_LOCKED
                );
            }

            const organization = await OrganizationV2.findById(user.organization.id).lean().exec();
            if (!organization || !organization.isEnabled) {
                throw new InvalidCredentialsError();
            }

            // Step 3: Decrypt the encrypted password
            const decryptedPassword = aesDecrypt(password, process.env.RESPONSE_ENCRYPTION_KEY!);
            if(!decryptedPassword) {
                throw new InvalidCredentialsError();
            }

            // console.info("User Agent", req.headers["user-agent"]);

            // Step 4: Validate user credentials
            const oldDevices = [...(user.toJSON()).devices];

            const auth0Service = new Auth0Service(req);
            const { mfaToken, invalidCredentials } = await auth0Service.validateUserCredentials({
                email: email ?? "",
                password: decryptedPassword
            });

            // If the credentials are invalid
            if(invalidCredentials) {
                // Add entry in the failed login attempts
                (user.failedLoginAttempts as number[]).push(Date.now());

                // Fetch how much failed login attempts are made in last 24 hours
                const failedAttemptsLast5Minutes = countLastXMinutesDateRecordsFromArray(user.failedLoginAttempts as string[], 24 * 60);

                // Check if there are at least 3 failed attempts in the last 24 hours
                if (failedAttemptsLast5Minutes >= 3) {
                    // Get the timestamps of the last 3 failed attempts
                    const lastThreeAttempts = (user.failedLoginAttempts as number[])
                        .slice(-3)
                        .sort((a, b) => a - b);

                    // Calculate time difference between first and last of the 3 attempts (in seconds)
                    const timeDifferenceInSeconds = (lastThreeAttempts[2] - lastThreeAttempts[0]) / 1000;

                    user.isAccountLocked = true;
                    user.accountLockedAt = Date.now();

                    // If attempts happened within 5 seconds then lock the account as bot otherwise lock the account as normal
                    if (timeDifferenceInSeconds <= 5) {
                        user.lockType = LockTypesEnum.BOT;
                    }
                    else {
                        user.lockType = LockTypesEnum.NORMAL;
                    }
                }

                // Save user details and sync it to v1.
                await user.save({ session: mongoTransaction.session });

                // Update user's details to v1
                await userV2ToV1Sync({
                    user: user.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });

                // Commit the transaction
                await mongoTransaction.commitTransaction();

                // Publish NATS Event
                await userUpdatedPublisherV2Wrapper(user);

                // If account is locked then throw locked error otherwise invalid credentials error
                if(user.isAccountLocked) {
                    // eslint-disable-next-line max-len
                    throw new ResourceLockedError(`Too many failed login attempts detected. Your account has been locked for ${user.lockType === LockTypesEnum.BOT ? "1 hour" : "15 minutes"}. Please try again later <NAME_EMAIL>.`,
                        ResourceLockedErrorCodes.FAILED_LOGIN_ATTEMPT_USER_ACCOUNT_LOCKED
                    );
                }

                throw new InvalidCredentialsError();
            }
            else {
                if(user.failedLoginAttempts.length) {
                    user.failedLoginAttempts = [];

                    // Save user details and sync it to v1.
                    await user.save({ session: mongoTransaction.session });

                    // Update user's details to v1
                    await userV2ToV1Sync({
                        user: user.toObject(),
                        operationType: OperationTypesEnums.UPDATE,
                        session: mongoTransaction.session
                    });

                    // Commit the transaction
                    await mongoTransaction.commitTransaction();

                    // Publish NATS Event
                    await userUpdatedPublisherV2Wrapper(user);
                }
            }

            // Step 5: If MFA Token is not provided then throw internal server error.
            if (!mfaToken) {
                throw new InternalServerError();
            }

            // Step 6: Check whether user has enrolled for any MFA options or not
            const mfaEnrollments = await auth0Service.listUserMFAAuthenticatorsUsingMFAToken(mfaToken);

            // Step 7: If user is not enrolled in any MFA options and provide MFA options
            const isMFAEnabled = Boolean(mfaEnrollments.length);
            const availableMFAOptions: {
                name: string,
                type: string
            }[] = [];
            // If MFA is not enabled then
            if (!isMFAEnabled) {
                // Loop through MFATypesEnum and except recovery-code, add everything
                for (const [type, name] of Object.entries(MFATypesEnum)) {
                    if (name !== MFATypesEnum["recovery-code"]) {
                        availableMFAOptions.push({
                            name,
                            type
                        });
                    }
                }
            }

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Step 7: Send response
            return res.sendResponse({
                mfaToken,
                isMFAEnabled,
                mfaEnrollments,
                availableMFAOptions
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "devices",
                    oldValue: JSON.stringify(oldDevices),
                    newValue: JSON.stringify(user.devices)
                }]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.LoginV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as loginV3Router };
