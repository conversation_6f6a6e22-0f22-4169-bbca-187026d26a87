import express, { NextFunction, Request } from "express";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { TokenTypesEnum } from "../../../../enums/token-types";
import { responseHand<PERSON>, validateRequest } from "@moxfive-llc/common";
import { VerifySetUserPasswordRequestValidation } from "../../../../validations/v3/authentication/verify-set-user-password-request.validation";

const router = express.Router();

router.post(
    "/v3/authentication/setPassword/verify",
    responseHand<PERSON>,
    VerifySetUserPasswordRequestValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            req.action = "VerifySetUserPasswordRequest";

            // Step 2: Verify Token
            const { sessionGUID, u }: { sessionGUID: string, u: string } = req.body;

            await UserServiceV3.verifyToken({
                userId: u,
                token: sessionGUID,
                type: TokenTypesEnum.PASSWORD_SETUP,
                tokenExpiredError: true
            });

            // Step 2: Send Response
            res.status(204).sendResponse();

        }
        catch (error) {
            console.error("Authentication.VerifySetUserPasswordRequestV3.");
            console.error(error);
            next(error);
        }
    }
);

export { router as verifySetUserPasswordRequestV3Router };
