import {
    currentUser,
    hasG<PERSON><PERSON>Action,
    InsufficientPrivilagesError,
    InvalidActionError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { UserV2 } from "../../../../models/v2/users-v2";
import { organizationUserPathParamValidation } from "../../../../validations/general/organization-user-path-params-validation";
import { Auth0Service } from "../../../../services/auth0";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { userDeletedPublisherWrapper } from "../../../../util";

const router = express.Router();

router.delete(
    "/v3/authentication/organizations/:organizationId/users/:userId",
    responseHandler,
    currentUser,
    requireAuth,
    organizationUserPathParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        const mongoTransaction = new MongoTransaction();
        try {
            // Check permissions
            const hasPermission = await hasGlobalAction(req, "RemoveOrganizationUser");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId, userId } = req.params;

            if (userId === req.currentUser?.id) {
                throw new InvalidActionError("You cannot remove yourself from the organization.");
            }

            // Start transaction
            await mongoTransaction.startTransaction();

            // Step 1: Find organization with session
            const organization = await OrganizationV2.findById(organizationId)
                .session(mongoTransaction.session)
                .lean()
                .exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: Find user with session
            const user = await UserV2.findById(userId)
                .session(mongoTransaction.session)
                .exec();
            if (
                !user ||
                !user.organization ||
                !user.organization.id ||
                String(user.organization.id) !== organizationId
            ) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            if(user.externalAuth0LoginUserId) {
                // Get Auth0 access token
                const auth0Service = new Auth0Service(req);
                const { accessToken } = await auth0Service.fetchApplicationAccessToken();

                // Remove user from Auth0
                await auth0Service.removeUser({
                    accessToken,
                    userId: user.externalAuth0LoginUserId
                });
            }

            // Step 3: Delete user with session
            await user.deleteOne({ session: mongoTransaction.session });

            // sync user
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.DELETE,
                session: mongoTransaction.session
            });

            // Commit the transaction
            await mongoTransaction.commitTransaction();

            // publish NATS event
            await userDeletedPublisherWrapper({
                id: user.id
            });

            // Send response after successful commit
            res.sendResponse(
                {
                    meta: {
                        message: "Organization user removed successfully."
                    }
                },
                {
                    targets: [{
                        type: TargetType.USER,
                        details: {
                            id: userId,
                            name: user.name
                        }
                    }],
                    correlation: TargetType.ORGANIZATION,
                    correlationId: organizationId
                }
            );
        }
        catch (error) {
            console.error("Authentication.RemoveOrganizationUser");
            console.error(error);

            // Abort the transaction on error
            await mongoTransaction.abortTransaction();
            next(error);
        }
    }
);

export { router as removeOrganizationUserV3Router };
