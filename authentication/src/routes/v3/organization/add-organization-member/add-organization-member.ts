import express, { NextFunction, Request, Response } from "express";
import {
    BodyInvalidBadRequestError,
    currentUser,
    getUserName,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InternalServerError,
    // hasGlobalAction,
    // InsufficientPrivilagesError,
    MembersAlreadyExistBadRequestError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { organizationPathParamValidation } from "../../../../validations/general/organizaiton-path-params-validation";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { MemberDetail } from "../../../../interfaces/add-user-detail-v3";
import { UserV2, UserV2Doc } from "../../../../models/v2/users-v2";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { Connections } from "../../../../models/connections";
import { generateTemporaryToken, isBusinessEmail, pickFromObject, scryptHash } from "../../../../util";
import { Auth0Service } from "../../../../services/auth0";
import { addOrganizationMemberBodyValidation } from "../../../../validations/v3/organizations/add-organization-member-body.validation";
import { authenticationFields } from "../../../../util/authentication-fields";
import { MakeSectionWiseFieldsFlexibleFields } from "../../../../util/make-section-wise-fields-flexible-fields";
import { userCreatedPublisherV2Wrapper } from "../../../../util/v2/user-created-publisher-wrapper-v2";
import { WelcomeEmailNotificationPublisher } from "../../../../events/publishers/welcome-email-notification";
import { natsWrapper } from "../../../../nats-wrapper";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { queueGroupName } from "../../../../events/queue-group-name";
import { flexibleFieldValidationV2 } from "../../../../util/v2/flexible-field-validation-v2";

const { fields, flexibleFieldsNameKey } = MakeSectionWiseFieldsFlexibleFields(authenticationFields.usersV3);
const router = express.Router();

router.post("/v3/authentication/organizations/:organizationId/users",
    responseHandler,
    currentUser,
    requireAuth,
    organizationPathParamValidation,
    addOrganizationMemberBodyValidation,
    validateRequest,
    flexibleFieldValidationV2(flexibleFieldsNameKey),
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Check user has permission to add users of organization
            const hasPermission = await hasGlobalAction(req, "AddUserToOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };

            /*
            * body:
                    {
                        email: <EMAIL>,
                        displayName: "ABC",
                        ...more
                    }
            */
            req.action = "AddUserToOrganization";

            const userDetail = pickFromObject(req.body, fields.commonBase, false);
            const { organizationId } = req.params;

            // Step 1: Find organization and if it's not present then throw NotFoundError
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: Fetching user on the basis of email provided
            const users = await UserV2.findOne({ email: userDetail.email });

            // Step 3: throw error if user already exist
            if (users) {
                //todo: check error
                throw new MembersAlreadyExistBadRequestError([userDetail.email]);
            }

            const userDetailToBeSaved: MemberDetail = userDetail;
            let usersNeedToBeAdded: MemberDetail | null = null;
            const targetedUser: { type: TargetType, details: { id: string, name: string, email: string } }[] = [];

            // Step 4: If users which are to be added to organization, And that are not present in platform then invite them and add them to the org
            const connection = await Connections.findOne({ "organization.id": organizationId }).lean().exec();
            const domain = userDetail.email.split("@")[1];
            let auth0 = false;

            /* Case 1: If user Org have not established any IdP connection then add to Auth0
            * 1.1: If user has out of band email without any connection
            * 1.2: If user has corporate email without any connection
            */
            if (!connection) {
                auth0 = true;
            }
            // Case 2: If user Org have setup any IdP connection
            else {
                const isCorporateUser = isBusinessEmail(userDetailToBeSaved.email);
                if (isCorporateUser && connection.domains && (connection.domains.length || !connection.domains.includes(domain))) {
                    throw new BodyInvalidBadRequestError([
                        { name: "email", value: userDetail.email, message: `Email domain must be one of the allowed domains (${connection.domains}).` }
                    ]);
                }
                // Case 2.1: If user is Out of band and connection of IdP enforcement is false, then user will be added to auth0
                // Case 2.2: If user is out of band email && enforce true || corporate email && enforce true or false then we will not add that user to auth0, thus email invitation will be sent
                if (!isCorporateUser && !connection.enforceAuthentication) {
                    // console.log("Case 2.1: executed")
                    auth0 = true;
                }
            }

            // If user to be added in auth0 then add it into auth0 database connection
            if (auth0) {
                const auth0Service = new Auth0Service(req);
                const tokenDetail = await auth0Service.fetchApplicationAccessToken();
                const userAuth0Id = await auth0Service.addUser({
                    userEmail: userDetailToBeSaved.email,
                    userDetails: userDetailToBeSaved,
                    organization,
                    accessToken: tokenDetail.accessToken
                });

                userDetailToBeSaved.externalAuth0LoginUserId = userAuth0Id;

                if (!userDetailToBeSaved.externalAuth0LoginUserId) {
                    throw new InternalServerError();
                }
            }

            // Case: Send a link to generate a password and join the MF platform
            const { token, expiry } = generateTemporaryToken({ minutes: 24 * 60 });
            const hashedToken = await scryptHash(token);

            // Update user's Email Verification token with expiry also sync that details with user v1
            userDetailToBeSaved.emailVerificationToken = hashedToken;
            userDetailToBeSaved.emailVerificationTokenExpiry = expiry;
            userDetailToBeSaved.isEmailVerified = false;
            usersNeedToBeAdded = userDetailToBeSaved;

            // Step 5: Adding all the users to db for the Platform
            const userAdded: UserV2Doc | null = usersNeedToBeAdded ? await UserV2.insertUserIfNotExistV3(usersNeedToBeAdded, organization, mongoTransaction.session) : null;

            // Step 6: Commit transaction
            await mongoTransaction.commitTransaction();

            if (userAdded) {
                // Send an email to user
                await new WelcomeEmailNotificationPublisher(natsWrapper.client).publish({
                    email: userAdded.email,
                    name: userAdded.name,
                    url: UserServiceV3.generateWelcomeEmailURL(token, String(userAdded._id)),
                    setPassword: Boolean(usersNeedToBeAdded.externalAuth0LoginUserId),
                    serviceName: queueGroupName
                });

                targetedUser.push({
                    type: TargetType.USER,
                    details: {
                        id: String(userAdded._id),
                        name: getUserName({
                            firstName: userAdded.firstName,
                            lastName: userAdded.lastName,
                            displayName: userAdded.displayName
                        }),
                        email: userAdded.email
                    }
                });
                await userCreatedPublisherV2Wrapper(userAdded);
            }

            // Todo: If user is not added to platform db then remove the user from auth0 if added to auth0

            // Step 7: Send Response
            res.sendResponse({
                meta: {
                    message: "Organization members added successfully."
                },
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    },
                    ...targetedUser
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.AddUserToOrganization");
            console.error(error);
            next(error);
        }

    });

export { router as addOrganizationMembersV3Router };
