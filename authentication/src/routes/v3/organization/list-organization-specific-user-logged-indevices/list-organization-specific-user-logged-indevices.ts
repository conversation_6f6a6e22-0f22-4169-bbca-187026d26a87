import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, hasGlobalAction, InsufficientPrivilagesError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler, validateRequest } from "@moxfive-llc/common";
import { organizationUserPathParamValidation } from "../../../../validations/general/organization-user-path-params-validation";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";

const router = express.Router();

router.get(
    "/v3/authentication/organizations/:organizationId/users/:userId/devices",
    responseHandler,
    currentUser,
    requireAuth,
    organizationUserPathParamValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Check Action
            const hasPermission = await hasGlobalAction(req, "ListUserLoggedinDevicesOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Step 2: Check whether organization and user are valid or not, if not then throw an error
            const { userId, organizationId } = req.params;

            const organization = await OrganizationV2.findById(organizationId, {
                _id: 1
            }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const user = await UserV2.findById(userId, {
                "devices.id": 1, "devices.device": 1, "devices.loginDateTime": 1, organization: 1
            }).lean().exec();

            if (!user || String(user.organization?.id) !== String(organizationId)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Step 4: Send response
            return res.sendResponse({
                totalRows: user.devices.length,
                data: user.devices
            }, {});
        }
        catch (error) {
            console.error("Authentication.ListUserLoggedinDevicesOfOrganization");
            console.error(error);
            next(error);
        }
    }
);

export { router as listUserLoggedinDevicesOfOrganizationV3Router };
