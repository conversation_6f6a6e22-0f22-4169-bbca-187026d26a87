import { currentUser, hasGlobalAction, InsufficientPrivilagesError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { MetaDataConnectionFields } from "../../../../../models/meta-data-connection-fields";
import { MetaDataConnectionFieldsQueryParamValidation } from "../../../../../validations/meta-data-connection-fields.validations";

const router = express.Router();

router.get(
    "/v1/authentication/organizations/connections/fields",
    responseHandler,
    currentUser,
    requireAuth,
    MetaDataConnectionFieldsQueryParamValidation,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasPermission = await hasGlobalAction(req, "ListEnterpriseConnectionTypeFields");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            req.action = "ListEnterpriseConnectionTypeFields";
            // Step 1: Fetch connection type id from query params
            const { connectionTypeId } = req.query;

            // Step 2: Make a DB call to fetch connection type fields associated with connection type
            const fields = await MetaDataConnectionFields.find({ "connectionType.id": String(connectionTypeId), neededInPlatform: true }).sort({ sequence: 1 });

            // Step 3: If not found throw error as connection type fields are not listed to meta data
            if (!fields.length) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_FIELDS_NOT_FOUND, "Connection fields not found.");
            }

            // Step 4: Return fields for the connection type
            res.sendResponse(fields, {});
        }
        catch (error) {
            console.error("Authentication.ListEnterpriseConnectionTypeFields");
            console.error(error);
            next(error);
        }
    }
);

export { router as getConnectionFieldsRouter };
