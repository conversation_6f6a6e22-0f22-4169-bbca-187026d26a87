import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilages<PERSON>rror,
    NotFound<PERSON><PERSON>,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { MetadataConnectionType } from "../../../../../models/metadata-connection-type";

const router = express.Router();

router.get(
    "/v3/authentication/organizations/connections/types",
    responseHandler,
    currentUser,
    requireAuth,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasPermission = await hasGlobalAction(req, "ListEnterpriseConnectionTypes");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            req.action = "ListEnterpriseConnectionTypes";

            const connectionTypes = await MetadataConnectionType.find({ isActive: true });

            // Step 3: If not found throw error as connection type are not listed to meta data
            if (!connectionTypes.length) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_TYPES_NOT_FOUND, "Connection types not found.");
            }

            res.sendResponse(
                {
                    totalRecords: connectionTypes.length,
                    data: connectionTypes,
                },
                {}
            );
        }
        catch (error) {
            console.error("Authentication.ListEnterpriseConnectionTypes");
            console.error(error);
            next(error);
        }
    }
);

export { router as getMetaDataConnectionTypesV3Router };
