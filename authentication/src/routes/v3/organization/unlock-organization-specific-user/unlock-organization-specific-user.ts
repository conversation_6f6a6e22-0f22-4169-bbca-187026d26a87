import express, { Request, NextFunction, Response } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, hasGlobalAction, InsufficientPrivilagesError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler, TargetType,
    validateRequest } from "@moxfive-llc/common";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { organizationUserPathParamValidation } from "../../../../validations/general/organization-user-path-params-validation";

const router = express.Router();

router.put("/v3/authentication/organizations/:organizationId/users/:userId/unlock",
    responseHandler,
    currentUser,
    requireAuth,
    organizationUserPathParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        //  step 1: Start mongo transaction
        const mongoTransaction = new MongoTransaction();
        await mongoTransaction.startTransaction();

        try {
            // Step 2: Check whether user has permission or not
            const hasPermission = await hasGlobalAction(req, "UnlockUserOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Step 3: Check whether organization and user is present or not, if not then throw an error
            const { organizationId, userId } = req.params;

            const organization = await OrganizationV2.findById(organizationId, {
                _id: 1
            }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const user = await UserV2.findById(userId).session(mongoTransaction.session);
            if (!user || String(user.organization?.id) !== String(organizationId)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // step 4: check weather user is not locked
            if(!user.isAccountLocked) {
                return res.sendResponse({
                    meta: {
                        message: "User unlocked successfully."
                    }
                }, {});
            }

            // Step 5: Update user and save it
            user.failedLoginAttempts = [];
            user.isAccountLocked = false;
            user.accountLockedAt = null;
            user.lockType = null;

            await user.save({ session: mongoTransaction.session });

            // commit transaction
            await mongoTransaction.commitTransaction();

            // step 6: send response
            return res.sendResponse({
                meta: {
                    message: "User unlocked successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user.id),
                            name: user.name,
                            email: user.email
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [
                    {
                        target: TargetType.USER,
                        propertyName: "isAccountLocked",
                        oldValue: JSON.stringify(true),
                        newValue: JSON.stringify(false)
                    }
                ]
            });
        }
        catch (error) {
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UnlockUserOfOrganization");
            console.error(error);
            next(error);
        }
    }
);

export { router as unlockOrganizationSpecificUserV3Router };

