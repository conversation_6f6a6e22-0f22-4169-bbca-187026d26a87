import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    File,
    downloadFile,
    ResourceNotFoundError,
    NotFoundCode,
    requireAuth,
    validateRequest,
    hasGlobalAction,
    InsufficientPrivilagesError
} from "@moxfive-llc/common";
import { unlink } from "fs/promises";
import { downloadConnectionFilePathValidation } from "../../../../../validations/downloadConnectionFilePathParam.validation";

const router = express.Router();

router.get("/v1/authentication/connections/files/:fileId",
    currentUser,
    requireAuth,
    downloadConnectionFilePathValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check Permission.
            const hasPermission = await hasGlobalAction(req, "DownloadOrganizationConnectionFile");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { fileId } = req.params;

            // Step 2: Check Connection file id is valid or not
            const fileDetail = await File.findById(fileId).lean().exec();
            if (!fileDetail) {
                throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "Connection file not found.");
            }

            const { filePath, file } = await downloadFile({
                parent: fileDetail.parent,
                parentId: fileDetail.parentId,
                entity: fileDetail.entity,
                entityId: fileDetail.entityId,
                fileId: fileDetail._id as string,
                AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
            });
            res.download(filePath, file.originalFileName, (err) => {
                if (err) {
                    console.error(err);
                }
                // eslint-disable-next-line security/detect-non-literal-fs-filename
                unlink(filePath).then();
            });
        }
        catch (error) {
            console.error("Authentication.DownloadOrganizationConnectionFile");
            console.error(error);
            next(error);
        }
    });

export { router as downloadConnectionFileRouter };
