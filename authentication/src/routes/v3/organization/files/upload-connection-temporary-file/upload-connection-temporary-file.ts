import express, { NextFunction, Request, Response } from "express";
import {
    BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, currentUser,
    requireAuth, InternalServerError, validateFileMagicBytes,
    uploadTemporaryFile,
    responseHandler,
    hasGlobalAction,
    InsufficientPrivilagesError
} from "@moxfive-llc/common";
import { fileUploadedMulter } from "../../../../../util/configure-multer-for-connection-file-upload";
import { TEMPORARY_BUCKET } from "../../../../../util";

const MAX_UPLOAD_COUNT = 1;
const filesInformation = { name: "files" };
// Configure multer
const upload = fileUploadedMulter();

const router = express.Router();

router.post("/v1/authentication/eph-files",
    responseHandler,
    currentUser,
    requireAuth,
    upload.array(filesInformation.name),
    validateFileMagicBytes(filesInformation),
    async (req: Request, res: Response, next: NextFunction): Promise<any> => {
        try {
            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };
            const hasPermission = await hasGlobalAction(req, "UploadTemporaryFiles");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }
            // req.action = "UploadTemporaryFiles";
            if (!req.currentUser) {
                throw new InternalServerError();
            }

            const files: any = req.files;
            if (!Array.isArray(files) || !files.length) {
                return res.send();
            }

            if (files.length > MAX_UPLOAD_COUNT) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.MAX_UPLOAD_FILES,
                    "At a time only one file can be uploaded."
                );
            }

            const failedToUpload: string[] = [];
            const uploadedFileIds: string[] = [];

            const fileUploaded = await uploadTemporaryFile({
                parent: TEMPORARY_BUCKET,
                filePath: files[0].path,
                fileName: files[0].originalname,
                userId: req.currentUser?.id as string,
                AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
            });
            if (!fileUploaded) {
                failedToUpload.push(files[0].originalname);
                uploadedFileIds.push("failed");
                throw new Error(`Failed to upload file: ${files[0].originalname}.`);
            }

            res.sendResponse({
                meta: {
                    fileId: fileUploaded.id,
                    message: "Files uploaded successfully."
                }
            }, {});
        }
        catch (error) {
            console.error("Authentication.UploadTemporaryFiles");
            // console.error(error);
            next(error);
        }
    });

export { router as uploadConnectionEPHTemporaryFilesRouter };
