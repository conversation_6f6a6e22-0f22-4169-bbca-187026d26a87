import {
    createPagination,
    currentUser,
    getPage,
    hasGlobalAction,
    InsufficientPrivilages<PERSON><PERSON>r,
    ListAP<PERSON><PERSON><PERSON><PERSON>,
    PageResponseObj,
    PaginationEntity,
    requireAuth,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";

import { authenticationFields } from "../../../../../util/authentication-fields";
import { SearchIndex } from "../../../../../enums/search-index";
import { fetchModuleFilterFieldsBasedOnPermissionV2 } from "../../../../../util/fetch-module-filter-fields-based-on-permission-v2";
import { isMOXFIVEUser } from "../../../../../util";
import { Connections } from "../../../../../models/connections";
import { getReaminingDaysUsingDate } from "../../../../../util/get-remaining-days-using-date";

const router = express.Router();

const { columnsMapping, filterFieldMapping } = ListAPIHelper.prepareColumnAndFilterFieldMappingsForModule(authenticationFields.connection);
const facetQueryMapping = ListAPIHelper.prepareFacetQueryMappings(authenticationFields.connection);
const sectionWiseFacets = ListAPIHelper.fetchFacetFieldsBySectionsForModule(authenticationFields.connection);

router.get("/v1/authentication/connections",
    responseHandler,
    currentUser,
    requireAuth,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };
            // Step 1: Check user has permission to get all organizations
            const hasPermission = await hasGlobalAction(req, "ListOrganizationConnections");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            isMOXFIVEUser({
                req,
                throwError: true
            });
            req.action = "ListConnections";
            const { search, filter, sort, skipToken } = req.query;

            // Step 2: If we are getting skipToken in request then just fetch the requested page and return the response
            if (skipToken) {
                const response: PageResponseObj | null = await getPage(req, PaginationEntity.LIST_CONNECTIONS);
                return res.sendResponse(response ?? {}, {});
            }

            const sectionWiseActions: { name: string, path: any, action?: string }[] = [
                {
                    name: "connections",
                    path: authenticationFields.connection.commonBase
                },
                {
                    name: "modifiedAt",
                    path: authenticationFields.connection.modifiedAt
                }
            ];

            // Step 3: Fetch the fields by permissions assigned
            const { userColumns, userFacets, userFilters } = ListAPIHelper.prepareUserColumnsFiltersAndFacetsBasedOnPermissions({
                assignedActions: new Set(),
                sectionWiseActions,
                columnsMapping,
                filterFieldMapping,
                facets: sectionWiseFacets
            });

            // Step 4: Prepare the sort queries
            const { atlasSort, regularSort, sortProcessingStages } = ListAPIHelper.prepareUserSortQuery({
                path: authenticationFields.connection, sort: sort as string, userColumns
            });

            // Step 5: Prepare filters based on the filter reqest
            const { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters } = ListAPIHelper.filterParserMust({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });

            const { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters } = ListAPIHelper.filterParserMustNot({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { blankFilters, blankAppliedFilters } = ListAPIHelper.filterParserBlank({ filter: filter as string, fieldMapping: userFilters });

            // Step 6: Prepare applied filters
            const appliedFilters = { ...mustAppliedFilters, ...mustNotAppliedFilters, ...blankAppliedFilters };

            let matchQuery: any = null;
            let facetMatchQuery: any = null;
            if (blankFilters.length || matchFilters.length || matchNotFilters.length) {
                matchQuery = { $and: [...blankFilters, ...matchFilters, ...matchNotFilters] };
            }
            if (blankFilters.length || facetMatchFilters.length || facetMatchNotFilters.length) {
                facetMatchQuery = { $and: [...blankFilters, ...facetMatchFilters, ...facetMatchNotFilters] };
            }

            const commonMust: any[] = [];

            if (search) {
                commonMust.push({
                    autocomplete: {
                        query: search,
                        path: "organization.value",
                        tokenOrder: "sequential"
                    }
                });
            }

            // Step 7: Prepare Data query using fields, filters and sortings
            const dataQuery = ListAPIHelper.prepareDataQuery({
                collection: Connections,
                searchIndex: SearchIndex.CONNECTIONS_DEFAULT,
                must: [...commonMust, ...mustFilters],
                mustNot: mustNotFilters,
                matchQuery,
                projection: userColumns,
                atlasSort,
                regularSort,
                sortProcessingStages
            });

            // Step 8: Prepare facet queries to get counts for all the flexible type fields
            const facetsQuery = ListAPIHelper.prepareFacetQuery({
                collection: Connections,
                searchIndex: SearchIndex.CONNECTIONS_DEFAULT,
                must: [...commonMust, ...mustFiltersForFacets],
                mustNot: mustNotFiltersForFacets,
                facetMatchQuery,
                facetFilter,
                userFacets,
                facetQueryMapping
            });

            // Step 9: Get all the values for flexible type fields
            const fieldPromise = fetchModuleFilterFieldsBasedOnPermissionV2({
                sections: sectionWiseActions,
                assignedActions: new Set()
            });

            const [data, facets, fields] = await Promise.all([dataQuery, facetsQuery, fieldPromise]);

            // Processing days as per needed
            data.forEach(detail => {
                if (detail.clientSecretExpirationDate) {
                    const days = getReaminingDaysUsingDate(detail.clientSecretExpirationDate);
                    detail.clientSecretExpirationDate = days === 1 ? `${days} day left` : `${days} days left`;
                }
                else {
                    detail.clientSecretExpiration = null;
                    delete detail.clientSecretExpirationDate;
                }
            });

            // Step 10: Prepare the flexible field value counts and quick filters
            ListAPIHelper.prepareQuickFilters({ facetsWithFields: facets[0], fields, appliedFilters });

            // Step 11: For applied filters get the selected values
            ListAPIHelper.prepareAppliedFiltersWithValues({ appliedFilters, fields });

            // Step 12: Create pagination and send the response
            const response: PageResponseObj | null = await createPagination(req,
                PaginationEntity.LIST_CONNECTIONS,
                fields,
                appliedFilters,
                data
            );

            if (response) {
                res.sendResponse(response, {});
            }
            res.sendResponse({}, {});
        }
        catch (error) {
            console.error("Authentication.ListOrganizationConnections");
            console.error(error);
            next(error);
        }
    });

export { router as listConnectionsRouter };
