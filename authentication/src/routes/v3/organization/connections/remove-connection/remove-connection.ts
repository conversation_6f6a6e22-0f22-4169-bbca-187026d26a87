import express, { NextFunction, Request, Response } from "express";
import { MongoTransaction } from "../../../../../services/mongo-transaction";
import { Connections } from "../../../../../models/connections";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest,
} from "@moxfive-llc/common";
import { Auth0Service } from "../../../../../services/auth0";
import { OrganizationV2 } from "../../../../../models/v2/oragnizations-v2";
import { isMOXFIVEUser } from "../../../../../util";
import { connectionPathParamValidation } from "../../../../../validations/connection-path-param.validation";

const router = express.Router();

router.delete(
    "/v1/authentication/connections/:connectionId",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    connectionPathParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        const mongoTransaction = new MongoTransaction();

        try {
            // Check permissions
            const hasPermission = await hasGlobalAction(req, "RemoveOrganizationConnection");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Start transaction
            await mongoTransaction.startTransaction();

            // Get connectionId from params
            const { connectionId } = req.params;

            // Verify connection exists
            const connection = await Connections.findById(connectionId).session(mongoTransaction.session);
            if (!connection) {
                throw new ResourceNotFoundError(
                    NotFoundCode.ORGANIZATION_NOT_FOUND,
                    "Connection not found."
                );
            }

            // check organization exist
            const organization = await OrganizationV2.findById(connection.organization.id).lean().exec();
            if(!organization) {
                throw new ResourceNotFoundError(
                    NotFoundCode.ORGANIZATION_NOT_FOUND,
                    "Organization not found."
                );
            }

            if(req.currentUser?.organizationId !== String(organization._id) && !isMOXFIVEUser({ req })) {
                throw new InsufficientPrivilagesError();
            }

            // Get Auth0 access token
            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            // Remove connection from Auth0
            await auth0Service.removeOrganizationConnection({
                accessToken,
                connectionId: connection.externalConnectionId
            });

            // Remove connection from database
            await connection.deleteOne().session(mongoTransaction.session);

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Send success response
            res.sendResponse({
                meta: {
                    message: "Connection removed successfully."
                }
            }, {});
        }
        catch (error) {
            console.error("Connections.RemoveOrganizationConnection");
            console.error(error);
            await mongoTransaction.abortTransaction();
            next(error);
        }
    }
);

export { router as removeConnectionRouter };
