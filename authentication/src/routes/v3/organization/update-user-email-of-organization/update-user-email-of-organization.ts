
import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import {
    currentUser, hasGlobalAction, InsufficientPrivilagesError, MissingParametersError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler,
    TargetType, validateRequest
} from "@moxfive-llc/common";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";
import { organizationUserPathParamValidation } from "../../../../validations/general/organization-user-path-params-validation";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { UpdateUserEmailOfOrganizationValidation } from "../../../../validations/v3/organizations/update-user-email-of-organization.validation";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { EmailChangeNotificationPublisher } from "../../../../events/publishers/email-change-notification";
import { natsWrapper } from "../../../../nats-wrapper";
import { queueGroupName } from "../../../../events/queue-group-name";
import { WelcomeEmailNotificationPublisher } from "../../../../events/publishers/welcome-email-notification";
import { TokenTypesEnum } from "../../../../enums/token-types";

const router = express.Router();

router.put(
    "/v3/authentication/organizations/:organizationId/users/:userId/email",
    responseHandler,
    currentUser,
    requireAuth,
    organizationUserPathParamValidation,
    UpdateUserEmailOfOrganizationValidation,
    validateRequest,

    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1: Check user has permission to update user email
            const hasPermission = await hasGlobalAction(req, "UpdateUserEmailOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Step 2: Check user exist or not, if not then throw an error.
            const { organizationId, userId } = req.params;

            const organization = await OrganizationV2.findById(organizationId, {
                _id: 1
            }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const user = await UserV2.findById(userId).session(mongoTransaction.session);
            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            const { token, email }: { token?: string | null, email: string } = req.body;

            // If logged in user's email address is beeing changed and user has username password authentication
            if(String(req.currentUser?.id) === String(userId) && user.externalAuth0LoginUserId) {
                // If token is not passed then throw an error
                if (!/^.{2,}$/.test(String(token || ""))) {
                    throw new MissingParametersError(["token"]);
                }

                // Chcek token is valid or not, if it is expired then throw an error
                const { tokenExpired } = await UserServiceV3.verifyToken({
                    userId: String(user._id),
                    user,
                    token: token ?? "",
                    type: TokenTypesEnum.ACTION_TOKEN,
                    tokenExpiredError: false,
                    encrypt: true
                });

                if(tokenExpired) {
                    throw new InsufficientPrivilagesError();
                }
            }

            const oldEmail = user.email;
            const devices = [...(user.toJSON()).devices];

            // Step 3: Update User email
            const { url } = await UserServiceV3.changeUserEmail({ user, email, req });

            // Step 4: Save uesr and sync details to v1
            await user.save({ session: mongoTransaction.session });

            // Add user entry in v1 as well
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // If there is any update then publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            try {
                // Send email
                if(user.isEmailVerified) {
                    await new EmailChangeNotificationPublisher(natsWrapper.client).publish({
                        oldEmail: oldEmail,
                        newEmail: email,
                        url,
                        serviceName: queueGroupName,
                        name: user.name,
                    });
                }
                else {
                    await new WelcomeEmailNotificationPublisher(natsWrapper.client).publish({
                        email,
                        name: user.name,
                        url: url ?? "",
                        setPassword: Boolean(user.externalAuth0LoginUserId),
                        serviceName: queueGroupName,
                    });
                }
            }
            catch(error) {
                console.error("Error in sending an email", error);
            }

            // Step 6: Send response
            return res.sendResponse({
                "meta": {
                    "message": "User email updated successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "email",
                    oldValue: JSON.stringify(oldEmail),
                    newValue: JSON.stringify(email)
                }, {
                    target: TargetType.USER,
                    propertyName: "devices",
                    oldValue: JSON.stringify(devices),
                    newValue: JSON.stringify(user.devices)
                }]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateUserEmailOfOrganizationV3");
            console.error(error);
            next(error);
        }
    }
);

export { router as updateUserEmailOfOrganizationV3Router };
