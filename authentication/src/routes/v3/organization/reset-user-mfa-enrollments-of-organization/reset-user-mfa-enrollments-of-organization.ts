import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, hasGlobalAction, InsufficientPrivilagesError, InvalidActionError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler,
    TargetType,
    validateRequest } from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { organizationUserPathParamValidation } from "../../../../validations/general/organization-user-path-params-validation";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { UserServiceV3 } from "../../../../services/v3/user.service";
import { userV2ToV1Sync } from "../../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { userUpdatedPublisherV2Wrapper } from "../../../../util/v2/user-updated-publisher-wrapper";

const router = express.Router();

router.delete(
    "/v3/authentication/organization/:organizationId/users/:userId/mfa/enrollments",
    responseHandler,
    currentUser,
    requireAuth,
    organizationUserPathParamValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        // Step 1: Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Step 1: Check Action
            const hasPermission = await hasGlobalAction(req, "ResetUserMFAEnrollmentsOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Step 2: Check user exist or not, if not then throw an error. Also if user is not from Auth0 then also throw an error
            const { organizationId, userId } = req.params;

            const organization = await OrganizationV2.findById(organizationId, {
                _id: 1
            }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }
            const user = await UserV2.findById(userId);

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            if(!user.externalAuth0LoginUserId) {
                throw new InvalidActionError("Your account is not registered in Auth0. If you believe this is an error, <NAME_EMAIL>.");
            }

            // Step 3:Fetch user MFA enrollments
            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            await auth0Service.removeUserMFAEnrollment({
                accessToken,
                userId: user.externalAuth0LoginUserId
            });

            // Save user details, sync details to v1
            const oldDevices = [...(user.toJSON()).devices];

            UserServiceV3.destroyAllUserActiveSessions(user);
            await user.save({ session: mongoTransaction.session });

            // Add user entry in v1 as well
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // If there is any update then publish NATS Event
            await userUpdatedPublisherV2Wrapper(user);

            // Step 4: Send response
            return res.sendResponse({
                "meta": {
                    "message": "MFA enrollment of user removed successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user._id),
                            name: user.name,
                            email: user.email,
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "devices",
                    oldValue: JSON.stringify(oldDevices),
                    newValue: JSON.stringify(user.devices)
                }]
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.ResetUserMFAEnrollmentsOfOrganizationV3");
            console.error(error);
            next(error);
        }
    }
);

export { router as resetUserMFAEnrollmentsOfOrganizationV3Router };
