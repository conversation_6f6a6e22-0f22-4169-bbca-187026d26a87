import express, { Request, NextFunction } from "express";
import { UserV2 } from "../../../../models/v2/users-v2";
import { currentUser, hasGlobalAction, InsufficientPrivilagesError, InvalidActionError, NotFoundCode, requireAuth, ResourceNotFoundError, responseHandler,
    validateRequest } from "@moxfive-llc/common";
import { Auth0Service } from "../../../../services/auth0";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { organizationUserPathParamValidation } from "../../../../validations/general/organization-user-path-params-validation";

const router = express.Router();

router.get(
    "/v3/authentication/organizations/:organizationId/users/:userId/mfa/enrollments",
    responseHandler,
    currentUser,
    requireAuth,
    organizationUserPathParamValidation,
    validateRequest,
    async (req: Request, res: any, next: NextFunction) => {
        try {
            // Step 1: Check Action
            const hasPermission = await hasGlobalAction(req, "ListUserMFAEnrollmentsOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Step 2: Check user exist or not, if not then throw an error. Also if user is not from Auth0 then also throw an error
            const { organizationId, userId } = req.params;

            const organization = await OrganizationV2.findById(organizationId, {
                _id: 1
            }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const user = await UserV2.findById(userId, {
                externalAuth0LoginUserId: 1
            }).lean().exec();

            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            if (!user.externalAuth0LoginUserId) {
                throw new InvalidActionError("Your account is not registered in Auth0. If you believe this is an error, <NAME_EMAIL>.");
            }

            // Step 3:Fetch user MFA enrollments
            const auth0Service = new Auth0Service(req);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            const mfaEnrollments = await auth0Service.listUserMFAEnrollments({
                accessToken,
                userId: user.externalAuth0LoginUserId
            });

            // Step 4: Send response
            return res.sendResponse(mfaEnrollments, {});
        }
        catch (error) {
            console.error("Authentication.ListUserMFAEnrollmentsOfOrganizationV3");
            console.error(error);
            next(error);
        }
    }
);

export { router as listUserMFAEnrollmentsOfOrganizationV3Router };
