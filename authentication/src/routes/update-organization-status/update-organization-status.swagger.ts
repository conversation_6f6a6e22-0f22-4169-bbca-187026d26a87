/**
 * @swagger
 * /v1/organizations/status:
 *   put:
 *     summary: Update organization status
 *     description: Update organization status.
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type : string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                isEnabled:
 *                  type: boolean
 *                organizations:
 *                  type: array
 *                  items:
 *                    type: string
 *                  example: ["62397c3b020359bf2f084267", "62397c3b020359bf2f084268"]
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                  {
 *                    "meta": {
 *                      "message": "Organizations status updated successfully"
 *                    }
 *                  }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *                   { "errors":
 *                     [
 *                       {"message": "isEnabled must be valid", "field": "isEnabled"},
 *                       {"message": "Organizations must be valid array with min 1 element", "field": "organizations"}
 *                     ]
 *                   }
 *       404:
 *         description: Organization not found
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Please provide valid organization IDs"} ] }
 */
