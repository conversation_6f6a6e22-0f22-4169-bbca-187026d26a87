// /* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
// import express, { NextFunction, Request, Response } from "express";
// import {
//     currentUser, hasGlobalAction, InsufficientPrivilagesError, InvalidActionError, NotFoundCode, requireAuth,
//     ResourceNotFoundError, responseHandler, SucceededPartially, TargetType, validateRequest
// } from "@moxfive-llc/common";
// import { Organization, OrganizationDoc } from "../../models/organization";
// import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
// import { updateOrganizationStatusValidation } from "./update-organization-status.validation";

// const router = express.Router();

// router.put("/v1/organizations/status",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     updateOrganizationStatusValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // Check user has permission to toggle organization status
//             const hasPermission = await hasGlobalAction(req, "UpdateOrganizationStatus");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { isEnabled }: { isEnabled: boolean } = req.body;
//             const { organizations: organizationIds }: { organizations: string[] } = req.body;

//             if (organizationIds.includes(process.env.MOXFIVE_ID as string)) {
//                 throw new InsufficientPrivilagesError();
//             }

//             if (organizationIds.includes(req.currentUser?.organizationId!)) {
//                 throw new InvalidActionError("You cannot change status of your own organization.");
//             }

//             // Step 1: Find organizations and if some organization not found then throw error
//             const organizations = await Organization.find({ _id: { $in: organizationIds } });

//             if (!organizations || (organizations && !organizations.length)) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organizations not found.");
//             }

//             // Prepare user details map
//             const orgDetailsMap = new Map();
//             organizations.forEach(org => {
//                 orgDetailsMap.set(String(org._id), org.name);
//             });

//             const validOrganizations: OrganizationDoc[] = [];
//             const inValidOrganizationsIds: string[] = [];
//             const orgIds: string[] = organizations.map(org => org.id);

//             organizationIds.forEach(orgId => {
//                 if (orgIds.includes(orgId)) {
//                     validOrganizations.push(organizations.find(org => org.id === orgId)!);
//                 }
//                 else {
//                     inValidOrganizationsIds.push(orgId);
//                 }
//             });

//             // Step 2: If organization status is not same as provided status then update status
//             const updatedOrgIds: string[] = [];
//             await Promise.all(validOrganizations.map(async organization => {
//                 if (organization.id !== process.env.MOXFIVE_ID && organization.isEnabled !== isEnabled) {
//                     organization.isEnabled = isEnabled;
//                     await organization.save();
//                     await OrganizationUpdatedPublisherWrapper(organization);

//                     updatedOrgIds.push(organization.id);
//                 }
//             }));

//             if (inValidOrganizationsIds.length) {
//                 const errors = [{
//                     parameters: [{
//                         attributes: inValidOrganizationsIds,
//                         message: "Orgnizations do not exist."
//                     }]
//                 }];
//                 throw new SucceededPartially(errors, "One or more organizations status failed to update.");
//             }

//             // Step 3: Send Response
//             res.sendResponse({
//                 meta: {
//                     message: "Organizations status updated successfully."
//                 }
//             }, updatedOrgIds.length ? {
//                 targets: updatedOrgIds.map(org => {
//                     const orgName = orgDetailsMap.get(String(org));
//                     return {
//                         type: TargetType.ORGANIZATION,
//                         details: {
//                             id: org,
//                             name: orgName || ""
//                         }
//                     };
//                 }),
//                 modifiedProperties: [{
//                     target: TargetType.ORGANIZATION,
//                     propertyName: "isEnabled",
//                     oldValue: JSON.stringify(!isEnabled),
//                     newValue: JSON.stringify(isEnabled)
//                 }]
//             } : {});
//         }
//         catch (error) {
//             console.error("Authentication.UpdateOrganizationStatus");
//             console.error(error);
//             next(error);
//         }
//     });

// export { router as updateOrganizationStatusRouter };
