import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../util";

export const updateOrganizationSpecificUserStatusValidation = [
    param("organizationId")
        .exists().bail()
        .isMongoId(),

    body("users")
        .isArray({ min: 1, max: 20 }).withMessage("At a given time max 20 users status can be updated.").bail()
        .custom((users: string[]) => {
            return users.every(user => {
                return isValidMongoObjectId(user);
            });
        }).withMessage("These user ids are invalid."),

    body("isEnabled")
        .isBoolean({ loose: false })
        .toBoolean()
        .withMessage("isEnabled must be boolean.")
];
