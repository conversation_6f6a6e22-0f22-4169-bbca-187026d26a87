/**
 * @swagger
 * /v1/organizations/{organizationId}/users/status:
 *   put:
 *     summary: Update users status of organization
 *     description: Update users status of organization
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type : string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                isEnabled:
 *                  type: boolean
 *                users:
 *                  type: array
 *                  items:
 *                    type: string
 *                  example: ["62397c3b020359bf2f084267", "62397c3b020359bf2f084268"]
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                  {
 *                    "meta": {
 *                      "message": "Users status updated successfully"
 *                    }
 *                  }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       {"message": "isEnabled must be valid", "field": "isEnabled"},
 *                       {"message": "Users must be valid array with 1-20 elements", "field": "users"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"},
 *                     ]
 *                   }
 *               Third:
 *                 summary: User doesn't exist in the provided organization
 *                 description: User doesn't exist in the provided organization
 *                 value:
 *                   { "errors": [ { "message": "All the provided users doesn't exist in the organization"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             examples:
 *                First:
 *                  summary: Organization not found
 *                  description: Organization not found
 *                  value:
 *                    { "errors": [ { "message": "Organization not found"} ] }
 *                Second:
 *                  summary: User not found
 *                  description: User not found
 *                  value:
 *                    { "errors": [ { "message": "Please provide valid user IDs"} ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: None of users status updated
 *                 description: None of users status updated
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 *               Second:
 *                 summary: Partial users status updated
 *                 description: Partial users status updated
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "<EMAIL> status didn't updated, please try again"
 *                          }
 *                      ]
 *                  }
 *               Third:
 *                 summary: Internal server error.
 *                 description: Internal server error.
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 */
