import { body, param } from "express-validator";

export const addOrganizationMembersValidation = [
    param("organizationId")
        .exists().bail()
        .isMongoId().withMessage("Organization id must be valid"),

    body("members")
        .isArray({ min: 1, max: 20 }).withMessage("At a given time max 20 users can be added into an organization."),

    body("members.*.id")
        .exists().bail()
        .isString().trim().blacklist("<>").withMessage("Member ID must be string."),

    body("members.*.mail")
        .exists().bail()
        .isEmail().normalizeEmail().withMessage("Email is invalid."),

    body("members.*.displayName")
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 256 }).withMessage("Display name can only contain letters, dashes, numbers, spaces, dot and can be of max 256 characters long."),

    body("members.*.givenName")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Given name must be string."),

    body("members.*.surname")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Suranme must be string."),

    body("members.*.streetAddress")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Street address must be string."),

    body("members.*.country")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Country must be string."),

    body("members.*.state")
        .optional()
        .isString().trim().blacklist("<>").withMessage("State must be string."),

    body("members.*.city")
        .optional()
        .isString().trim().blacklist("<>").withMessage("City must be string."),

    body("members.*.postalCode")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Postal code must be valid US zip."),

    body("members.*.businessPhones")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Phone number must be valid US number."),

    body("members.*.accountEnabled")
        .optional()
        .isBoolean({ loose: false }).toBoolean().withMessage("Account enabled status must be boolean."),

    body("members.*.jobTitle")
        .optional()
        .isString().trim().blacklist("<>").withMessage("Job Title must be string."),
];
