import {
    createPagination,
    currentUser,
    getPage,
    hasGlobalAction,
    InsufficientPrivilages<PERSON>rror,
    ListAP<PERSON><PERSON><PERSON><PERSON>,
    PageResponseObj,
    PaginationEntity,
    requireAuth,
    response<PERSON>andler,
    SanitizedTypesEnum,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";

import { getAllOrganizationsValidation } from "./get-all-organizations.validation";
import { authenticationFields } from "../../../util/authentication-fields";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { SearchIndex } from "../../../enums/search-index";
import { fetchModuleFilterFieldsBasedOnPermissionV2 } from "../../../util/fetch-module-filter-fields-based-on-permission-v2";
import { Connections } from "../../../models/connections";

const router = express.Router();

const { columnsMapping, filterFieldMapping } = ListAPIHelper.prepareColumnAndFilterFieldMappingsForModule(authenticationFields.organizationsV2);
const facetQueryMapping = ListAPIHelper.prepareFacetQueryMappings(authenticationFields.organizationsV2);
const sectionWiseFacets = ListAPIHelper.fetchFacetFieldsBySectionsForModule(authenticationFields.organizationsV2);

// If any sanitized field is present, then define the field details here. Otherwise the filtering and sorting won't work.
const sanitizedFields = [
    { type: SanitizedTypesEnum.EMAIL, fields: ["hotlineEmail", "billingContactEmail"] },
    { type: SanitizedTypesEnum.URL, fields: ["website"] }
];

function checkConnection(organizationId: string, connections: Record<string, boolean>) {
    return connections[String(organizationId)] ?? false;
}

router.get("/v2/authentication/organizations",
    responseHandler,
    currentUser,
    requireAuth,
    getAllOrganizationsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            // Step 1: Check user has permission to get all organizations
            const hasPermission = await hasGlobalAction(req, "ListOrganizations");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { search, filter, sort, skipToken } = req.query;

            // Step 2: If we are getting skipToken in request then just fetch the requested page and return the response
            if (skipToken) {
                const response: PageResponseObj | null = await getPage(req, PaginationEntity.LIST_ORGANIZATIONS);
                return res.sendResponse(response ?? {}, {});
            }

            const sectionWiseActions: { name: string, path: any, action?: string }[] = [
                {
                    name: "organizations",
                    path: authenticationFields.organizationsV2.commonBase
                },
                {
                    name: "modifiedAt",
                    path: authenticationFields.modifiedAt
                }
            ];

            // Fetch connections
            const connections = await Connections.find({}, { organization: 1 }).lean().exec();
            const connnectionsObj: Record<string, boolean> = {};
            connections.forEach((connection) => {
                connnectionsObj[String(connection.organization.id)] = true;
            });

            const optionalQuery: { [key: string]: any }[] = [
                {
                    "$addFields": {
                        "connection": {
                            "$function": {
                                "body": checkConnection,
                                "args": [{ $toString: "$_id" }, { $literal: connnectionsObj }],
                                "lang": "js"
                            }
                        }
                    }
                }
            ];

            let filters: string | null = null;
            if (filter) {
                // eslint-disable-next-line security/detect-unsafe-regex
                const filtersArr = (filter as string).split(/(?<=^([^']|'[^']*')*) and /gm);
                let filterStringConnection: string | null = null;
                for (const filter of filtersArr) {
                    if (filter.includes("connection")) {
                        filterStringConnection = filter;
                    }
                }
                filters = filtersArr.filter(filterStr => !filterStr.includes("connection")).join(" and ");
                if (filterStringConnection) {
                    optionalQuery.push({
                        "$match":
                        {
                            "connection": filterStringConnection.includes("true") ? true : false
                        }
                    });
                }
            }

            // Step 3: Fetch the fields by permissions assigned
            const { userColumns, userFacets, userFilters } = ListAPIHelper.prepareUserColumnsFiltersAndFacetsBasedOnPermissions({
                assignedActions: new Set(),
                sectionWiseActions,
                columnsMapping,
                filterFieldMapping,
                facets: sectionWiseFacets
            });

            // Step 4: Prepare the sort queries
            const sortQueryResult = ListAPIHelper.prepareUserSortQuery({
                path: authenticationFields.organizationsV2,
                sort: sort as string, userColumns
            });

            // CASE: Here, there are many fields which are not present in all the document and if we apply atlas sort there then it will add that field in each document which will cause Heap Out Of Memory. So, that's why will remove atlas sort and keep regular sort only
            const { sortProcessingStages } = sortQueryResult;
            let { atlasSort, regularSort } = sortQueryResult;

            if (atlasSort && Object.keys(atlasSort).length) {
                regularSort = { ...atlasSort, ...regularSort };
                atlasSort = undefined;

            }

            // Step 5: Prepare filters based on the filter reqest
            const { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters } = ListAPIHelper.filterParserMust({
                filter: filters as string, fieldMapping: userFilters, facetFilterMapping: userFacets, sanitizedFields
            });

            const { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters } = ListAPIHelper.filterParserMustNot({
                filter: filters as string, fieldMapping: userFilters, facetFilterMapping: userFacets, sanitizedFields
            });
            const { blankFilters, blankAppliedFilters } = ListAPIHelper.filterParserBlank({ filter: filters as string, fieldMapping: userFilters });

            // Step 6: Prepare applied filters
            const appliedFilters = { ...mustAppliedFilters, ...mustNotAppliedFilters, ...blankAppliedFilters };

            let matchQuery: any = null;
            let facetMatchQuery: any = null;
            if (blankFilters.length || matchFilters.length || matchNotFilters.length) {
                matchQuery = { $and: [...blankFilters, ...matchFilters, ...matchNotFilters] };
            }
            if (blankFilters.length || facetMatchFilters.length || facetMatchNotFilters.length) {
                facetMatchQuery = { $and: [...blankFilters, ...facetMatchFilters, ...facetMatchNotFilters] };
            }

            const commonMust: any[] = [];

            if (search) {
                commonMust.push({
                    autocomplete: {
                        query: search,
                        path: "name",
                        tokenOrder: "sequential"
                    }
                });
            }

            // Step 7: Prepare Data query using fields, filters and sortings
            const dataQuery = ListAPIHelper.prepareDataQuery({
                collection: OrganizationV2,
                searchIndex: SearchIndex.ORGANIZATIONS_DEFAULT,
                must: [...commonMust, ...mustFilters],
                mustNot: mustNotFilters,
                matchQuery,
                projection: userColumns,
                atlasSort,
                regularSort,
                sortProcessingStages,
                optionalQuery: optionalQuery ?? {}
            });

            // Step 8: Prepare facet queries to get counts for all the flexible type fields
            const facetsQuery = ListAPIHelper.prepareFacetQuery({
                collection: OrganizationV2,
                searchIndex: SearchIndex.ORGANIZATIONS_DEFAULT,
                must: [...commonMust, ...mustFiltersForFacets],
                mustNot: mustNotFiltersForFacets,
                facetMatchQuery,
                facetFilter,
                userFacets,
                facetQueryMapping
            });

            // Step 9: Get all the values for flexible type fields
            const fieldPromise = fetchModuleFilterFieldsBasedOnPermissionV2({
                sections: sectionWiseActions,
                assignedActions: new Set()
            });

            const [data, facets, fields] = await Promise.all([dataQuery, facetsQuery, fieldPromise]);
            const fieldsDetail = fields.filter(field => !(field.name === "connection"));

            // Step 10: Prepare the flexible field value counts and quick filters
            ListAPIHelper.prepareQuickFilters({ facetsWithFields: facets[0], fields: fieldsDetail, appliedFilters });

            // Step 11: For applied filters get the selected values
            ListAPIHelper.prepareAppliedFiltersWithValues({ appliedFilters, fields: fieldsDetail });

            // Step 12: Create pagination and send the response
            const response: PageResponseObj | null = await createPagination(req,
                PaginationEntity.LIST_ORGANIZATIONS,
                fieldsDetail,
                appliedFilters,
                data
            );

            if (response) {
                return res.sendResponse(response, {});
            }
            return res.sendResponse({}, {});
        }
        catch (error) {
            console.error("Authentication.GetAllOrganizationsV2");
            console.error(error);
            next(error);
        }
    });

export { router as getAllOrganizationsV2Router };
