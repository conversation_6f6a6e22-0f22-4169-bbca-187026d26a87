import { param, body } from "express-validator";

export const changeOrganizationMembershipValidation = [
    param("organizationId")
        .isMongoId(),

    body("user")
        .exists().bail()
        .isMongoId().withMessage("User not found"),

    body("membershipType")
        .exists().bail()
        .custom(value => {
            if (!["owner", "member"].includes(value)) {
                throw new Error("Membership type can be any from this list only: owner, member.");
            }
            else {
                return true;
            }
        })
];
