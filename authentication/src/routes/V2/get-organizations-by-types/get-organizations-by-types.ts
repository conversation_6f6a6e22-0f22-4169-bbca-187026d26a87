import {
    currentUser, hasGlobalAction,
    InsufficientPrivilages<PERSON>rror,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    setActions,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { OrganizationType } from "../../../models/organization-type";
import { PlatformDashboard } from "../../../models/platform-dashboard";
import { Widget } from "../../../models/widget";
import { intersectArrays } from "../../../util";
import { colorMappings } from "../../../util/color-mappings";
import { widgetPathParamValidations } from "./widget-path-param.validation";

const sortSequence = ["Client", "Privacy Counsel", "Insurance Carrier", "Forensics", "Recovery", "Negotiator",
    "Monitoring Counsel", "Insurance Broker", "Payment Facilitator", "Other"];

const router = express.Router();

router.get(
    "/v2/authentication/dashboards/widgets/:widgetId/organizationsByTypes",
    responseHand<PERSON>,
    currentUser,
    requireAuth,
    widgetPathParamValidations,
    validateRequest,
    setActions,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            await hasGlobalAction(req, "WidgetOrganizationsByTypesDetail");

            const { widgetId } = req.params;
            const userId = req.currentUser?.id || null;
            const assignedPermissions = req.actions as string[];

            // Validate widget
            const isValidWidget = await Widget.findOne({ _id: widgetId, name: "OrganizationsbyType" }, { _id: 1, actions: 1 }).lean().exec();
            if (!isValidWidget) {
                throw new ResourceNotFoundError(NotFoundCode.WIDGET_NOT_FOUND, "Widget not found.");
            }

            // Check user has a permission
            const intersectedArray = intersectArrays(isValidWidget.actions, assignedPermissions);
            if (intersectedArray.length !== isValidWidget.actions.length) {
                throw new InsufficientPrivilagesError();
            }

            // Check user dashboard exists
            const dashboardDetails = await PlatformDashboard.findOne({ userId }, { platformWidgets: 1 }).lean().exec();
            if (!dashboardDetails) {
                throw new ResourceNotFoundError(NotFoundCode.DASHBOARD_NOT_FOUND, "Dashboard not found");
            }

            // Check user dashboard has widget
            if (!dashboardDetails.platformWidgets.map(String).includes(widgetId)) {
                throw new ResourceNotFoundError(NotFoundCode.WIDGET_NOT_FOUND, "Widget not found.");
            }

            const organizationsByTypes: { name: string, value: number, _id: string }[] = await OrganizationType.aggregate([
                {
                    $lookup: {
                        from: "organizationsv2",
                        let: {
                            orgTypeId: "$_id",
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {
                                                $isArray: "$organizationTypes",
                                            },
                                            {
                                                $in: [
                                                    "$$orgTypeId",
                                                    "$organizationTypes.id",
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                        as: "organizationByTypes",
                    },
                },
                {
                    $addFields: {
                        value: {
                            $size: "$organizationByTypes",
                        },
                    },
                },
                {
                    $project: {
                        name: 1,
                        value: 1,
                    },
                },
            ]);

            // Prepare response
            const validOrganizationsByTypes = organizationsByTypes.filter(elem => Boolean(elem.value));
            const availableTypes = validOrganizationsByTypes.map(elem => elem.name);
            const sortSequenceOnAvailables = intersectArrays(sortSequence, availableTypes);
            const colorArray = colorMappings[sortSequenceOnAvailables.length];
            const preparedResp: any = [];
            sortSequenceOnAvailables.forEach((type: any, index: number) => {
                const organizationByType = validOrganizationsByTypes.find(elem => elem.name === type);
                if (organizationByType) {
                    preparedResp.push({
                        id: String(organizationByType._id),
                        name: organizationByType.name,
                        value: organizationByType.value,
                        ...colorArray[+index]
                    });
                }
            });

            res.sendResponse(preparedResp, {});
        }
        catch (error) {
            console.error("Authentication.GetOrganizationsByTypes");
            console.error(error);
            next(error);
        }
    }
);

export { router as getOrganizationsByTypesRouter };
