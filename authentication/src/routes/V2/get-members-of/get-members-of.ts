import { currentUser, requireA<PERSON>, response<PERSON><PERSON><PERSON>, validateRequest } from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { getMembersOfValidation } from "./get-members-of.validation";
import { UserV2 } from "../../../models/v2/users-v2";

const router = express.Router();

router.post(
    "/v2/authentication/users/membersOf",
    responseH<PERSON><PERSON>,
    currentUser,
    requireAuth,
    getMembersOfValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "GetUserMembershipDetail";

            const { users }: { users: string[]  } = req.body;

            // Step 1: Fetch details of the user
            const usersDetails = await UserV2.find({ azureId: { $in: users } });

            // Step 2: map user azure Id and it's organization Id
            const userMemberOf = new Map();
            usersDetails.forEach(user => {
                userMemberOf.set(user.azureId, user.organization?.id ?? null);
            });

            // Step 3: Map user with it's organization id
            const membersOf = users.map(user => {
                return {
                    user,
                    organization: userMemberOf.get(user) ?? null
                };
            });

            // Step 4: Send response
            res.sendResponse(membersOf, {});
        }
        catch (error) {
            console.error("Authentication.GetMembersOfV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as getMembersOfV2Router };
