import { booleanValidator, mongoIDValidator } from "../../../util/express-validator-wrapper";

export const logoAndFaviconParamValidation = [
    ...mongoIDValidator([
        {
            name: "organizationId",
            param: true,
            message: "Organizaiton ID must be valid."
        }
    ]),
    ...booleanValidator([
        {
            name: "imageAndLogoSame",
            query: true,
            message: "imageAndLogoSame must be boolean."
        }
    ])
];
