// import express, { NextFunction, Request, Response } from "express";
// import {
//     currentUser,
//     deleteFile,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     validateRequest,
//     File
// } from "@moxfive-llc/common";
// import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
// import { logoAndFaviconParamValidation } from "./delete-logo-and-favicon.validations";
// import { OrganizationUpdatedPublisherWrapperV2 } from "../../../util/v2/organization-updated-publisher-wrapper";
// import { MongoTransaction } from "../../../services/mongo-transaction";
// import { organizationV2ToV1Sync } from "../../../util/organization-v2-to-v1-sync";
// import { OperationTypesEnums } from "../../../enums/operation-types.enum";
//
// const router = express.Router();
//
// router.delete("/v2/authentication/organizations/:organizationId/upload/logo",
//     currentUser,
//     requireAuth,
//     logoAndFaviconParamValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         // Start Mongo Transaction
//         const mongoTransaction = new MongoTransaction();
//         mongoTransaction.startTransaction();
//
//         try {
//             const hasPermission = await hasGlobalAction(req, "RemoveProfileIcon");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }
//
//             const { organizationId } = req.params;
//             const { entity } = req.query as { entity: string };
//
//             const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }
//
//             const fileDetails = await File.findOne({
//                 parent: organization.bucketName,
//                 parentId: organizationId,
//                 entity: `${(entity ?? "").toLowerCase()}`,
//                 entityId: organizationId,
//             }).lean().exec();
//
//             if (!fileDetails) {
//                 // TODO ERROR
//                 throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "File not found.");
//             }
//
//             await deleteFile({
//                 parent: organization.bucketName as string,
//                 parentId: organizationId,
//                 entity: fileDetails.entity.toLowerCase(),
//                 entityId: organizationId,
//                 fileId: fileDetails._id as string,
//                 AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_CDN_STORAGE_CONNECTION_STRING
//             });
//
//             if (entity.toLowerCase() === "profile") {
//                 organization.profile = null;
//                 if (organization.imageAndLogoSame) {
//                     organization.favicon = null;
//                     organization.imageAndLogoSame = false;
//                 }
//             }
//             else if (entity.toLowerCase() === "favicon") {
//                 organization.favicon = null;
//             }
//
//             await organization.save({ session: mongoTransaction.session });
//
//             await organizationV2ToV1Sync({
//                 organization: organization.toObject(),
//                 operationType: OperationTypesEnums.UPDATE,
//                 session: mongoTransaction.session
//             });
//
//             // Commit transaction
//             await mongoTransaction.commitTransaction();
//
//             await OrganizationUpdatedPublisherWrapperV2(organization);
//
//             res.json({
//                 meta: {
//                     message: "File has been deleted successfully."
//                 }
//             });
//         }
//         catch (error) {
//             // Abort transaction
//             await mongoTransaction.abortTransaction();
//
//             console.error("Authentication.RemoveOrganizationProfileImageV2");
//             console.error(error);
//             next(error);
//         }
//     });
//
// export { router as removeLogoOrFaviconV2Router };
