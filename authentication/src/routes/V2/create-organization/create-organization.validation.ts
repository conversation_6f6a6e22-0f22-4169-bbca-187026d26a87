/* eslint-disable max-len */
import { OrganizationFields } from "../../../services/organization-fields";
import { body } from "express-validator";
import { flexibleFieldReqBodyValidation } from "../../../util/incident-flexible-field-req-body-validation";
import { authenticationFields } from "../../../util/authentication-fields";

export const createOrganizationValidation = [
    // Company Information
    body("name")
        .if(OrganizationFields.fieldValidator)
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 100 }).withMessage("Name can only contain letters, dashes, numbers, spaces, dot and can be of max 100 characters long."),

    body("website")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 250 })
        .isURL().withMessage("Website must be a valid URL and can be of max 250 characters long."),

    body("highLevelCompanyInformation")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ max: 2000 }).withMessage("High level company information can be of max 2000 characters long."),

    body("descriptionOfEnvironment")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ max: 2000 }).withMessage("Description of environment can be of max 2000 characters long."),

    body("officeLocations")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isArray().withMessage("Office locations must be array"),

    body("officeLocations.*.addressline1")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 128 }).withMessage("Office Locations addressline1 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."),

    body("officeLocations.*.addressline2")
        .if(body("officeLocations").exists())
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 128 }).withMessage("Office Locations addressline2 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."),

    body("officeLocations.*.city")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Office Locations city can only contain letters, spaces, and can be of max 64 characters long."),

    body("officeLocations.*.cityShortName")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Office Locations city short name can only contain letters and can be of max 64 characters long."),

    body("officeLocations.*.state")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Office Locations state can only contain letters, spaces, and can be of max 64 characters long."),

    body("officeLocations.*.stateShortName")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Office Locations state short name can only contain letters and can be of max 64 characters long."),

    body("officeLocations.*.zip")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isPostalCode("US").withMessage("Office Locations zip must be valid US zip."),

    body("officeLocations.*.country")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Office Locations country can only contain letters, spaces, and can be of max 64 characters long."),

    body("officeLocations.*.countryShortName")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Office Locations country short name can only contain letters and can be of max 64 characters long."),

    body("officeLocations.*.latitude")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isFloat({ gt: -90.1, lt: 90.1 }).trim()
        .withMessage("Office Locations latitude should be valid and range between -90 to 90 degrees"),

    body("officeLocations.*.longitude")
        .if(body("officeLocations").exists())
        .exists().bail()
        .isFloat({ gt: -180.1, lt: 180.1 }).trim()
        .withMessage("Office Locations longitude should be valid and range between -180 to 180 degrees"),

    body("numberOfEmployees")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isInt({ min: 0 }).toInt()
        .isLength({ max: 16 }).withMessage("Number of employees must be positive number and of max 16 digits long."),

    body("numberOfITStaff")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isInt({ min: 0 }).toInt()
        .isLength({ max: 16 }).withMessage("Number of It Staff must be positive number and of max 16 digits long."),

    body("itStaffLocation")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isArray().withMessage("IT Staff Locations must be array."),

    body("itStaffLocation.*.addressline1")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 128 }).withMessage("IT Staff Locations addressline1 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."),

    body("itStaffLocation.*.addressline2")
        .if(body("itStaffLocation").exists())
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 128 }).withMessage("IT Staff Locations addressline2 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."),

    body("itStaffLocation.*.city")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("IT Staff Locations city can only contain letters, spaces, and can be of max 64 characters long."),

    body("itStaffLocation.*.cityShortName")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("IT Staff Locations city short name can only contain letters and can be of max 64 characters long."),

    body("itStaffLocation.*.state")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("IT Staff Locations state can only contain letters, spaces, and can be of max 64 characters long."),

    body("itStaffLocation.*.stateShortName")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("IT Staff Locations state short name can only contain letters and can be of max 64 characters long."),

    body("itStaffLocation.*.zip")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isPostalCode("US").withMessage("IT Staff Locations zip must be valid US zip."),

    body("itStaffLocation.*.country")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("IT Staff Locations country can only contain letters, spaces, and can be of max 64 characters long."),

    body("itStaffLocation.*.countryShortName")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("IT Staff Locations country short name can only contain letters and can be of max 64 characters long."),

    body("itStaffLocation.*.latitude")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isFloat({ gt: -90.1, lt: 90.1 }).trim()
        .withMessage("IT Staff Locations latitude should be valid and range between -90 to 90 degrees"),

    body("itStaffLocation.*.longitude")
        .if(body("itStaffLocation").exists())
        .exists().bail()
        .isFloat({ gt: -180.1, lt: 180.1 }).trim()
        .withMessage("IT Staff Locations longitude should be valid and range between -180 to 180 degrees"),

    body("activePartner")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isBoolean({ loose: false }).toBoolean().withMessage("Active Partner must be boolean."),

    // Hotline details
    body("moxfiveHotline")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isBoolean({ loose: false }).toBoolean().withMessage("MOXFIVE Hotline must be boolean."),

    body("hotlineEmail")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isEmail().normalizeEmail().withMessage("MOXFIVE Hotline Email must be valid."),

    body("hotlinePhoneNumber")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isMobilePhone("en-US").withMessage("MOXFIVE Hotline Phone Number must be valid US number."),

    // Contact Details
    body("msaSignatureDate")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isISO8601().withMessage("MSA signature date must be valid ISO-8601 date."),

    body("billingContactName")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ max: 50 }).withMessage("Billing Contact Name can only contain letters, dashes, numbers, spaces, dot, and can be of max 50 characters long."),

    body("billingContactEmail")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isEmail().normalizeEmail().withMessage("Billing Contact Email must be valid."),

    body("billingContactPhone")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isMobilePhone("en-US").withMessage("Billing contact phone must be valid US number"),

    body("billingAddresses")
        .if(OrganizationFields.fieldValidator)
        .optional({ nullable: true })
        .isArray().withMessage("Billing addresses must be array"),

    body("billingAddresses.*.addressline1")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 128 }).withMessage("Billing addressline1 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."),

    body("billingAddresses.*.addressline2")
        .if(body("billingAddresses").exists())
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 128 }).withMessage("Billing addressline2 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."),

    body("billingAddresses.*.city")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Billing city can only contain letters, spaces, and can be of max 64 characters long."),

    body("billingAddresses.*.cityShortName")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Billing city short name can only contain letters and can be of max 64 characters long."),

    body("billingAddresses.*.state")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Billing state can only contain letters, spaces, and can be of max 64 characters long."),

    body("billingAddresses.*.stateShortName")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Billing state short name can only contain letters and can be of max 64 characters long."),

    body("billingAddresses.*.zip")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isPostalCode("US").withMessage("Billing zip must be valid US zip."),

    body("billingAddresses.*.country")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Billing country can only contain letters, spaces, and can be of max 64 characters long."),

    body("billingAddresses.*.countryShortName")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 64 }).withMessage("Billing country short name can only contain letters and can be of max 64 characters long."),

    body("billingAddresses.*.latitude")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isFloat({ gt: -90.1, lt: 90.1 }).trim()
        .withMessage("Billing Addresses latitude should be valid and range between -90 to 90 degrees"),

    body("billingAddresses.*.longitude")
        .if(body("billingAddresses").exists())
        .exists().bail()
        .isFloat({ gt: -180.1, lt: 180.1 }).trim()
        .withMessage("Billing Addresses longitude should be valid and range between -180 to 180 degrees"),

    // Partner details
    body("onboardedDate")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isISO8601().withMessage("Onboarded date must be valid ISO-8601 date."),

    body("partnerEula")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 1000 })
        .isURL().withMessage("Partner EULA must be valid URL."),

    body("partnerTermsConditions")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 250 })
        .isURL().withMessage("Partner terms & conditions must be valid URL."),

    body("inboundRequestInfo")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isEmail().normalizeEmail().withMessage("Inbound request info must be valid email."),

    body("numberOfPMs")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isInt({ min: 1 }).toInt()
        .isLength({ max: 16 }).withMessage("Number of PMs must be positive number and of max 16 digits long."),

    body("numberOfLeads")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isInt({ min: 1 }).toInt()
        .isLength({ max: 16 }).withMessage("Number of Leads must be positive number and of max 16 digits long."),

    body("numberOfEngineers")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isInt({ min: 1 }).toInt()
        .isLength({ max: 16 }).withMessage("Number of Engineers must be positive number and of max 16 digits long."),

    body("moxfivePMSponsor")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isMongoId().withMessage("MOXFIVE PM Sponsor must be valid MOXFIVE user."),

    body("moxfiveTASponsor")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isMongoId().withMessage("MOXFIVE TA Sponsor must be valid MOXFIVE user."),

    body("moxfiveSalesSponsor")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isMongoId().withMessage("MOXFIVE Sales Sponsor must be valid MOXFIVE user."),

    body("shortDescription")
        .if(OrganizationFields.fieldValidator)
        .if(OrganizationFields.canSendPartnerDetailsField)
        .optional({ nullable: true })
        .isString().trim().blacklist("<>")
        .isLength({ max: 2000 }).withMessage("Short Description can be of max 2000 characters long."),

    ...flexibleFieldReqBodyValidation(
        authenticationFields.organizations
            .filter((f) => f.flexibleField && !f.partnerField)
            .map((f) => {
                return {
                    name: f.name,
                    minValuesLength: f.required ? 1 : 0,
                    ifConditions: [OrganizationFields.fieldValidator],
                    nullable: true
                };
            })
    ),

    ...flexibleFieldReqBodyValidation(
        authenticationFields.organizations
            .filter((f) => f.flexibleField && f.partnerField)
            .map((f) => {
                return {
                    name: f.name,
                    minValuesLength: f.required ? 1 : 0,
                    ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
                    nullable: true
                };
            })
    )
];
