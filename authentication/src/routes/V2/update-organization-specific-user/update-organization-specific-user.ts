// import express, { NextFunction, Request, Response } from "express";
// import {
//     validateRequest,
//     currentUser,
//     requireAuth,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     ResourceNotFoundError,
//     NotFoundCode,
//     generateSearchKeys,
//     responseHandler,
//     TargetType,
//     InvalidActionError
// } from "@moxfive-llc/common";

// import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
// import { UpdateUserDetailsParams } from "../../../interfaces";
// import {
//     getSearchFields, getUserName,
//     intersectTwoObjects,
//     isMOXFIVEUser,
//     pickFromObject
// } from "../../../util";
// import { updateOrganizationSpecificUserValidation } from "./update-organization-specific-user.validation";
// import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
// import { UserV2 } from "../../../models/v2/users-v2";
// import { userUpdatedPublisherV2Wrapper } from "../../../util/v2/user-updated-publisher-wrapper";
// import { AuditLogV2 } from "../../../services/v2/audit-log";
// import { flexibleFieldValidationV2 } from "../../../util/v2/flexible-field-validation-v2";
// import { MakeSingleSectionFieldsFlexibleFields } from "../../../util/make-single-section-fields-flexible-fields";
// import { authenticationFields } from "../../../util/authentication-fields";
// import { MongoTransaction } from "../../../services/mongo-transaction";
// import { userV2ToV1Sync } from "../../../util/user-v2-to-v1-sync";
// import { OperationTypesEnums } from "../../../enums/operation-types.enum";

// const router = express.Router();
// const { flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.users);

// router.put(
//     "/v2/authentication/organizations/:organizationId/users/:userId",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     updateOrganizationSpecificUserValidation,
//     validateRequest,
//     flexibleFieldValidationV2(flexibleFieldsNameKey),
//     async (req: Request, res: Response, next: NextFunction) => {
//         // Start Mongo Transaction
//         const mongoTransaction = new MongoTransaction();
//         mongoTransaction.startTransaction();

//         try {
//             // req.currentUser = {
//             //     id: "62397c3b020359bf2f08426a",
//             //     email: "<EMAIL>",
//             //     organizationId: "62305427f5c54aaa68d33901",
//             //     userName: "Romit Gandhi",
//             //     isSystemUser: false
//             // };
//             // Step 1: Check user has permission to update user profile of organization
//             const hasPermission = await hasGlobalAction(req, "UpdateUserProfileOfAnOrganization");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { organizationId, userId } = req.params;
//             const isMoxfiveUser = isMOXFIVEUser({
//                 req,
//                 throwError: false
//             });

//             // Step 2: If provided organization id moxfive then throw error
//             if (organizationId === process.env.MOXFIVE_ID) {
//                 throw new InvalidActionError("This operation is not allowed.");
//             }

//             // Step 3: If role property has been provided and user is not moxfive user then throw an error
//             if (req.body.hasOwnProperty("role") && !isMoxfiveUser) {
//                 throw new InsufficientPrivilagesError();
//             }

//             // Step 4: If organization not found then throw NotFoundError
//             const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session).lean().exec();
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }

//             // Step 5: If user is not found then throw NotFoundError
//             const user = await UserV2.findById(userId).session(mongoTransaction.session);
//             if (!user) {
//                 throw new ResourceNotFoundError(NotFoundCode["USER_NOT_FOUND"], "User not found.");
//             }

//             // Step 6: Check if provided user is moxfive user, if it is then throw error
//             const isProvidedUserMoxfiveUser = isMOXFIVEUser({
//                 req,
//                 throwError: false,
//                 organizationId: user.organization?.id
//             });

//             if (isProvidedUserMoxfiveUser) {
//                 throw new InsufficientPrivilagesError();
//             }

//             // Step 7: Check user has been in the organization or not
//             const isMember = organization.owner.includes(user.azureId) || organization.member.includes(user.azureId);
//             if (!isMember) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
//             }

//             // Step 8: Fetch data only which are updated
//             const userData: UpdateUserDetailsParams = pickFromObject(
//                 user,
//                 ["firstName", "lastName", "displayName", "userLocation", "officePhone", "jobTitle", "role"]
//             );

//             const updatedData: UpdateUserDetailsParams = intersectTwoObjects(userData, req.body);

//             // Step 9: If there is no data updated then return
//             if (Object.keys(updatedData).length === 0) {
//                 // Abort transaction
//                 await mongoTransaction.abortTransaction();

//                 return res.sendResponse({
//                     meta: {
//                         message: "User updated successfully"
//                     }
//                 }, {});
//             }

//             // Step 10:  Get Microsoft Access Token and fetch update user details
//             const accessToken = await microsoftGraphAPI.getAccessToken();
//             await microsoftGraphAPI.updateUserDetails({ accessToken, azureId: user.azureId, ...updatedData });

//             // Step 11: If firstName, lastName or displayName updated then update the name & regenerate keys
//             if (updatedData.firstName || updatedData.lastName || updatedData.displayName) {
//                 // Get username
//                 updatedData.name = getUserName({
//                     firstName: updatedData.firstName ?? user.firstName,
//                     lastName: updatedData.lastName ?? user.lastName,
//                     displayName: updatedData.displayName ?? user.displayName,
//                 });

//                 // Generate keys and save it
//                 const searchFields = getSearchFields(
//                     updatedData.firstName || user.firstName,
//                     updatedData.lastName || user.lastName,
//                     updatedData.displayName || user.displayName,
//                     user.email
//                 );
//                 const searchKeys = generateSearchKeys(searchFields);
//                 user.keys = searchKeys as string[];
//             }

//             // Step 12: Save user details
//             Object.assign(user, updatedData);

//             await user.save({ session: mongoTransaction.session });

//             // Add user entry in v1 as well
//             await userV2ToV1Sync({
//                 user: user.toObject(),
//                 operationType: OperationTypesEnums.UPDATE,
//                 session: mongoTransaction.session
//             });

//             // Commit transaction
//             await mongoTransaction.commitTransaction();

//             await userUpdatedPublisherV2Wrapper(user);

//             // Step 13: Fetch modified properties for audit log
//             const data = { ...updatedData };
//             const oldData = { ...userData };

//             const modifiedProperties = AuditLogV2.prepareModifiedProperties({
//                 data,
//                 oldData,
//                 target: TargetType.USER
//             });

//             // Step 14: Send response
//             res.sendResponse({
//                 meta: {
//                     message: "User updated successfully."
//                 }
//             }, {
//                 targets: [
//                     {
//                         type: TargetType.USER,
//                         details: {
//                             id: String(user.id),
//                             name: user.name,
//                             email: user.email,
//                             azureId: user.azureId
//                         }
//                     }
//                 ],
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: String(user.organization?.id),
//                 modifiedProperties
//             });
//         }
//         catch (error) {
//             // Abort transaction
//             await mongoTransaction.abortTransaction();

//             console.error("Authentication.UpdateOrganizationSpecificUserV2");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as updateOrganizationSpecificUserV2Router };
