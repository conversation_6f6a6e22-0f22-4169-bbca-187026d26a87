// import express, { NextFunction, Request, Response } from "express";
// import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
// import {
//     OrganizationAzureUpdates,
//     OrganizationAzureUpdatesDoc,
// } from "../../../models/organization-azure-updates";
// import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
// import { OrganizationUpdatedPublisherWrapperV2 } from "../../../util/v2/organization-updated-publisher-wrapper";
// import { MongoTransaction } from "../../../services/mongo-transaction";
// import { OperationTypesEnums } from "../../../enums/operation-types.enum";
// import { organizationV2ToV1Sync } from "../../../util/organization-v2-to-v1-sync";

// interface orgDetailsObj {
//   data: {
//     displayName: string;
//   };
// }

// const router = express.Router();

// router.post(
//     "/v2/authentication/groupupdate/callback",
//     async (req: Request, res: Response, next: NextFunction):Promise<any> => {
//         let mongoTransaction: MongoTransaction | null = null;

//         try {
//             req.action = "AzureGroupsUpdatesWebhook";

//             const { validationToken } = req.query;
//             const { value } = req.body;

//             if (validationToken) {
//                 return res.set("content-type", "text/plain").send(validationToken);
//             }

//             if (Array.isArray(value) && value.length) {
//                 // Send 202 (Accepted) response to acknowledge microsoft azure
//                 res.status(202).send();

//                 // Start Mongo Transaction
//                 mongoTransaction = new MongoTransaction();
//                 mongoTransaction.startTransaction();

//                 // Prepare the data and save it in the db
//                 const preparedObj: any = [];
//                 value.map((item: any) => {
//                     if (
//                         !process.env.CLIENT_STATE || item.clientState === process.env.CLIENT_STATE
//                     ) {
//                         preparedObj.push({
//                             organizationId: item?.resourceData["id"],
//                             changeType: item?.changeType,
//                             status: "Not Started",
//                         });
//                     }
//                 });

//                 await OrganizationAzureUpdates.insertMany(preparedObj);

//                 // Once data is inserted, get organization which are yet to be updated
//                 const notStartedOrganization = await OrganizationAzureUpdates.find({
//                     status: "Not Started",
//                 });
//                 // Get unique records to process them
//                 const uniqueRecords: OrganizationAzureUpdatesDoc[] = [];
//                 notStartedOrganization.forEach((organization) => {
//                     if (
//                         !uniqueRecords.find(
//                             (record: OrganizationAzureUpdatesDoc) =>
//                                 record.organizationId === organization.organizationId
//                         )
//                     ) {
//                         uniqueRecords.push(organization);
//                     }
//                 });

//                 // Change the started records to in progress
//                 await Promise.all(
//                     notStartedOrganization.map(async (organization) => {
//                         organization.status = "In Progress";
//                         await organization.save();
//                     })
//                 );

//                 await Promise.all(
//                     uniqueRecords.map(async (organization) => {
//                         try {
//                             // Check user exist in our system, if user don't exists then delete the user azure updates colleciton details
//                             const oldOrgDetails = await OrganizationV2.findOne({
//                                 azureId: organization.organizationId,
//                             }).session(mongoTransaction?.session ?? null);
//                             if (!oldOrgDetails) {
//                                 await OrganizationAzureUpdates.deleteMany({
//                                     organizationId: organization.organizationId,
//                                     status: "In Progress",
//                                 });
//                                 return false;
//                             }

//                             // If user is valid then update user details
//                             const accessToken = await microsoftGraphAPI.getAccessToken();
//                             const newOrganizationDetails: orgDetailsObj = await microsoftGraphAPI.getAzureOrganizationDetailsById({
//                                 accessToken,
//                                 organizationId: organization.organizationId,
//                             });

//                             if (
//                                 newOrganizationDetails.data.displayName && oldOrgDetails.name !== newOrganizationDetails.data.displayName
//                             ) {
//                                 oldOrgDetails.name = newOrganizationDetails.data.displayName;
//                                 await oldOrgDetails?.save({ session: mongoTransaction?.session ?? null });

//                                 // Sync in Organization V1
//                                 await organizationV2ToV1Sync({
//                                     organization: organization.toObject(),
//                                     operationType: OperationTypesEnums.UPDATE,
//                                     session: mongoTransaction?.session ?? null
//                                 });

//                                 // Commit transaction
//                                 await mongoTransaction?.commitTransaction();

//                                 await OrganizationUpdatedPublisherWrapperV2(oldOrgDetails!);
//                             }

//                             // Delete the records after update
//                             await OrganizationAzureUpdates.deleteMany({
//                                 organizationId: organization.organizationId,
//                                 status: "In Progress",
//                             });
//                         }
//                         catch (err) {
//                             // Abort transaction
//                             if(mongoTransaction) {
//                                 await mongoTransaction.abortTransaction();
//                             }

//                             if (err instanceof Error) {
//                                 await OrganizationAzureUpdates.updateMany(
//                                     {
//                                         organizationId: organization.organizationId,
//                                         status: "In Progress",
//                                     },
//                                     { $set: { errorMessage: err.message, status: "Not Started" } }
//                                 );
//                             }

//                             // Send an email notification
//                         }
//                     })
//                 );
//             }
//         }
//         catch (error) {
//             // Abort transaction
//             if(mongoTransaction) {
//                 await mongoTransaction.abortTransaction();
//             }
//             console.error("Authetication.OrganizationUpdateCallbackV2");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as organizationUpdateCallbackV2Router };
