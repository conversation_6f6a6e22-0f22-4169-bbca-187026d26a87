import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    requireAuth,
    responseHand<PERSON>
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { Policy } from "../../../models/policy";
import { UserV2 } from "../../../models/v2/users-v2";

const router = express.Router();

router.get(
    "/v2/authentication/am/summary",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };

            // Check if user has a ability to get access management summary
            const hasPermission = await hasGlobalAction(req, "GetAccessManagementSummary");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const organizationsCount = await OrganizationV2.countDocuments();
            const usersCount = await UserV2.countDocuments();
            const policiesCount = await Policy.countDocuments();

            res.sendResponse({
                organizations: organizationsCount,
                users: usersCount,
                policies: policiesCount
            }, {});
        }
        catch(error) {
            console.error("Authentication.GetAccessManagementSummaryV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as accessManagementSummaryV2Router };
