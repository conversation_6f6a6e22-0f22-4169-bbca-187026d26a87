import express, { NextFunction, Request, Response } from "express";
import {
    currentUser, hasGlobalAction, InsufficientPrivilagesError, InternalServerError, InvalidActionError, NotFoundCode, requireAuth,
    ResourceNotFoundError, responseHandler, SucceededPartially, TargetType, validateRequest
} from "@moxfive-llc/common";
import { updateOrganizationStatusValidation } from "./update-organization-status.validation";
import { OrganizationV2, OrganizationV2Doc } from "../../../models/v2/oragnizations-v2";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../../util/v2/organization-updated-publisher-wrapper";
import { MongoTransaction } from "../../../services/mongo-transaction";
import { Organization } from "../../../models/organization";

const router = express.Router();

router.put("/v2/authentication/organizations/status",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    updateOrganizationStatusValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            // Step 1: Check user has permission
            const hasPermission = await hasGlobalAction(req, "UpdateOrganizationStatus");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            if(!req.currentUser) {
                throw new InternalServerError();
            }

            const { isEnabled }: { isEnabled: boolean } = req.body;
            const { organizations: organizationIds }: { organizations: string[] } = req.body;

            if (organizationIds.includes(process.env.MOXFIVE_ID as string)) {
                throw new InsufficientPrivilagesError();
            }

            if (organizationIds.includes(req.currentUser.organizationId)) {
                throw new InvalidActionError("You cannot change status of your own organization.");
            }

            // Step 2: Find organizations and if some organization not found then throw error
            const organizations = await OrganizationV2.find({ _id: { $in: organizationIds } }).session(mongoTransaction.session);
            if (!organizations || (organizations && !organizations.length)) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organizations not found.");
            }

            // Prepare user details map
            const orgDetailsMap = new Map();
            organizations.forEach(org => {
                orgDetailsMap.set(String(org._id), org.name);
            });

            const validOrganizations: OrganizationV2Doc[] = [];
            const inValidOrganizationsIds: string[] = [];
            const orgIds: string[] = organizations.map(org => org.id);

            organizationIds.forEach(orgId => {
                if (orgIds.includes(orgId)) {
                    validOrganizations.push(organizations.find(org => org.id === orgId)!);
                }
                else {
                    inValidOrganizationsIds.push(orgId);
                }
            });

            // Step 3: If organization status is not same as provided status then update status
            const updatedOrgIds: string[] = [];
            await Promise.all(validOrganizations.map(async organization => {
                if (organization.isEnabled !== isEnabled) {
                    organization.isEnabled = isEnabled;
                    await organization.save({ session: mongoTransaction.session });
                    await Organization.findByIdAndUpdate(
                        String(organization._id),
                        { isEnabled: Boolean(isEnabled), version: Number(organization.version) },
                        { session: mongoTransaction.session }
                    );
                    updatedOrgIds.push(organization.id);
                }
            }));

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish update organization update
            await Promise.all(validOrganizations.map(async organization => {
                await OrganizationUpdatedPublisherWrapperV2(organization);
            }));

            if (inValidOrganizationsIds.length) {
                const errors = [{
                    parameters: [{
                        attributes: inValidOrganizationsIds,
                        message: "Orgnizations do not exist."
                    }]
                }];
                throw new SucceededPartially(errors, "One or more organizations status failed to update.");
            }

            // Step 4: Send Response
            res.sendResponse({
                meta: {
                    message: "Organizations status updated successfully."
                }
            }, updatedOrgIds.length ? {
                targets: updatedOrgIds.map(org => {
                    const orgName = orgDetailsMap.get(String(org));
                    return {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: org,
                            name: orgName || ""
                        }
                    };
                }),
                modifiedProperties: [{
                    target: TargetType.ORGANIZATION,
                    propertyName: "isEnabled",
                    oldValue: JSON.stringify(!isEnabled),
                    newValue: JSON.stringify(isEnabled)
                }]
            } : {});
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateOrganizationStatusV2");
            console.error(error);
            next(error);
        }
    });

export { router as updateOrganizationStatusV2Router };
