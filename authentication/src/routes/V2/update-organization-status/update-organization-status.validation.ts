import { body } from "express-validator";
import { isValidMongoObjectId } from "../../../util";

export const updateOrganizationStatusValidation = [
    body("organizations")
        .isArray({ min: 1 }).withMessage("Organizations must be valid array with min 1 element").bail()
        .custom((organizations: string[]) => {
            return organizations.every(org => {
                return isValidMongoObjectId(org);
            });
        }).withMessage("These organization ids are invalid."),

    body("isEnabled")
        .isBoolean({ loose: false })
        .toBoolean()
        .withMessage("isEnabled must be valid")
];
