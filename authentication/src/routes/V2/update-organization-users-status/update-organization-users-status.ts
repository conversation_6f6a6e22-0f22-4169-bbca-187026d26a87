// import express, { NextFunction, Request, Response } from "express";
// import {
//     currentUser,
//     ExternalServerError,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     InternalServerError,
//     InvalidActionError,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     responseHandler,
//     SucceededPartially, TargetType,
//     validateRequest
// } from "@moxfive-llc/common";
// import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
// import { updateOrganizationSpecificUserStatusValidation } from "./update-organization-users-status.validation";
// import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
// import { UserV2, UserV2Doc } from "../../../models/v2/users-v2";
// import { userUpdatedPublisherV2Wrapper } from "../../../util/v2/user-updated-publisher-wrapper";
// import { MongoTransaction } from "../../../services/mongo-transaction";
// import { User } from "../../../models/user";

// const router = express.Router();

// router.put("/v2/authentication/organizations/:organizationId/users/status",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     updateOrganizationSpecificUserStatusValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         // Start Mongo Transaction
//         const mongoTransaction = new MongoTransaction();
//         mongoTransaction.startTransaction();

//         try {
//             // req.currentUser = {
//             //     id: "62397c3b020359bf2f08426a",
//             //     email: "<EMAIL>",
//             //     organizationId: "62305427f5c54aaa68d33901",
//             //     userName: "Romit Gandhi",
//             //     isSystemUser: false
//             // };
//             // Step 1: Check user has permission
//             const hasPermission = await hasGlobalAction(req, "UpdateUsersStatusOfAnOrganization");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { isEnabled, users: userIds }: { isEnabled: boolean, users: string[] } = req.body;
//             const { organizationId } = req.params;

//             if (!req.currentUser) {
//                 throw new InternalServerError();
//             }

//             if (userIds.includes(req.currentUser.id)) {
//                 throw new InvalidActionError("You cannot change status of your own account");
//             }

//             // Step 2: If organization not found then throw NotFoundError
//             const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session).lean().exec();
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }

//             // Step 3: If any user is not found then throw NotFoundError
//             const users = await UserV2.find({ _id: { $in: userIds } }).session(mongoTransaction.session);
//             if (!users || (users && !users.length)) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
//             }

//             // Prepare user details map
//             const userDetailsMap = new Map();
//             users.forEach(user => {
//                 userDetailsMap.set(String(user._id), {
//                     id: String(user._id),
//                     name: user.name,
//                     email: user.email,
//                     azureId: user.azureId
//                 });
//             });

//             // Step 3: Check user has been in the organization or not and filter out users who are valid andn whose status is not same as provided
//             const inValidUsers: string[] = [];
//             const validUsers: UserV2Doc[] = [];

//             // Loop throuh all userIds
//             userIds.forEach(userId => {
//                 // If userDetailsMap has that user
//                 if (userDetailsMap.has(userId)) {
//                     // Fetch user from users
//                     const user = users.find(user => user.id === userId);

//                     // If user record is found and user is part of organization then
//                     if (user && (organization.owner.includes(user.azureId!) || organization.member.includes(user.azureId!))) {
//                         // If user's status is different then provided
//                         if (user.isEnabled !== isEnabled) {
//                             validUsers.push(user);
//                         }
//                     }
//                     // Add entry in inValidUsers
//                     else {
//                         inValidUsers.push(userId);
//                     }
//                 }
//                 // Add entry in inValidUsers
//                 else {
//                     inValidUsers.push(userId);
//                 }
//             });

//             // If all of the users are inValidUsers then throw an error
//             if (inValidUsers.length === userIds.length) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
//             }

//             const updatedUsers: UserV2Doc[] = [];
//             const failedUsers: any = [];

//             // Step 4: If there are any users needs to be updated in azure, update in azure as well user doc
//             if (validUsers.length > 0) {
//                 const validUsersAzureIds = validUsers.map(user => user.azureId!);

//                 // Update user status in azure
//                 const accessToken = await microsoftGraphAPI.getAccessToken();
//                 const updateStatusResponse = await microsoftGraphAPI.updateMultipleUsersStatus({ accessToken, users: validUsersAzureIds, status: isEnabled });

//                 // Filter out which user's status updated and which didn't
//                 validUsers.forEach(user => {
//                     if (updateStatusResponse[user.azureId!].status) {
//                         updatedUsers.push(user);
//                     }
//                     else {
//                         failedUsers.push(updateStatusResponse[user.azureId!].body?.error);
//                     }
//                 });

//                 // If no user is updated then throw internal server error
//                 if (failedUsers.length === userIds.length) {
//                     throw new ExternalServerError(failedUsers);
//                 }

//                 // Update users status
//                 await Promise.all(updatedUsers.map(async user => {
//                     user.isEnabled = isEnabled;
//                     await user.save({ session: mongoTransaction.session });
//                     await User.findByIdAndUpdate(String(user._id), { isEnabled: Boolean(isEnabled), version: Number(user.version) }, { session: mongoTransaction.session });
//                 }));

//                 // Commit transaction
//                 await mongoTransaction.commitTransaction();

//                 // Update publish user update
//                 await Promise.all(updatedUsers.map(async user => {
//                     await userUpdatedPublisherV2Wrapper(user);
//                 }));
//             }

//             // Step 5: If partial users got updated then throw error with email ids
//             const errors = [];
//             if (inValidUsers.length) {
//                 errors.push({
//                     attributes: inValidUsers,
//                     message: "Users do not exist."
//                 });
//             }

//             if (failedUsers.length) {
//                 failedUsers.forEach((err: any) => {
//                     errors.push(err);
//                 });
//             }

//             if (errors.length) {
//                 throw new SucceededPartially([{
//                     parameters: errors
//                 }], "One or more users status failed to update.");
//             }

//             // Step 6: Send Response
//             res.sendResponse({
//                 meta: {
//                     message: "Users status updated successfully"
//                 }
//             }, updatedUsers.length ? {
//                 targets: updatedUsers.map(user => {
//                     const userDetails = userDetailsMap.get(user.id);
//                     return {
//                         type: TargetType.USER,
//                         details: userDetails || {}
//                     };
//                 }),
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: organizationId,
//                 modifiedProperties: [{
//                     target: TargetType.USER,
//                     propertyName: "isEnabled",
//                     oldValue: JSON.stringify(!isEnabled),
//                     newValue: JSON.stringify(isEnabled)
//                 }]
//             } : {});
//         }
//         catch (error) {
//             // Abort transaction
//             await mongoTransaction.abortTransaction();

//             console.error("Authentication.UpdateOrganizationSpecificUserStatusV2");
//             console.error(error);
//             next(error);
//         }
//     });

// export { router as updateOrganizationUsersStatusV2Router };
