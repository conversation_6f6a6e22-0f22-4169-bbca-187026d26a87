import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import { GetOrganizationSpecificUserResponse } from "../../../interfaces";
import { getOrganizationSpecificUserValidation } from "./get-organization-specific-user.validation";
import { UserV2 } from "../../../models/v2/users-v2";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";

const router = express.Router();

router.get("/v2/authentication/organizations/:organizationId/users/:userId",
    responseHandler,
    currentUser,
    requireAuth,
    getOrganizationSpecificUserValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to read users of organization
            const hasPermission = await hasGlobalAction(req, "GetOrganizationUserDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId, userId } = req.params;

            // Step 1: If organization not found then throw NotFoundError
            const organization = await OrganizationV2.findById(organizationId).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: If user is not found then throw NotFoundError
            const user = await UserV2.findById(userId, {
                "email": 1,
                "displayName": 1,
                "isEnabled": 1,
                "firstName": 1,
                "lastName": 1,
                "userLocation": 1,
                "officePhone": 1,
                "jobTitle": 1,
                "organizationId": 1,
                "role": 1,
                "lastSignIn": 1,
                "createdAt": 1,
                "updatedAt": 1,
                "name": 1,
                "organization": 1,
                // "isOwner": 1,
                "isEmailVerified": 1,
                "isAccountSetupDone": 1,
                "isAccountLocked": 1,
                "externalAuth0LoginUserId": 1
            });

            // If user not found or user's organization is not same as provided organization then throw NotFoundError
            if (!user || String(user.organization?.id) !== String(organization._id)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Step 3: Send response
            const response: GetOrganizationSpecificUserResponse = {
                ...user.toJSON(),
                // isOwner: user.isOwner ?? undefined,
                _id: user._id as string,
                organization: user.organization as unknown as { id: string; name: string; favicon: string | null } | undefined,
                passwordAuthentication: Boolean(user.externalAuth0LoginUserId)
            };

            response.organization = {
                id: organization._id as string,
                name: organization.name,
                favicon: organization.favicon ?? null
            };

            delete response.externalAuth0LoginUserId;
            // delete response.azureId;

            res.sendResponse(response, {});
        }
        catch (error) {
            console.error("Authentication.GetOrganizationSpecificUserV2");
            console.error(error);
            next(error);
        }
    });

export { router as getOrganizationSpecificUserV2Router };
