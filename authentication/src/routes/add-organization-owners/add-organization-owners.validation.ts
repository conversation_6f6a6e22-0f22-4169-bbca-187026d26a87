// import { body, param } from "express-validator";

// export const addOrganizationOwnersValidation = [
//     param("organizationId")
//         .exists().bail()
//         .isMongoId().withMessage("Organization id must be valid"),

//     body("owners")
//         .isArray({ min: 1, max: 20 }).withMessage("At a given time max 20 owners can be added into an organization."),

//     body("owners.*.id")
//         .exists().bail()
//         .isString().blacklist("<>").withMessage("Owner ID must be string."),

//     body("owners.*.mail")
//         .exists().bail()
//         .isEmail().normalizeEmail().withMessage("Email is invalid."),

//     body("owners.*.displayName")
//         .exists().bail()
//         .isString().trim().blacklist("<>").isLength({ min: 1, max: 256 }).withMessage("Display name can only contain letters, dashes, numbers, spaces, dot and can be of max 256 characters long."),

//     body("owners.*.givenName")
//         .optional()
//         .isString().blacklist("<>").withMessage("Given name must be string."),

//     body("owners.*.surname")
//         .optional()
//         .isString().blacklist("<>").withMessage("Suranme must be string."),

//     body("owners.*.otherMails")
//         .optional()
//         .isArray().withMessage("Other Mails must not be empty."),

//     body("owners.*.streetAddress")
//         .optional()
//         .isString().blacklist("<>").withMessage("Street address must be string."),

//     body("owners.*.country")
//         .optional()
//         .isString().blacklist("<>").withMessage("Country must be string."),

//     body("owners.*.state")
//         .optional()
//         .isString().blacklist("<>").withMessage("State must be string."),

//     body("owners.*.city")
//         .optional()
//         .isString().blacklist("<>").withMessage("City must be string."),

//     body("owners.*.postalCode")
//         .optional()
//         .isString().blacklist("<>").withMessage("Postal code must be valid US zip."),

//     body("owners.*.businessPhones")
//         .optional()
//         .isString().blacklist("<>").withMessage("Phone number must be valid US number."),

//     body("owners.*.accountEnabled")
//         .optional()
//         .isBoolean({ loose: false }).toBoolean().withMessage("Account enabled status must be boolean."),

//     body("owners.*.jobTitle")
//         .optional()
//         .isString().trim().blacklist("<>").withMessage("Job Title must be string."),

//     body("owners.*.companyName")
//         .optional()
//         .isString().trim().blacklist("<>").withMessage("Company Name must be string."),
// ];
