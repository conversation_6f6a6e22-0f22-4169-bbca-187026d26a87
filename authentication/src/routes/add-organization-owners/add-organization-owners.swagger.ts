// /**
//  * @swagger
//  * /v1/organizations/{organizationId}/owners:
//  *   post:
//  *     name: Add Owners to Organization.
//  *     summary: Add Owners to Organization.
//  *     description: this will Add Owners to Organization.
//  *     tags:
//  *       - Organizations
//  *     parameters:
//  *       - in: path
//  *         name: organizationId
//  *         required: true
//  *         description: organization id
//  *         schema:
//  *           type: string
//  *     requestBody:
//  *       required: true
//  *       content:
//  *         application/json:
//  *           schema:
//  *              type: object
//  *              properties:
//  *                owners:
//  *                  type: array
//  *                  items:
//  *                    $ref: '#/components/schemas/addMemberInOrganization'
//  *     responses:
//  *       200: #response type
//  *         description: OK
//  *         content:
//  *           application/json:
//  *               example:
//  *                {
//  *                    meta: {
//  *                        message: "Organization owners added successfully"
//  *                    }
//  *                }
//  *       401:
//  *         description: Not authorized
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Not authorized"} ] }
//  *       400:
//  *         description: Bad Request
//  *         content:
//  *           application/json:
//  *             examples:
//  *               First:
//  *                 summary: Request body invalid
//  *                 description: Request body invalid
//  *                 value:
//  *                   { "errors":
//  *                     [
//  *                       { "message": "Owners must be valid array with min 1 and max 20 elements", "field": "owners"}
//  *                     ]
//  *                   }
//  *               Second:
//  *                 summary: URL params invalid
//  *                 description: URL params invalid
//  *                 value:
//  *                   { "errors":
//  *                     [
//  *                       { "message": "Organization id must be valid", "field": "organizationId"}
//  *                     ]
//  *                   }
//  *               Third:
//  *                 summary: Owners already assigned to other org
//  *                 description: Owners already assigned to other org
//  *                 value:
//  *                   { "errors": [ { "message": "Please make sure provided owners must not added as either user or owner"} ] }
//  *       404:
//  *         description: Not Found
//  *         content:
//  *           application/json:
//  *             example:
//  *              {
//  *                  "errors": [
//  *                      {
//  *                          "message": "Organization not found"
//  *                      }
//  *                  ]
//  *              }
//  *       500:
//  *         description: Internal Server Error
//  *         content:
//  *           application/json:
//  *             examples:
//  *               First:
//  *                 summary: None of owner inserted.
//  *                 description: None of owner inserted.
//  *                 value:
//  *                  {
//  *                      "errors": [
//  *                          {
//  *                              "message": "Something went wrong"
//  *                          }
//  *                      ]
//  *                  }
//  *               Second:
//  *                 summary: Partial owners inserted.
//  *                 description: Partial owners inserted.
//  *                 value:
//  *                  {
//  *                      "errors": [
//  *                          {
//  *                              "message": "<EMAIL> didn't inserted, please try again"
//  *                          }
//  *                      ]
//  *                  }
//  *               Third:
//  *                 summary: Internal server error.
//  *                 description: Internal server error.
//  *                 value:
//  *                  {
//  *                      "errors": [
//  *                          {
//  *                              "message": "Something went wrong"
//  *                          }
//  *                      ]
//  *                  }
//  */
