// import express, { NextFunction, Request, Response } from "express";
// import { BadRequestError, currentUser, hasGlobalAction, InsufficientPrivilagesError, InternalServerError, InvalidMembersBadRequestError, MembersAlreadyExistBadRequestError, NotFoundCode, NotFoundError, requireAuth, ResourceNotFoundError, validateRequest } from "@moxfive-llc/common";
// import { User } from "../../models/user";
// import { Organization } from "../../models/organization";
// import { IdBatchRequestStatus, UserAzureDetails } from "../../interfaces";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
// import { addOrganizationOwnersValidation } from "./add-organization-owners.validation";

// const router = express.Router();

// router.post("/v1/organizations/:organizationId/owners",
//   currentUser,
//   requireAuth,
//   addOrganizationOwnersValidation,
//   validateRequest,
//   async (req: Request, res: Response, next: NextFunction) => {
//     try {
//       // Check user has permission to add owners of organization
//       const hasPermission = await hasGlobalAction(req, "AddOwnerToOrganization");
//       if (!hasPermission) {
//         throw new InsufficientPrivilagesError();
//       }

//       const { owners }: { owners: UserAzureDetails[] } = req.body;
//       const { organizationId } = req.params;

//       // Step 1: Find organization and if it's not present then throw NotFoundError
//       const organization = await Organization.findById(organizationId);

//       if (!organization) {
//         throw new ResourceNotFoundError(NotFoundCode["ORGANIZATION_NOT_FOUND"], "Organization not found.");
//       }

//       // Step 2: Find which members are part of same org and which aren't
//       const usersInSameOrgAsMember: string[] = [];
//       const usersNotInSameOrg: string[] = [];
//       const usersInSameOrgAsOwner: string[] = [];
//       const usersInOtherOrg: string[] = [];

//       owners.forEach(owner => {
//         if (!organization.owner.includes(owner.id)) {
//           if (organization.member.includes(owner.id)) {
//             usersInSameOrgAsMember.push(owner.id);
//           } else {
//             usersNotInSameOrg.push(owner.id);
//           }
//         } else {
//           usersInSameOrgAsOwner.push(owner.id);
//         }
//       });

//       if (owners.length === usersInSameOrgAsOwner.length) {
//         throw new MembersAlreadyExistBadRequestError(usersInSameOrgAsOwner);
//       }

//       if (usersInSameOrgAsMember.length !== 0 || usersNotInSameOrg.length !== 0) {
//         // Step 3: Check provided owners are already associated with other Org as owner or member
//         if (usersNotInSameOrg.length > 0) {
//           const existingUsersOrgs = await Organization.findUserAlreadyInOtherOrganization(usersNotInSameOrg);

//           // if (existingUser) {
//           //   throw new BadRequestError("Please make sure provided owners must not added as either user or owner");
//           // }
//           if (existingUsersOrgs && existingUsersOrgs.length) {
//             existingUsersOrgs.map(org => {
//               org.member.map(memberId => {
//                 if (usersNotInSameOrg.includes(memberId)) {
//                   usersInOtherOrg.push(memberId);
//                 }
//               });

//               org.owner.map(ownerId => {
//                 if (usersNotInSameOrg.includes(ownerId)) {
//                   usersInOtherOrg.push(ownerId);
//                 }
//               });
//             });
//           }

//           if (owners.length === usersInOtherOrg.length) {
//             throw new InvalidMembersBadRequestError(usersInOtherOrg);
//           }
//         }

//         if (usersInOtherOrg.length) {
//           usersInOtherOrg.map(userId => {
//             const index = usersNotInSameOrg.indexOf(userId);
//             if (index !== -1) {
//               usersNotInSameOrg.splice(index, 1);
//             }
//           });
//         }

//         // Step 4: Get the authorization token
//         const token = await microsoftGraphAPI.getAccessToken();

//         // Step 5 : Add owners in the group microsoft azure
//         const addOwnersResponse: IdBatchRequestStatus = await microsoftGraphAPI.addOwnersInOrganization(
//           {
//             token,
//             orgId: organization.azureId,
//             owners: [...usersInSameOrgAsMember, ...usersNotInSameOrg]
//           });

//         // Step 7: Filter out inserted owners and not inserted owners
//         const usersExistInSameOrg = new Set(usersInSameOrgAsMember);

//         const insertedOwners: UserAzureDetails[] = [];
//         const insertedExistingMembers: string[] = [];
//         const notInsertedOwners: string[] = [];

//         owners.forEach(owner => {
//           if (addOwnersResponse.hasOwnProperty(owner.id)) {
//             // eslint-disable-next-line no-nested-ternary
//             addOwnersResponse[owner.id] ?
//               usersExistInSameOrg.has(owner.id) ?
//                 insertedExistingMembers.push(owner.id) :
//                 insertedOwners.push(owner)
//               : notInsertedOwners.push(owner.mail);
//           }
//         });

//         // If no owner is added then throw internal server error
//         if (insertedOwners.length === 0 && insertedExistingMembers.length === 0) {
//           throw new InternalServerError();
//         }

//         const insertedOwnersIds = insertedOwners.map(owner => owner.id);

//         // Step 8 : Add users in group DB
//         organization.owner = organization.owner.concat([...insertedOwnersIds, ...insertedExistingMembers]);
//         await organization.save();
//         await OrganizationUpdatedPublisherWrapper(organization);

//         // Step 9: Insert users if not exist
//         await User.insertUsersIfNotExist(insertedOwners, organization.id);

//         // Step 10: If some users are not inserted then throw internal server error with email ids which are not inserted
//         if (notInsertedOwners.length > 0) {
//           throw new InternalServerError(`${notInsertedOwners} didn't inserted, please try again`);
//         }
//       }

//       // Step 11: Send Response
//       res.json({
//         meta: {
//           message: "Organization owners added successfully"
//         }
//       });
//     } catch (error) {
//       console.error("Authentication.AddOrganizationOwners");
//       console.error(error);
//       next(error);
//     }
//   });

// export { router as addOrganizationOwnersRouter };
