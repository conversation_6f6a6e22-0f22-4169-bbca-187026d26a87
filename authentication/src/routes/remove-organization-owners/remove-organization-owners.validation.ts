import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../util";

export const removeOrganizationOwnersValidation = [
    param("organizationId")
        .exists().bail()
        .isMongoId(),

    body("owners")
        .isArray({ min: 1, max: 20 })
        .withMessage("At a given time max 20 owners can be removed from an organization.").bail()
        .custom((owners: string[]) => {
            return owners.every(owner => {
                return isValidMongoObjectId(owner);
            });
        }).withMessage("These user ids are invalid.")
];
