/**
 * @swagger
 * /v1/organizations/{organizationId}/owners:
 *   delete:
 *     summary: Remove Owners from organization
 *     description: Remove Owners from organization
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                owners:
 *                  type: array
 *                  items:
 *                    type: string
 *           example:
 *            {
 *                "owners": ["6239ff4af2cd78120fa94d47", "6239ff4af2cd78120fa94d48"]
 *            }
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                    meta: {
 *                        message: "Organization owners removed successfully"
 *                    }
 *                }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Owners must be valid array with min 1 and max 20 elements", "field": "owners"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"},
 *                     ]
 *                   }
 *               Third:
 *                 summary: Invalid owner Ids
 *                 description: Invalid owner Ids
 *                 value:
 *                   { "errors": [ { "message": "Provide valid owner Ids"} ] }
 *               Fourth:
 *                 summary: All provided owners not in organization
 *                 description: All the owners provided are not in organization
 *                 value:
 *                   { "errors": [ { "message": "All the owners provided are not in organization"} ] }
 *               Fifth:
 *                 summary: Must have atleast one owner after removing owners
 *                 description: Must have atleast one owner after removing owners
 *                 value:
 *                   { "errors": [ { "message": "Organization must have at least one owner after removing specified owners"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization not found"
 *                      }
 *                  ]
 *              }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: None of owners removed
 *                 description: None of owners removed
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 *               Second:
 *                 summary: Partial owners removed
 *                 description: Partial owners removed
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "<EMAIL> didn't removed, please try again"
 *                          }
 *                      ]
 *                  }
 *               Third:
 *                 summary: Internal server error.
 *                 description: Internal server error.
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 */
