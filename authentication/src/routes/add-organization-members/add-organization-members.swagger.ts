/**
 * @swagger
 * /v1/organizations/{organizationId}/members:
 *   post:
 *     name: Add members to Organization.
 *     summary: Add members to Organization.
 *     description: this will Add members to Organization.
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                members:
 *                  type: array
 *                  items:
 *                    $ref: '#/components/schemas/addMemberInOrganization'
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                    meta: {
 *                        message: "Organization members added successfully"
 *                    }
 *                }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Members must be valid array with min 1 and max 20 elements", "field": "members"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"}
 *                     ]
 *                   }
 *               Third:
 *                 summary: members already assigned to other org
 *                 description: members already assigned to other org
 *                 value:
 *                   { "errors": [ { "message": "Please make sure provided members must not added as either user or owner"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization not found"
 *                      }
 *                  ]
 *              }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Something went wrong"
 *                      }
 *                  ]
 *              }
 */
