// /* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
// /* eslint-disable max-statements */
// import express, { NextFunction, Request, Response } from "express";
// import {
//     currentUser,
//     ExternalServerError,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     InvalidMembersBadRequestError,
//     MembersAlreadyExistBadRequestError,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     responseHandler,
//     SucceededPartially, TargetType,
//     validateRequest
// } from "@moxfive-llc/common";
// import { User } from "../../models/user";
// import { Organization } from "../../models/organization";
// import { AddMembersUsingEmailRequestData, GetUsersListResponse, UserAzureDetails } from "../../interfaces";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
// import { InviteMultipleUsersResponse } from "../../interfaces/invite-multiple-users-response";
// import { addOrganizationMembersByEmailValidation } from "./add-organization-members-by-email.validation";
// import { getUserName } from "../../util";
// import { UserInviteNotificationPublisher } from "../../events/publishers/user-invite-notification";
// import { natsWrapper } from "../../nats-wrapper";
// import { queueGroupName } from "../../events/queue-group-name";

// const router = express.Router();

// router.post("/v1/organizations/:organizationId/members/emails",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     addOrganizationMembersByEmailValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // Check user has permission to add users of organization
//             const hasPermission = await hasGlobalAction(req, "AddMemberToOrganizationUsingEmail");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { members: membersToAdd, invitationMessage }: { members: AddMembersUsingEmailRequestData[], invitationMessage: string | undefined } = req.body;
//             const { organizationId } = req.params;
//             const emails = membersToAdd.map(m => m.email);

//             const members: UserAzureDetails[] = [];
//             const usersToBeInvited: AddMembersUsingEmailRequestData[] = [];
//             const notInsertedMembers: any = [];

//             // Step 1: Find organization and if it's not present then throw NotFoundError
//             const organization = await Organization.findById(organizationId);
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }

//             // Step 2: Search users in AD
//             const accessToken = await microsoftGraphAPI.getAccessToken();
//             const usersList: GetUsersListResponse = await microsoftGraphAPI.searchUsersByMultipleEmails({ accessToken, emails });
//             membersToAdd.forEach(m => {
//                 const user = usersList.data.value.find((user) => user.mail === m.email);
//                 if (!user) {
//                     usersToBeInvited.push(m);
//                 }
//                 else {
//                     members.push({ id: user.id, mail: user.mail, displayName: user.displayName });
//                 }
//             });

//             // Step 3: Invite new users
//             if (usersToBeInvited.length) {
//                 const inviteMultipleUsersResponse: InviteMultipleUsersResponse[] = await microsoftGraphAPI.inviteMultipleUsers(
//                     {
//                         token: accessToken,
//                         users: usersToBeInvited,
//                         invitationMessage
//                     });

//                 await Promise.all(inviteMultipleUsersResponse.map(async (user) => {
//                     if (!user.status) {
//                         notInsertedMembers.push(user.body?.error);
//                     }
//                     else {
//                         await new UserInviteNotificationPublisher(natsWrapper.client).publish({
//                             email: user.data.email,
//                             inviteRedeemUrl: user.data.inviteRedeemUrl,
//                             displayName: user.data.displayName,
//                             serviceName: queueGroupName
//                         });

//                         members.push({ id: user.id, mail: user.data.email, displayName: user.data.displayName });
//                     }
//                 }));

//                 if (notInsertedMembers.length === membersToAdd.length) {
//                     throw new ExternalServerError(notInsertedMembers);
//                 }
//             }

//             // Step 4: Find which members are part of same org and which aren't
//             const usersInSameOrg: string[] = [];
//             const usersNotInSameOrg: string[] = [];
//             const usersNotInSameOrgDetails: UserAzureDetails[] = [];
//             const usersInOtherOrg: string[] = [];
//             const usersInSameOrgEmails: string[] = [];
//             const usersInOtherOrgEmails: string[] = [];
//             let targetedUsers: {type: TargetType, details: {id: string, name: string, email: string, azureId: string}}[] = [];

//             members.forEach(member => {
//                 if (organization.member.includes(member.id) || organization.owner.includes(member.id)) {
//                     usersInSameOrg.push(member.id);
//                 }
//                 else {
//                     usersNotInSameOrg.push(member.id);
//                     usersNotInSameOrgDetails.push(member);
//                 }
//             });

//             if (usersInSameOrg.length) {
//                 usersInSameOrg.forEach(azureId => {
//                     usersInSameOrgEmails.push(members.find(member => member.id === azureId)?.mail!);
//                 });
//             }

//             if (members.length === usersInSameOrgEmails.length) {
//                 throw new MembersAlreadyExistBadRequestError(usersInSameOrgEmails);
//             }

//             if (usersNotInSameOrg.length !== 0) {
//                 // Step 5: Check provided owners are already associated with other Org as owner or member
//                 if (usersNotInSameOrg.length > 0) {
//                     const existingUsersOrgs = await Organization.findUserAlreadyInOtherOrganization(usersNotInSameOrg);

//                     if (existingUsersOrgs && existingUsersOrgs.length) {
//                         existingUsersOrgs.forEach(org => {
//                             org.member.forEach(memberId => {
//                                 if (usersNotInSameOrg.includes(memberId)) {
//                                     usersInOtherOrg.push(memberId);
//                                 }
//                             });

//                             org.owner.forEach(ownerId => {
//                                 if (usersNotInSameOrg.includes(ownerId)) {
//                                     usersInOtherOrg.push(ownerId);
//                                 }
//                             });
//                         });
//                     }

//                     if (usersInOtherOrg.length) {
//                         usersInOtherOrg.forEach(azureId => {
//                             usersInOtherOrgEmails.push(members.find(member => member.id === azureId)?.mail!);
//                         });
//                     }

//                     if (members.length === usersInOtherOrgEmails.length) {
//                         throw new InvalidMembersBadRequestError(usersInOtherOrgEmails);
//                     }
//                 }

//                 if (usersInOtherOrg.length) {
//                     usersInOtherOrg.forEach(userId => {
//                         const index = usersNotInSameOrg.indexOf(userId);
//                         if (index !== -1) {
//                             usersNotInSameOrg.splice(index, 1);
//                             usersNotInSameOrgDetails.splice(index, 1);
//                         }
//                     });
//                 }

//                 if (usersNotInSameOrg.length > 0) {
//                     // Step 6: Get the authorization token
//                     const token = await microsoftGraphAPI.getAccessToken();

//                     // Step 7 : Add users in the group microsoft azure
//                     await microsoftGraphAPI.addMembersInOrganization(
//                         {
//                             token,
//                             orgId: organization.azureId,
//                             members: [...usersNotInSameOrg]
//                         });

//                     // Step 8 : Add users in group DB
//                     organization.member = organization.member.concat([...usersNotInSameOrg]);
//                     await organization.save();
//                     await OrganizationUpdatedPublisherWrapper(organization);

//                     // Step 9: Insert users if not exist
//                     const addedUsers = await User.insertUsersIfNotExist(usersNotInSameOrgDetails, organization.id);
//                     targetedUsers = addedUsers.map(user => {
//                         return {
//                             type: TargetType.USER,
//                             details: {
//                                 id: user.id,
//                                 name: getUserName({
//                                     firstName: user.firstName,
//                                     lastName: user.lastName,
//                                     displayName: user.displayName
//                                 }),
//                                 email: user.email,
//                                 azureId: user.azureId
//                             }
//                         };
//                     });
//                 }
//             }

//             // Step 10: If some users are not inserted then throw internal server error with email ids which are not inserted
//             const errors = [];
//             if (usersInSameOrgEmails.length) {
//                 errors.push({
//                     attributes: usersInSameOrgEmails,
//                     message: "These members are already exist in the specified organization."
//                 });
//             }

//             if (usersInOtherOrgEmails.length) {
//                 errors.push({
//                     attributes: usersInOtherOrgEmails,
//                     message: "Disallowed memebers. These members are already part of other organization."
//                 });
//             }

//             if (errors.length) {
//                 const finalErrors = [{
//                     parameters: errors
//                 }];
//                 throw new SucceededPartially(finalErrors, "One or more users are failed to add in the specified organization.");
//             }

//             // Step 11: Send Response
//             res.sendResponse({
//                 meta: {
//                     message: "Organization members added successfully"
//                 }
//             }, {
//                 targets: [
//                     {
//                         type: TargetType.ORGANIZATION,
//                         details: {
//                             id: organizationId,
//                             name: organization.name
//                         }
//                     },
//                     ...targetedUsers
//                 ],
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: organizationId
//             });
//         }
//         catch (error) {
//             console.error("Authentication.AddOrganizationMembersByEmails");
//             console.error(error);
//             next(error);
//         }

//     });

// export { router as addOrganizationMembersByEmailsRouter };
