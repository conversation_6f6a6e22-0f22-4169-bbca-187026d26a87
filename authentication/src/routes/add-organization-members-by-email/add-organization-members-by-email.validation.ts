import { body, param } from "express-validator";

export const addOrganizationMembersByEmailValidation = [
    param("organizationId")
        .exists().bail()
        .isMongoId(),

    body("members")
        .exists().bail()
        .isArray({ min: 1, max: 20 }).withMessage("At a given time max 20 users can be added into an organization."),

    body("members.*.email")
        .exists().bail()
        .isEmail().normalizeEmail().withMessage("Email is invalid."),

    body("members.*.displayName")
        .exists().bail()
        .isString().trim().blacklist("<>")
        .isLength({ min: 1, max: 256 }).withMessage("Display name can only contain letters, dashes, numbers, spaces, dot and can be of max 256 characters long."),

    body("invitationMessage")
        .optional()
        .isString().trim().blacklist("<>")
        .isLength({ max: 500 }).withMessage("Invitation Message must be string and of max 500 characters long.")
];
