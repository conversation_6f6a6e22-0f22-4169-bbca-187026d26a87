/**
 * @swagger
 * /v1/organizations/{organizationId}/members/email:
 *   post:
 *     name: Add members to organization using emails.
 *     summary: Add members to organization using emails.
 *     description: This will add members to organization using emails.
 *     tags:
 *       - Organizations
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         description: organization id
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                emails:
 *                  type: array
 *                  items:
 *                    type: string
 *                  example: ["<EMAIL>", "<EMAIL>"]
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                {
 *                    meta: {
 *                        message: "Organization members added successfully"
 *                    }
 *                }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: Request body invalid
 *                 description: Request body invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Members must be valid array with min 1 and max 20 elements", "field": "members"}
 *                     ]
 *                   }
 *               Second:
 *                 summary: URL params invalid
 *                 description: URL params invalid
 *                 value:
 *                   { "errors":
 *                     [
 *                       { "message": "Organization id must be valid", "field": "organizationId"}
 *                     ]
 *                   }
 *               Third:
 *                 summary: Members already assigned to other org
 *                 description: Members already assigned to other org
 *                 value:
 *                   { "errors": [ { "message": "Please make sure provided members must not added as either user or owner"} ] }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Organization not found"
 *                      }
 *                  ]
 *              }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             example:
 *              {
 *                  "errors": [
 *                      {
 *                          "message": "Something went wrong"
 *                      }
 *                  ]
 *              }
 */
