import { body } from "express-validator";
import { isValidMongoObjectId } from "../../util";

export const UpdateUsersStatusValidation = [
    body("users")
        .isArray({ min: 1, max: 20 }).withMessage("At a given time max 20 users status can be updated").bail()
        .custom((users: string[]) => {
            return users.every(user => {
                return isValidMongoObjectId(user);
            });
        }).withMessage("These users ids are invalid."),

    body("isEnabled")
        .isBoolean({ loose: false })
        .toBoolean()
        .withMessage("isEnabled must be boolean.")
];
