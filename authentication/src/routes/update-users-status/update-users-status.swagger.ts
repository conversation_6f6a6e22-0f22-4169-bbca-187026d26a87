/**
 * @swagger
 * /v1/users/status:
 *   put:
 *     name: Update users status.
 *     summary: Update users status.
 *     description: This will update users status.
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *              type: object
 *              properties:
 *                isEnabled:
 *                  type: boolean
 *                users:
 *                  type: array
 *                  items:
 *                    type: string
 *                  example: ["62397c3b020359bf2f084267", "62397c3b020359bf2f084268"]
 *     responses:
 *       200: #response type
 *         description: OK
 *         content:
 *           application/json:
 *               example:
 *                  {
 *                    "meta": {
 *                      "message": "Users status updated successfully"
 *                    }
 *                  }
 *       401:
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               { "errors": [ { "message": "Not authorized"} ] }
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             example:
 *                   { "errors":
 *                     [
 *                       {"message": "isEnabled must be valid", "field": "isEnabled"},
 *                       {"message": "Users must be valid array with 1-20 elements", "field": "users"}
 *                     ]
 *                   }
 *       404:
 *         description: Not Found
 *         content:
 *           application/json:
 *             example:
 *                    { "errors": [ { "message": "Please provide valid user IDs"} ] }
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             examples:
 *               First:
 *                 summary: None of users status updated
 *                 description: None of users status updated
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 *               Second:
 *                 summary: Partial users status updated
 *                 description: Partial users status updated
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "<EMAIL> status didn't updated, please try again"
 *                          }
 *                      ]
 *                  }
 *               Third:
 *                 summary: Internal server error.
 *                 description: Internal server error.
 *                 value:
 *                  {
 *                      "errors": [
 *                          {
 *                              "message": "Something went wrong"
 *                          }
 *                      ]
 *                  }
 */
