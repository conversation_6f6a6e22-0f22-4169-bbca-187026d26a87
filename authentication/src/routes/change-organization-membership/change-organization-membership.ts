// import {
//     currentUser,
//     hasGlobalAction,
//     InsufficientPrivilagesError,
//     NotFoundCode,
//     requireAuth,
//     ResourceNotFoundError,
//     responseHandler,
//     TargetType,
//     UserMembershipAlreadyExistBadRequestError,
//     validateRequest
// } from "@moxfive-llc/common";
// import express, { NextFunction, Request, Response } from "express";
// import { AzureDeleteUser } from "../../models/azure-delete-users";
// import { Organization } from "../../models/organization";
// import { User } from "../../models/user";
// import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
// import { changeOrganizationMembershipValidation } from "./change-organization-membership.validation";
// import { getUserName } from "../../util";

// const router = express.Router();

// // This function is responsible for createing new azure delete user
// const createAzureDeleteUser = async ({ groupId, userId, membershipType } : { groupId: string, userId: string, membershipType: string }) => {
//     const userDelete = AzureDeleteUser.build({
//         groupId,
//         userId,
//         owner: !(membershipType === "owner")
//     });
//     await userDelete.save();
// };

// // This function is responsible for updating owner property of azure delete user if record exist otherwise it will create it
// const updateAzureDeleteUser = async ({ groupId, userId, membershipType } : { groupId: string, userId: string, membershipType: string }) => {
//     const userDelete = await AzureDeleteUser.findOne({ groupId, userId });
//     if(!userDelete) {
//         await createAzureDeleteUser({
//             groupId, userId, membershipType
//         });
//     }
//     else {
//         userDelete.owner = !(membershipType === "owner");
//         await userDelete.save();
//     }
// };

// router.put(
//     "/v1/organizations/:organizationId/changeMembership",
//     responseHandler,
//     currentUser,
//     requireAuth,
//     changeOrganizationMembershipValidation,
//     validateRequest,
//     async (req: Request, res: Response, next: NextFunction) => {
//         try {
//             // Check user has permission to add owners of organization
//             const hasPermission = await hasGlobalAction(req, "ChangeOrganizationMembership");
//             if (!hasPermission) {
//                 throw new InsufficientPrivilagesError();
//             }

//             const { user, membershipType }: { user: string, membershipType: string  } = req.body;
//             const { organizationId } = req.params;

//             // Step 1: Find organization and if it's not present then throw NotFoundError
//             const organization = await Organization.findById(organizationId);
//             if (!organization) {
//                 throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
//             }
//             const oldOwners = [...organization.owner];
//             const oldMembers = [...organization.member];

//             // Step 2: Find user and if it's not present then throw NotFoundError
//             const userDetails = await User.findById(String(user)).lean().exec();
//             if (!userDetails) {
//                 throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found");
//             }

//             // Step 3: Check which membership type is given and do operation based on type
//             let needToCreateAzureDeleteUser = false;

//             if(membershipType === "owner") { // Means change membership to owner
//                 // Check whether user is already present as member or not
//                 if (organization.owner.includes(userDetails.azureId)) {
//                     throw new UserMembershipAlreadyExistBadRequestError();
//                 }

//                 if(!organization.member.includes(userDetails.azureId)) {
//                     throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
//                 }

//                 // Get access token and add owner in the organization if it is not added as owner
//                 const accessToken = await microsoftGraphAPI.getAccessToken();

//                 const isAlreadyOwner = await microsoftGraphAPI.isUserOwnerOfOrganization({
//                     token: accessToken,
//                     orgId: organization.azureId,
//                     email: userDetails.email
//                 });

//                 if(!isAlreadyOwner) {
//                     await microsoftGraphAPI.addOwnerInOrganization({
//                         token: accessToken,
//                         orgId: organization.azureId,
//                         userId: userDetails.azureId
//                     });

//                     needToCreateAzureDeleteUser = true;
//                 }
//                 else {
//                     await updateAzureDeleteUser({
//                         groupId: organization.azureId,
//                         userId: userDetails.azureId,
//                         membershipType
//                     });
//                 }

//                 // Remove user as a member from organization and add as owner
//                 organization.owner.push(userDetails.azureId);
//                 organization.member = organization.member.filter(member => member !== userDetails.azureId);
//                 await organization.save();

//             }
//             else if(membershipType === "member") { // Means change membership to member
//                 // Check whether user is already present as owner or not
//                 if (organization.member.includes(userDetails.azureId)) {
//                     throw new UserMembershipAlreadyExistBadRequestError();
//                 }

//                 if(!organization.owner.includes(userDetails.azureId)) {
//                     throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
//                 }

//                 // Get access token and add member in the organization if it is not added as member
//                 const accessToken = await microsoftGraphAPI.getAccessToken();

//                 const isAlreadyMember = await microsoftGraphAPI.isUserMemberOfOrganization({
//                     token: accessToken,
//                     orgId: organization.azureId,
//                     email: userDetails.email
//                 });

//                 if(!isAlreadyMember) {
//                     await microsoftGraphAPI.addMemberInOrganization({
//                         token: accessToken,
//                         orgId: organization.azureId,
//                         userId: userDetails.azureId
//                     });

//                     needToCreateAzureDeleteUser = true;
//                 }
//                 else {
//                     await updateAzureDeleteUser({
//                         groupId: organization.azureId,
//                         userId: userDetails.azureId,
//                         membershipType
//                     });
//                 }

//                 // Remove user as a member from organization and add as owner
//                 organization.member.push(userDetails.azureId);
//                 organization.owner = organization.owner.filter(owner => owner !== userDetails.azureId);
//                 await organization.save();
//             }

//             // Step 4: Add record to ToBeDeleted collection so it will be deleted in the CRON
//             if(needToCreateAzureDeleteUser) {
//                 await createAzureDeleteUser({
//                     groupId: organization.azureId,
//                     userId: userDetails.azureId,
//                     membershipType
//                 });
//             }

//             // Step 5: Send response
//             res.sendResponse({
//                 meta: {
//                     message: "Membership changed successfully"
//                 }
//             }, {
//                 targets: [
//                     {
//                         type: TargetType.ORGANIZATION,
//                         details: {
//                             id: organizationId,
//                             name: organization.name
//                         }
//                     }, {
//                         type: TargetType.USER,
//                         details: {
//                             id: String(userDetails._id),
//                             name: getUserName({
//                                 firstName: userDetails.firstName,
//                                 lastName: userDetails.lastName,
//                                 displayName: userDetails.displayName
//                             }),
//                             email: userDetails.email,
//                             azureId: userDetails.azureId
//                         }
//                     }],
//                 correlation: TargetType.ORGANIZATION,
//                 correlationId: organizationId,
//                 modifiedProperties: [
//                     {
//                         target: TargetType.ORGANIZATION,
//                         propertyName: "owner",
//                         oldValue: JSON.stringify(oldOwners),
//                         newValue: JSON.stringify(organization.owner)
//                     },
//                     {
//                         target: TargetType.ORGANIZATION,
//                         propertyName: "member",
//                         oldValue: JSON.stringify(oldMembers),
//                         newValue: JSON.stringify(organization.member)
//                     }
//                 ]
//             });
//         }
//         catch (error) {
//             console.error("Authentication.ChangeOrganizationMembership");
//             console.error(error);
//             next(error);
//         }
//     }
// );

// export { router as changeOrganizationMembershipRouter };
