import {
    currentUser,
    require<PERSON><PERSON>,
    response<PERSON><PERSON><PERSON>,
    validateRequest
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";

import { Country } from "../../models/country";
import { State } from "../../models/state";
import { City } from "../../models/city";
import { getLocationsValidation } from "./get-locations.validation";

const router = express.Router();

router.get(
    "/v1/location/:searchType",
    responseH<PERSON><PERSON>,
    currentUser,
    requireAuth,
    getLocationsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "GetLocations";

            let result: any = [];
            const { searchType } = req.params;
            const { targetId } = req.query;
            if (searchType === "country") {
                result = await Country.find({});
            }
            else if (searchType === "state") {
                if(targetId) {
                    result = await State.find({ countryId: String(targetId) });
                }
                else {
                    result = await State.find({});
                }
            }
            else if (searchType === "city") {
                if(targetId) {
                    result = await City.find({ stateId: String(targetId) });
                }
                else {
                    result = await City.find({});
                }
            }

            res.sendResponse(result.map((element: any) => ({ "value": element.id, "label": element.name })), {});
        }
        catch (error) {
            console.error("Authentication.GetLocations");
            console.error(error);
            next(error);
        }
    }
);

export { router as getLocationsRouter };
