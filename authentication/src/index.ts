import { InternalServerError, loadSecrets, natsConnection, Secrets } from "@moxfive-llc/common"; // setConnection
import mongoose from "mongoose";
import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";

import app, { addMiddleWaresToApp } from "./app";
import { PlatformDashboardCreatedListener } from "./events/listeners/platform-dashboard-created-listener";
import { PlatformDashboardUpdatedListener } from "./events/listeners/platform-dashboard-updated-listener";
import { PolicyCreatedListener } from "./events/listeners/policy-created-listener";
import { PolicyDeletedListener } from "./events/listeners/policy-deleted-listener";
import { PolicyUpdatedListener } from "./events/listeners/policy-updated-listener";
import { UserPolicyUpdatedListener } from "./events/listeners/user-policy-updated-listener";
import { natsWrapper } from "./nats-wrapper";
import { UserAgreementSignedListener } from "./events/listeners/user-agreement-signed-listener";
import { IncidentCreatedListener } from "./events/listeners/incident-created-listener";
import { IncidentUpdatedListener } from "./events/listeners/incident-updated-listener";
import { IncidentDeletedListener } from "./events/listeners/incident-deleted-listener";
import { IncidentMembersRemovedListener } from "./events/listeners/incident-members-removed-listener";
import { IncidentPolicyUpdatedListener } from "./events/listeners/incident-policy-updated";
import { IncidentFlexibleFieldUpdatedListener } from "./events/listeners/incident-flexible-field-updated-listener";
import { ApplicationCreatedListener } from "./events/listeners/application-created-listener";
import { ApplicationUpdatedListener } from "./events/listeners/application-updated-listener";

const options: swaggerJsdoc.OAS3Options = {
    definition: {
        openapi: "3.0.0",
        info: {
            title: "Authentication",
            version: "1.0.0",
            description: "Microservice for handling user authentication.",
            license: {
                name: "MIT",
                url: "https://spdx.org/licenses/MIT.html",
            },
        },
        servers: [{
            url: process.env.DOMAIN_URL!,
            description: "Development APIs for Authentication"
        }],
        tags: [
            {
                name: "Credentials",
                description: "Contains credentials APIs"
            },
            {
                name: "Organizations",
                description: "Contains organizations apis"
            },
            {
                name: "Users",
                description: "Contains users APIs"
            }
        ]
    },
    apis: ["**/*.ts"],
};

// swagger middleware
const specs = swaggerJsdoc(options);

const main = async () => {
    try {
        // If there is a local development environment then by pass the call of loadSecrets
        if (process.env.LOCAL_ENVIRONMENT !== "true") {
            await loadSecrets([
                Secrets.MONGO_URI,
                Secrets.MOXFIVE_ID,
                Secrets.ENTERPRISE_OBJECT_ID,
                Secrets.COOKIE_SECRET,
                Secrets.JWT_SECRET,
                Secrets.SUPER_ADMIN_POLICY_ID,
                Secrets.EMAIL_CONNECTION_STRING,
                Secrets.CLIENT_STATE,
                Secrets.RESPONSE_ENCRYPTION_KEY,
                Secrets.AZURE_ASSETS_STORAGE_CONNECTION_STRING,
                Secrets.CDN_ENDPOINT,
                Secrets.NATS_TOKEN,
                Secrets.AUTH0_DOMAIN,
                Secrets.AUTH0_WEB_APP_CLIENT_ID,
                Secrets.AUTH0_WEB_APP_CLIENT_SECRET,
                Secrets.AUTH0_SPA_APP_CLIENT_ID,
                Secrets.AUTH0_SPA_APP_CLIENT_SECRET,
                // Secrets.AUTH0_WEBHOOK_TOKEN,
                // Secrets.RECAPTCHA_V2_SECRET_KEY,
                // Secrets.RECAPTCHA_V3_SECRET_KEY
            ]);
        }
        addMiddleWaresToApp();
        app.use(
            "/authentication/api-docs-1.0",
            swaggerUi.serve,
            swaggerUi.setup(specs, { explorer: true })
        );
        await handleValidationError();
        await connectDatabase();
        await handleNatsListener();
    // if (process.env.NODE_ENV !== "development") {
    //   await handleNatsListener();
    // }
    }
    catch (error) {
        console.error("Authentication.index.");
        console.error(error);
        throw new InternalServerError();
    }
};

/**
 * @method handleValidationError
 * @description This will handle the validation error for all the env variable keys
 */
const handleValidationError = () => {
    if (!process.env.MONGO_URI) {
        throw new Error("MONGO_URI must be defined");
    }
    if (!process.env.CLIENT_ID || !process.env.CLIENT_SECRET || !process.env.TENANT_ID || !process.env.ENTERPRISE_OBJECT_ID) {
        throw new Error("Microsoft azure secrets must be defined");
    }
    if (!process.env.JWT_SECRET) {
        throw new Error("JWT_SECRET must be defined");
    }
    if (!process.env.COOKIE_SECRET) {
        throw new Error("COOKIE_SECRET must be defined");
    }
    if (!process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING) {
        throw new Error("AZURE_ASSETS_STORAGE_CONNECTION_STRING must be defined");
    }
    if (!process.env.CDN_ENDPOINT) {
        throw new Error("CDN_ENDPOINT must be defined");
    }

    if (!process.env.AUTH0_DOMAIN) {
        throw new Error("AUTH0_DOMAIN must be defined");
    }

    if (!process.env.AUTH0_WEB_APP_CLIENT_ID) {
        throw new Error("AUTH0_WEB_APP_CLIENT_ID must be defined");
    }

    if (!process.env.AUTH0_WEB_APP_CLIENT_SECRET) {
        throw new Error("AUTH0_WEB_APP_CLIENT_SECRET must be defined");
    }

    if (!process.env.AUTH0_SPA_APP_CLIENT_ID) {
        throw new Error("AUTH0_SPA_APP_CLIENT_ID must be defined");
    }

    if (!process.env.AUTH0_SPA_APP_CLIENT_SECRET) {
        throw new Error("AUTH0_SPA_APP_CLIENT_SECRET must be defined");
    }

    if (!process.env.AUTH0_WEBHOOK_TOKEN) {
        throw new Error("AUTH0_WEBHOOK_TOKEN must be defined");
    }
};
/**
 * @method connectDatabase
 * @description This function will perform the Database connection via Mongo URI
 */
const connectDatabase = async () => {
    mongoose.set("strictQuery", true);
    mongoose.set("toJSON", { flattenMaps: false });
    //mongoose.set("sanitizeFilter", true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await mongoose.connect(process.env.MONGO_URI, {
        dbName: "authentication"
    });
    console.info("connect to db successfully..");
};
/**
 * @method handleNatsListener
 * @description This function is used to handle the NATS Listeners.
 */
const handleNatsListener = async () => {
    await natsWrapper.connect();
    natsConnection.setClient(natsWrapper.client);
    new PolicyCreatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info(error);
    });
    new PolicyUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info(error);
    });
    new UserPolicyUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info(error);
    });
    new PolicyDeletedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info(error);
    });
    new PlatformDashboardCreatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info(error);
    });
    new PlatformDashboardUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info(error);
    });
    new UserAgreementSignedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("User agreement assigned error:", error);
    });
    new IncidentCreatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Incident created listener error", JSON.stringify(error));
    });
    new IncidentUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Incident updated listener error", JSON.stringify(error));
    });
    new IncidentDeletedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Incident deleted listener error", JSON.stringify(error));
    });
    new IncidentMembersRemovedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Incident members removed listener error", JSON.stringify(error));
    });
    new IncidentPolicyUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Incident policy updated listener error", JSON.stringify(error));
    });
    new IncidentFlexibleFieldUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Incident flexible field updated listener error", JSON.stringify(error));
    });
    new ApplicationCreatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Application created listener error", JSON.stringify(error));
    });
    new ApplicationUpdatedListener(natsWrapper.client).listen().then().catch((error) => {
        console.info("Application updated listener error", JSON.stringify(error));
    });
};

main().then(() => {
    app.listen(3000, () => {
        console.info("Listening on port 3000!!!");
    });
}).catch((error) => {
    console.info(error.reason);
});

process.on("uncaughtException", err => {
    console.error(err && err.stack);
});

// const exitHandler = async () => {
//     // Disconnect mongoDB connection
//     try {
//         await mongoose.connection.close(true);
//         console.info("DB disconnected successfully");
//     }
//     catch (err) {
//         console.info("Something went wrong while mongoDB disconnect", err);
//     }
//     finally {
//         process.exit();
//     }
// };
//
// [
//     "unhandledRejection", "SIGHUP", "SIGINT", "SIGQUIT", "SIGILL", "SIGTRAP", "SIGABRT", "SIGBUS", "SIGFPE", "SIGUSR1", "SIGSEGV", "SIGUSR2", "SIGTERM",
// ].forEach(evt => process.on(evt, exitHandler));
