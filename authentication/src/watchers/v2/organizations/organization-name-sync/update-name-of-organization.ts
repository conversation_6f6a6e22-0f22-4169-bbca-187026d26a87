/* eslint-disable @typescript-eslint/no-unused-vars */
import { ObjectId } from "mongodb";
import { OrganizationV2 } from "../../../../models/v2/oragnizations-v2";
import { Connections } from "../../../../models/connections";
import { UserV2 } from "../../../../models/v2/users-v2";

export const updateNameOfOrganization = async (key: ObjectId) => {
    const organizationDetails = await OrganizationV2.findById(key, { name: 1 }).lean().exec();
    if (!organizationDetails) {
        return;
    }

    await Connections.updateMany({ "organization.id": String(organizationDetails._id) },
        { $set: { "organization.value": organizationDetails.name } });
    await UserV2.updateMany({ "organization.id": String(organizationDetails._id) },
        { $set: { "organization.value": organizationDetails.name } });
};

