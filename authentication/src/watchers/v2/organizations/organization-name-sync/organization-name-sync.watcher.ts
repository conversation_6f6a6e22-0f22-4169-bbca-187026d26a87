import { ChangeStreamDocument } from "mongodb";
import { updateNameOfOrganization } from "./update-name-of-organization";

export const organizationWatcher = async (doc: ChangeStreamDocument<any>) => {
    // if (doc.operationType === "insert") {
    //     await changedName(doc.documentKey._id, doc.fullDocument);
    // }

    if (doc.operationType === "update" &&
        doc.updateDescription.updatedFields &&
        (doc.updateDescription.updatedFields.hasOwnProperty("name"))) {
        await updateNameOfOrganization(doc.documentKey._id);
    }
};
