import { ChangeStreamDocument } from "mongodb";
import { Organization } from "../../../models/organization";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { OrganizationsDeletedByWatcher } from "../../../models/organizations-deleted-by-watcher";

export const organizationV2ToV1SyncWatcher = async (doc: ChangeStreamDocument<any>) => {
    if(doc.operationType === "insert" || doc.operationType === "update") {
        // Find organization
        const organization = doc.fullDocument ?? (await OrganizationV2.findById(doc.documentKey._id).lean().exec());
        if(organization) {
            const data: Record<string, any> = { ...organization };

            // Add organizationTypeIds, transform industry, serviceLines
            if(data.hasOwnProperty("organizationTypes")) {
                data.organizationTypeIds = data.organizationTypes ? data.organizationTypes.map((record: any) => record.id) : null;
            }

            if(data.hasOwnProperty("industry")) {
                data.industry = data.industry ? [data.industry.id] : null;
            }

            if(data.hasOwnProperty("serviceLines")) {
                data.serviceLines = data.serviceLines ? data.serviceLines.map((record: any) => record.id) : null;
            }

            if(data.hasOwnProperty("partnerType")) {
                data.partnerType = data.partnerType ? [data.partnerType.id] : null;
            }

            if(data.hasOwnProperty("coverageStates")) {
                data.coverageStates = data.coverageStates ? data.coverageStates.map((record: any) => record.id) : null;
            }

            if(data.hasOwnProperty("offerings")) {
                data.offerings = data.offerings ? data.offerings.map((record: any) => record.id) : null;
            }

            if(data.hasOwnProperty("moxfivePMSponsor")) {
                data.moxfivePMSponsor = data.moxfivePMSponsor ? data.moxfivePMSponsor.id : null;
            }

            if(data.hasOwnProperty("moxfiveTASponsor")) {
                data.moxfiveTASponsor = data.moxfiveTASponsor ? data.moxfiveTASponsor.id : null;
            }

            if(data.hasOwnProperty("moxfiveSalesSponsor")) {
                data.moxfiveSalesSponsor = data.moxfiveSalesSponsor ? data.moxfiveSalesSponsor.id : null;
            }

            if(data.hasOwnProperty("languages")) {
                data.languages = data.languages ? data.languages.map((record: any) => record.id) : null;
            }

            // Delete organizationTypes
            delete data.organizationTypes;

            // Update organizations data in v1
            await Organization.findByIdAndUpdate(doc.documentKey._id, data, { upsert: true });

        }
    }
    else if (doc.operationType === "delete") {
        const organization = await Organization.findById(doc.documentKey._id).lean().exec();
        if(organization) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            await OrganizationsDeletedByWatcher.build({
                ...organization,
                collectionName: "organizationsV2"
            }).save();
        }

        await Organization.findByIdAndDelete(doc.documentKey._id);
    }
};
