import { ChangeStreamDocument } from "mongodb";
import { changedName } from "./changed-name";

export const userWatcher = async (doc: ChangeStreamDocument<any>) => {
    if (doc.operationType === "insert") {
        await changedName(doc.documentKey._id, doc.fullDocument);
    }

    if (doc.operationType === "update" &&
        doc.updateDescription.updatedFields &&
        (doc.updateDescription.updatedFields.hasOwnProperty("firstName") ||
            doc.updateDescription.updatedFields.hasOwnProperty("lastName") ||
            doc.updateDescription.updatedFields.hasOwnProperty("displayName"))) {
        await changedName(doc.documentKey._id, doc.updateDescription.updatedFields);
    }
};
