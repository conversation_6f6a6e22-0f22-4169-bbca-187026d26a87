/* eslint-disable @typescript-eslint/no-unused-vars */
import { ObjectId } from "mongodb";
import { User } from "../../models/user";
import { getUserName } from "../../util";

interface ChangedField {
    firstName?: string;
    lastName?: string;
    displayName?: string;
}

export const changedName = async (key: ObjectId, changedFields: ChangedField) => {
    const userDetails = await User.findById(key, { firstName: 1, lastName: 1, displayName: 1 }).lean().exec();
    if (!userDetails) {
        return;
    }

    const name = getUserName({ firstName: userDetails.firstName, lastName: userDetails.lastName, displayName: userDetails.displayName });

    await User.updateOne({ _id: key }, { $set: { name: name } });
};

