import { ExternalServerError, InvalidTokenError, InvalidResourceIdBadRequestError } from "@moxfive-llc/common";
import axios, { AxiosResponse } from "axios";
import crypto from "crypto";
import qs from "qs";
import app from "../app";
import {
    AddOrganizationInApplicationAzureParams,
    AddOrganizationOwnersAzureParams,
    AddOrganizationMembersAzureParams,
    BatchRequestIndividualResponse,
    BatchRequestResponse,
    CreateBatchRequestData,
    CreateOrganizationAzureParams,
    CreateOrganizationAzureRequestData,
    CreateOrganizationAzureResponse,
    GetUsersListResponse,
    IdBatchRequestStatus,
    InviteUserParams,
    InviteUserResponse,
    RemoveOrganizationOwnersAzureParams,
    RemoveOrganizationUsersAzureParams,
    SearchUsersParams,
    UpdateOrganizationNameAzureParams,
    UpdateUserAzureDetailsParams,
    UpdateUserEmailParams,
    VerifyCodeAzureParams,
    RefreshTokenAzureParams,
    AddOrganizationOwnerMemberAzureParams,
    CheckOrganizationOwnerMemberAzureParams
} from "../interfaces";
import { ConfidentialClientApplication } from "@azure/msal-node";
import { InviteMultipleUsersAzureParams } from "../interfaces/invite-multiple-users-azure-params";
import { InviteMultipleUsersResponse } from "../interfaces/invite-multiple-users-response";
import { getAzureError } from "../util/get-azure-error";
import {isValidMongoObjectId} from "../util";

const MICROSOFT_GRAPH_HOST = "https://graph.microsoft.com/v1.0";
const MICROSOFT_LOGIN_HOST = "https://login.microsoftonline.com";
const REDIRECT_URI = process.env.REDIRECT_URI || `${process.env.DOMAIN_URL}/v1/redirect`;
const BACKEND_REDIRECT_URI = `${process.env.DOMAIN_URL}/v1/redirect`;

const GET_USERS_SELECT_FIELDS = `$select=displayName,givenName,surname,mail,
  otherMails,streetAddress,country,state,city,postalCode,mobilePhone,jobTitle,
  accountEnabled,userPrincipalName,id,externalUserState,creationType,companyName`;

const config = {
    auth: {
        clientId: process.env.CLIENT_ID!,
        authority: `${MICROSOFT_LOGIN_HOST}/${process.env.TENANT_ID!}/`,
        clientSecret: process.env.CLIENT_SECRET!
    }
};

export class microsoftGraphAPI {

    private static async batchRequest(token: string, requests: CreateBatchRequestData[]): Promise<BatchRequestIndividualResponse[]> {
        try {
            // Batch request
            const batchRequestResponse = await axios.post(`${MICROSOFT_GRAPH_HOST}/$batch`, {
                requests: requests
            }, {
                headers: {
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "Authorization": `Bearer ${token}`
                }
            });

            const result: BatchRequestResponse = batchRequestResponse.data;

            const responses: BatchRequestIndividualResponse[] = result.responses.map(response => {
                return {
                    ...response,
                    requestSucceed: response.status >= 200 && response.status <= 209,
                };
            });

            return responses;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async getAccessToken(): Promise<string> {
        try {
            // Check whether the token already exist and it"s valid, otherwise call the API and get the token
            if (app.locals.microsoftGraphAPIToken.accessToken
        && (app.locals.microsoftGraphAPIToken.expiresIn > new Date().valueOf())) {
                return app.locals.microsoftGraphAPIToken.accessToken;
            }
            else {
                const appAccessTokenResponse = await axios.post(
                    `${MICROSOFT_LOGIN_HOST}/${process.env.TENANT_ID}/oauth2/v2.0/token`,
                    qs.stringify({
                        client_id: process.env.CLIENT_ID,
                        client_secret: process.env.CLIENT_SECRET,
                        scope: "https://graph.microsoft.com/.default",
                        grant_type: "client_credentials",
                    }),
                    {
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                        },
                    }
                );

                app.locals.microsoftGraphAPIToken = {
                    accessToken: appAccessTokenResponse.data.access_token,
                    expiresIn: new Date().setSeconds(new Date().getSeconds() + appAccessTokenResponse.data.expires_in - (60 * 5)) //Now token will be valid for 55 minutes, actual was 60 minutes.
                };
                return app.locals.microsoftGraphAPIToken.accessToken;
            }
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async createOrganization({ token, data }: CreateOrganizationAzureParams): Promise<CreateOrganizationAzureResponse> {
        try {
            const createOrgData: CreateOrganizationAzureRequestData = {
                "displayName": data.displayName,
                "mailEnabled": false,
                "mailNickname": crypto.randomBytes(10).toString("hex"),
                "securityEnabled": true
            };

            if (data.description) {
                createOrgData.description = data.description;
            }

            const createOrganizationResponse: AxiosResponse = await axios.post(
                `${MICROSOFT_GRAPH_HOST}/groups`,
                createOrgData,
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            return createOrganizationResponse.data;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async addOrganizationInApplication({ token, orgId }: AddOrganizationInApplicationAzureParams): Promise<AxiosResponse> {
        try {
            const addOrganizationEnterpriseResp = await axios.post(
                `${MICROSOFT_GRAPH_HOST}/groups/${orgId}/appRoleAssignments`,
                {
                    principalId: orgId, // Group id / Org Id
                    resourceId: process.env.ENTERPRISE_OBJECT_ID, // Service princiapl Id (Enterprise Object Id)
                    appRoleId: "00000000-0000-0000-0000-000000000000", // Default App Role Id 00000000-0000-0000-0000-000000000000 if app Role is not defined in the application otherwise must provide valid appRoleId
                },
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            return addOrganizationEnterpriseResp;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async addMembersInOrganization({ token, orgId, members }: AddOrganizationMembersAzureParams): Promise<AxiosResponse> {
        try {
            const usersData = members.map(id => {
                return `${MICROSOFT_GRAPH_HOST}/directoryObjects/${id}`;
            });

            const addMembersInOrganizationResp = await axios.patch(
                `${MICROSOFT_GRAPH_HOST}/groups/${orgId}`,
                {
                    "<EMAIL>": usersData
                },
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            return addMembersInOrganizationResp;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async inviteMultipleUsers({ token, users, invitationMessage }: InviteMultipleUsersAzureParams): Promise<InviteMultipleUsersResponse[]> {
        try {
            // prepare Requests data for batch request
            const requestURL = `/invitations`;
            const headers = {
                "Authorization": `Bearer ${token}`,
                "Content-type": "application/json"
            };

            const requests: CreateBatchRequestData[] = [];
            users.forEach((user, index) => {
                requests.push({
                    id: String(index + 1),
                    url: requestURL,
                    method: "POST",
                    headers: headers,
                    body: {
                        "invitedUserDisplayName": user.displayName,
                        "invitedUserEmailAddress": user.email,
                        "inviteRedirectUrl": REDIRECT_URI, //Add frontend dashboard URL
                        "sendInvitationMessage": false,
                        "invitedUserMessageInfo": {
                            "customizedMessageBody": invitationMessage
                        }
                    }
                });
            });

            // call batch request
            const batchRequestResponse = await this.batchRequest(token, requests);

            // Process that response
            const result: InviteMultipleUsersResponse[] = [];

            batchRequestResponse.forEach(response => result.push({
                status: response.requestSucceed || false,
                id: response.body ? response.body.invitedUser.id : null,
                data: {
                    ...(users[response.id as number - 1] ?? {}),
                    inviteRedeemUrl: response.body.inviteRedeemUrl
                },
                body: response.body
            }));

            return result;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async addOwnersInOrganization({ token, orgId, owners }: AddOrganizationOwnersAzureParams): Promise<IdBatchRequestStatus> {
        try {
            // Preapre Requests data for batch request
            const requestURL = `/groups/${orgId}/owners/$ref`;
            const headers = {
                "Authorization": `Bearer ${token}`,
                "Content-type": "application/json"
            };

            const requests: CreateBatchRequestData[] = [];
            owners.forEach((owner, index) => {
                requests.push({
                    id: String(index + 1),
                    url: requestURL,
                    method: "POST",
                    headers: headers,
                    body: {
                        "@odata.id": `${MICROSOFT_GRAPH_HOST}/users/${owner}`
                    }
                });
            });

            // call batch request
            const batchRequestResponse = await this.batchRequest(token, requests);

            // Process that response
            const result: IdBatchRequestStatus = {};

            batchRequestResponse.forEach(response => {
                result[owners[response.id as number - 1]] = {
                    status: response.requestSucceed || false,
                    body: response.body
                };
            });

            return result;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async addOwnerInOrganization({ token, orgId, userId }: AddOrganizationOwnerMemberAzureParams): Promise<AxiosResponse> {
        try {
            const addOwnerInOrganizationResp = await axios.post(
                `${MICROSOFT_GRAPH_HOST}/groups/${orgId}/owners/$ref`,
                {
                    "@odata.id": `${MICROSOFT_GRAPH_HOST}/users/${userId}`
                },
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            return addOwnerInOrganizationResp;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async isUserOwnerOfOrganization({ token, orgId, email }: CheckOrganizationOwnerMemberAzureParams): Promise<boolean> {
        try {
            const response = await axios.get(
                `${MICROSOFT_GRAPH_HOST}/groups/${orgId}/owners?$count=true&$filter=startswith(mail, '${email}')`,
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                        "ConsistencyLevel": "eventual"
                    },
                }
            );

            return response.data["@odata.count"] === 1;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async isUserMemberOfOrganization({ token, orgId, email }: CheckOrganizationOwnerMemberAzureParams): Promise<boolean> {
        try {
            const response = await axios.get(
                `${MICROSOFT_GRAPH_HOST}/groups/${orgId}/members?$count=true&$filter=startswith(mail, '${email}')`,
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                        "ConsistencyLevel": "eventual"
                    },
                }
            );

            return response.data["@odata.count"] === 1;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async addMemberInOrganization({ token, orgId, userId }: AddOrganizationOwnerMemberAzureParams): Promise<AxiosResponse> {
        try {
            const addMemberInOrganizationResp = await axios.post(
                `${MICROSOFT_GRAPH_HOST}/groups/${orgId}/members/$ref`,
                {
                    "@odata.id": `${MICROSOFT_GRAPH_HOST}/directoryObjects/${userId}`
                },
                {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            return addMemberInOrganizationResp;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async removeUsersFromOrganization({ token, orgId, users }: RemoveOrganizationUsersAzureParams): Promise<IdBatchRequestStatus> {
        try {
            // Preapre Requests data for batch request
            const headers = {
                "Authorization": `Bearer ${token}`,
            };

            const requests: CreateBatchRequestData[] = [];
            users.forEach((user, index) => {
                requests.push({
                    id: String(index + 1),
                    url: `/groups/${orgId}/members/${user}/$ref`,
                    method: "DELETE",
                    headers: headers,
                });
            });

            // call batch request
            const batchRequestResponse = await this.batchRequest(token, requests);

            // Process that response
            const result: IdBatchRequestStatus = {};

            batchRequestResponse.forEach(response => {
                result[users[response.id as number - 1]] = {
                    status: response.requestSucceed || false,
                    body: response.body
                };
            });

            return result;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async removeOwnersFromOrganization({ token, orgId, owners }: RemoveOrganizationOwnersAzureParams): Promise<IdBatchRequestStatus> {
        try {
            // Preapre Requests data for batch request
            const headers = {
                "Authorization": `Bearer ${token}`,
            };

            const requests: CreateBatchRequestData[] = [];
            owners.forEach((owner, index) => {
                requests.push({
                    id: String(index + 1),
                    url: `/groups/${orgId}/owners/${owner}/$ref`,
                    method: "DELETE",
                    headers: headers,
                });
            });

            // call batch request
            const batchRequestResponse = await this.batchRequest(token, requests);

            // Process that response
            const result: IdBatchRequestStatus = {};

            batchRequestResponse.forEach(response => {
                result[owners[response.id as number - 1]] = {
                    status: response.requestSucceed || false,
                    body: response.body
                };
            });

            return result;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async InviteUser({ accessToken, email, invitationMessage, displayName }: InviteUserParams): Promise<{azureId: string, inviteRedeemUrl: string}> {
        try {
            const invitedUserDetails: InviteUserResponse = await axios.post(`${MICROSOFT_GRAPH_HOST}/invitations`,
                {
                    "invitedUserDisplayName": displayName,
                    "invitedUserEmailAddress": email,
                    "inviteRedirectUrl": REDIRECT_URI, //Add frontend dashboard URL
                    "sendInvitationMessage": false,
                    "invitedUserMessageInfo": {
                        "customizedMessageBody": invitationMessage
                    }
                },
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json"
                    },
                }
            );

            return {
                azureId: invitedUserDetails.data.invitedUser.id,
                inviteRedeemUrl: invitedUserDetails.data.inviteRedeemUrl
            };
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async verifyCodeAndGetDetails({ code, codeVerifier }: { code: string, codeVerifier: string }) {
        try {
            const options: VerifyCodeAzureParams = {
                client_id: `${process.env.CLIENT_ID}`,
                code: code,
                scope: ".default",
                grant_type: "authorization_code",
                redirect_uri: `${REDIRECT_URI}`,
            };

            const headers: { "Content-Type": string, "Origin"?: string } = {
                "Content-Type": "application/x-www-form-urlencoded"
            };

            if (codeVerifier) {
                options.code_verifier = codeVerifier;
                headers.Origin = REDIRECT_URI;
            }
            else {
                options.client_secret = `${process.env.CLIENT_SECRET}`;
                options.redirect_uri = BACKEND_REDIRECT_URI;
            }

            const verifyCodeResponse = await axios.post(
                `${MICROSOFT_LOGIN_HOST}/${process.env.TENANT_ID}/oauth2/v2.0/token`,
                qs.stringify(options),
                {
                    headers: headers,
                }
            );

            return verifyCodeResponse;
        }
        catch (error) {
            let errorObject;
            if (axios.isAxiosError(error)) {
                errorObject = error.response?.data;
            }
            throw new ExternalServerError([errorObject || getAzureError(error)]);
        }
    }

    static async getUserDetails({ accessToken }: { accessToken: string }) {
        try {
            const userDetails = await axios.get(
                `${MICROSOFT_GRAPH_HOST}/me`,
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json"
                    },
                }
            );

            return userDetails;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async getAzureUserDetailsById({ accessToken, userId }: { accessToken: string, userId: string }) {
        try {
            const userDetails = await axios.get(
                `${MICROSOFT_GRAPH_HOST}/users/${userId}?${GET_USERS_SELECT_FIELDS}`,
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json"
                    },
                }
            );

            return userDetails;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async getAzureOrganizationDetailsById({ accessToken, organizationId }: { accessToken: string, organizationId: string }) {
        try {
            const organizationDetails = await axios.get(
                `${MICROSOFT_GRAPH_HOST}/groups/${organizationId}?$select=displayName`,
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json"
                    },
                }
            );

            return organizationDetails;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async refreshToken({ refreshToken, pkceEnabled }: { refreshToken: string, pkceEnabled: boolean }) {
        try {
            const options: RefreshTokenAzureParams = {
                client_id: `${process.env.CLIENT_ID}`,
                scope: ".default",
                grant_type: "refresh_token",
                refresh_token: refreshToken
            };

            const headers: { "Content-Type": string, "Origin"?: string } = {
                "Content-Type": "application/x-www-form-urlencoded"
            };

            if (pkceEnabled) {
                headers.Origin = REDIRECT_URI;
            }
            else {
                options.client_secret = `${process.env.CLIENT_SECRET}`;
            }

            const refreshTokenResp = await axios.post(
                `${MICROSOFT_LOGIN_HOST}/${process.env.TENANT_ID}/oauth2/v2.0/token`,
                qs.stringify(options),
                {
                    headers: headers,
                }
            );

            return refreshTokenResp;
        }
        catch (err) {
            console.error("oauth2 token err");
            console.error(err);
            throw new InvalidTokenError();
        }
    }

    static async getUsers({ accessToken, top, skipToken }: SearchUsersParams) {
        try {
            let reqUrl = `${MICROSOFT_GRAPH_HOST}/users?${GET_USERS_SELECT_FIELDS}&$orderby=displayName&$count=true`;
            reqUrl = top ? `${reqUrl}&$top=${top}` : reqUrl;
            reqUrl = skipToken ? `${reqUrl}&$skiptoken=${skipToken}` : reqUrl;

            const usersList: GetUsersListResponse = await axios.get(
                reqUrl,
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                        "ConsistencyLevel": "eventual"
                    },
                }
            );

            return usersList;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async searchUsers({ accessToken, search, top, skipToken }: SearchUsersParams): Promise<GetUsersListResponse> {
        try {
            const replacedSearch = encodeURIComponent(encodeURIComponent((search ?? "").replace(/\\/g, `\\\\`).replace(/"/g, `\\"`)));

            let reqUrl = `${MICROSOFT_GRAPH_HOST}/users?${GET_USERS_SELECT_FIELDS}&$search="displayName:${replacedSearch}" OR "mail:${replacedSearch}"&$count=true`;
            reqUrl = top ? `${reqUrl}&$top=${top}` : reqUrl;
            reqUrl = skipToken ? `${reqUrl}&$skiptoken=${skipToken}` : reqUrl;

            return await axios.get(
                reqUrl,
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                        "ConsistencyLevel": "eventual"
                    },
                }
            );
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async searchUsersByMultipleEmails({ accessToken, emails, top, skipToken }: SearchUsersParams): Promise<GetUsersListResponse> {
        try {
            // eslint-disable-next-line max-len
            const mailsQuery = emails ? emails.map(email => (`"mail:${encodeURIComponent(encodeURIComponent((email ?? "").trim().replace(/\\/g, `\\\\`).replace(/"/g, `\\"`)))}"`)).join(" OR ") : [];
            let reqUrl = `${MICROSOFT_GRAPH_HOST}/users?${GET_USERS_SELECT_FIELDS}&$search=${mailsQuery}&$count=true`;
            reqUrl = top ? `${reqUrl}&$top=${top}` : reqUrl;
            reqUrl = skipToken ? `${reqUrl}&$skiptoken=${skipToken}` : reqUrl;

            return await axios.get(
                reqUrl,
                {
                    headers: {
                        "Authorization": `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                        "ConsistencyLevel": "eventual"
                    },
                }
            );
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async updateOrganization({ token, orgId, data }: UpdateOrganizationNameAzureParams): Promise<AxiosResponse> {
        try {
            if(this.checkAzureId(orgId)) {
                const updateOrganizationNameResponse = await axios.patch(
                    `${MICROSOFT_GRAPH_HOST}/groups/${String(orgId)}`,
                    data,
                    {
                        headers: {
                            "Authorization": `Bearer ${token}`,
                            "Content-Type": "application/json"
                        },
                    }
                );
                return updateOrganizationNameResponse;
            } else {
                throw new InvalidResourceIdBadRequestError([
                    {
                        name: "orgId",
                        value: orgId,
                        message: "These values are invalid."
                    }
                ]);
            }
        }
        catch (error) {
            if (axios.isAxiosError(error)) {
                throw new ExternalServerError([error.response?.data.error]);
            } else {
                throw error;
            }

        }
    }

    static async updateMultipleUsersStatus({ accessToken, users, status }: { accessToken: string, users: string[], status: boolean }): Promise<IdBatchRequestStatus> {
        try {
            // Preapre Requests data for batch request
            const headers = {
                "Authorization": `Bearer ${accessToken}`,
                "Content-type": "application/json"
            };

            const requests: CreateBatchRequestData[] = [];
            users.forEach((user, index) => {
                requests.push({
                    id: String(index + 1),
                    url: `/users/${user}`,
                    method: "PATCH",
                    headers: headers,
                    body: {
                        accountEnabled: status
                    }
                });
            });

            // call batch request
            const batchRequestResponse = await this.batchRequest(accessToken, requests);

            // Process that response
            const result: IdBatchRequestStatus = {};

            batchRequestResponse.forEach(response => {
                result[users[response.id as number - 1]] = {
                    status: response.requestSucceed || false,
                    body: response.body
                };
            });

            return result;
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static checkAzureId(azureId: string) {
        const azureIDRegex = /^[0-9a-zA-Z]{8}-[0-9a-zA-Z]{4}-[1-5][0-9a-zA-Z]{3}-[0-9a-zA-Z][0-9a-zA-Z]{3}-[0-9a-zA-Z]{12}$/;
        return azureIDRegex.test(azureId);
    }

    static async updateUserDetails({
        accessToken, azureId, firstName, lastName, displayName,
        userLocation, officePhone, jobTitle }: UpdateUserAzureDetailsParams) {
        try {
            const reqBody: Record<string, any> = {
                givenName: firstName ?? undefined,
                surname: lastName ?? undefined,
                displayName: displayName ?? undefined,
                mobilePhone: officePhone || officePhone == null ? officePhone : undefined,
                jobTitle: jobTitle || jobTitle == null ? jobTitle : undefined,
            };

            // If user location has been passed then update state, city and postal code
            if(userLocation) {
                reqBody.streetAddress = userLocation.addressline1 && userLocation.addressline2 ?
                    `${userLocation.addressline1}, ${userLocation.addressline2}` : userLocation.addressline1 ?? undefined;
                reqBody.country = userLocation.country || userLocation.country === null ? userLocation.country : undefined;
                reqBody.state = userLocation.state || userLocation.state == null ? userLocation.state : undefined;
                reqBody.city = userLocation.city || userLocation.city == null ? userLocation.city : undefined;
                reqBody.postalCode = userLocation.zip || userLocation.zip == null ? userLocation.zip : undefined;
            }

            if(this.checkAzureId(azureId)) {
                return await axios.patch(
                    `${MICROSOFT_GRAPH_HOST}/users/${azureId}`,
                    {
                        ...reqBody
                    },
                    {
                        headers: {
                            "Authorization": `Bearer ${accessToken}`,
                            "Content-Type": "application/json"
                        },
                    }
                );
            }
            else {
                throw new InvalidResourceIdBadRequestError([
                    {
                        name: "azureId",
                        value: azureId,
                        message: "These values are invalid."
                    }
                ]);

            }

        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async updateUserEmail({ accessToken, azureId, email }: UpdateUserEmailParams) {
        try {
            return await axios.patch(
                `${MICROSOFT_GRAPH_HOST}/users/${azureId}`,
                {
                    mail: email
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                    },
                }
            );
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static async reinviteUser({ accessToken, azureId, email }: UpdateUserEmailParams) {
        try {
            return await axios.post(
                `https://graph.microsoft.com/beta/invitations`,
                {
                    invitedUserEmailAddress: email,
                    inviteRedirectUrl: REDIRECT_URI,
                    sendInvitationMessage: true,
                    resetRedemption: true,
                    invitedUser: {
                        id: azureId,
                    },
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                    },
                }
            );
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }

    static getConfidentialClientApplication() {
        if (app.locals.ConfidentialClientApplication.clientSecret !== process.env.CLIENT_SECRET) {
            const pca = new ConfidentialClientApplication(config);
            app.locals.ConfidentialClientApplication = pca;
        }

        return app.locals.ConfidentialClientApplication;
    }

    static async login() {
        try {
            const pca = microsoftGraphAPI.getConfidentialClientApplication();

            return await pca.getAuthCodeUrl({
                scopes: ["user.read", "offline_access"],
                redirectUri: BACKEND_REDIRECT_URI
            });
        }
        catch (error) {
            throw new ExternalServerError([getAzureError(error)]);
        }
    }
}
