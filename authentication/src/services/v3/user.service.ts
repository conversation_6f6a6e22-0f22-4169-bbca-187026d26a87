/* eslint-disable no-param-reassign */
/* eslint-disable security/detect-object-injection */
import jwt from "jsonwebtoken";
import { UserV2, UserV2Doc } from "../../models/v2/users-v2";
import { generateObjectId, generateTemporaryToken, isBusinessEmail, scryptHash, scryptHashVerify } from "../../util";
import mongoose, { ClientSession } from "mongoose";
import { JsonWebTokenV2Service } from "../v2/json-web-token";
import { BasicResourceValueUnacceptableConflictError, BodyInvalidBadRequestError, ConflictErrorCodes, InternalServerError, InvalidRequestTokenError,
    InvalidRequestTokenErrorCode, NotFoundCode, ResourceNotFoundError } from "@moxfive-llc/common";
import { TokenTypesEnum } from "../../enums/token-types";
import { Auth0Service } from "../auth0";
import { userUpdatedPublisherV2Wrapper } from "../../util/v2/user-updated-publisher-wrapper";
import { MongoTransaction } from "../mongo-transaction";
import { userV2ToV1Sync } from "../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../enums/operation-types.enum";
import { Request, Response } from "express";
import app from "../../app";
import { OrganizationV2 } from "../../models/v2/oragnizations-v2";
import { Connections } from "../../models/connections";
import { fetchUserDeviceInformation } from "../../util/fetch-user-device-information";
import { aesDecrypt } from "../../util/aes-decrypt";
import { IRefreshTokenParamsV2 } from "../../interfaces";

export class UserServiceV3 {
    private tokenDetails: any;
    private user: UserV2Doc | null;
    private deviceId: mongoose.Types.ObjectId | string | null;

    constructor({ user }: { user?: UserV2Doc | null }) {
        this.tokenDetails = null;
        this.user = user ?? null;
        this.deviceId = null;
    }

    async processUserLogin({
        idToken, externalRefreshToken, pkceEnabled, passwordLogin, request, response, refreshTokenData = null,
    }: { idToken: string, externalRefreshToken: string, pkceEnabled: boolean, passwordLogin: boolean, request: Request, response: Response,
        refreshTokenData?: IRefreshTokenParamsV2 | null }) {
        const mongoTransaction = new MongoTransaction();
        await mongoTransaction.startTransaction();

        try {
            // Decode the ID token
            this.tokenDetails = jwt.decode(idToken);

            // Fetch users doc and check whether user is present or not
            this.user = await UserV2.findOne({ email: this.tokenDetails.email.toLowerCase() }).session(mongoTransaction.session);
            if (!this.user || !this.user.isEnabled) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // If it is password login then set user id in externalAuth0LoginUserId otherwise in externalIDPLoginUserId
            if (passwordLogin) {
                this.user.externalAuth0LoginUserId = this.tokenDetails.sub;
            }
            else {
                this.user.externalIDPLoginUserId = this.tokenDetails.sub;
            }

            // If it is refresh token then update expiration time of that device otherwise add new device
            if(refreshTokenData) {
                this.deviceId = refreshTokenData.deviceId;
            }
            else {
                // This function is used to add device
                this.addDevice({ passwordLogin, request });
            }

            // This functions is used to generate ID and Refresh Tokens
            const { accessToken, refreshToken } = await this.generateTokens({
                externalRefreshToken,
                pkceEnabled,
                passwordLogin
            });

            // This function is used to sync attributes b/w platform and Auth0
            await this.syncAttributes(request);

            if(refreshTokenData) {
                await UserV2.findOneAndUpdate({
                    _id: this.user._id,
                    "devices.id": this.deviceId
                }, {
                    $set: {
                        "devices.$.expirationDateTime": this.getDeviceExpirationDateTime()
                    }
                });
            }
            else {
                this.user.lastSignIn = new Date().toISOString();

                // If account setup done is false then set it to true
                if(!this.user.isAccountSetupDone) {
                    this.user.isAccountSetupDone = true;
                }

                // Save the user details
                await this.user.save({ session: mongoTransaction.session });

                // Update user's details to v1
                await userV2ToV1Sync({
                    user: this.user.toObject(),
                    operationType: OperationTypesEnums.UPDATE,
                    session: mongoTransaction.session
                });
            }

            // Commit the transaction
            await mongoTransaction.commitTransaction();

            if(!refreshTokenData) {
                // Publish NATS Event
                await userUpdatedPublisherV2Wrapper(this.user);
            }

            // Set access token and refresh token in the response
            response.cookie("Token1", accessToken, app.locals.cookieOptions);
            response.cookie("Token2", refreshToken, app.locals.cookieOptions);
        }
        catch (error: any) {
            // Abort the transaction
            await mongoTransaction.abortTransaction();

            throw new Error(error);
        }
    }

    // This function is responsible for adding new device
    private addDevice({ passwordLogin, request }: {
        passwordLogin: boolean, request: Request
    }) {
        if (this.user) {
            // Generate deviceId and fetch device info
            this.deviceId = generateObjectId();

            const userAgent = request.headers["user-agent"] ?? "";
            const device = fetchUserDeviceInformation(userAgent);

            // Add entry in the user devices and allowedLoginTokens
            (this.user.devices ?? []).push({
                id: this.deviceId,
                device,
                passwordLogin,
                expirationDateTime: this.getDeviceExpirationDateTime()
            });

            (this.user.allowedLoginTokens as string[]).push(String(this.deviceId));
        }
    }

    async generateTokens({ externalRefreshToken, pkceEnabled, passwordLogin }: {
        externalRefreshToken: string, pkceEnabled: boolean, passwordLogin: boolean
    }) {
        // If user not found then throw an error
        if (!this.user) {
            throw new InternalServerError();
        }

        // Generate Access Token
        const { accessToken } = await JsonWebTokenV2Service.getAccessToken({
            id: String(this.user._id),
            email: this.user.email,
            organizationId: String(this.user.organization?.id),
            deviceId: this.deviceId ? String(this.deviceId) : null,
            passwordLogin
        });

        // Generate Refresh Token
        const { refreshToken } = await JsonWebTokenV2Service.getRefreshToken({
            id: String(this.user._id),
            refreshToken: externalRefreshToken,
            deviceId: this.deviceId ? String(this.deviceId) : null,
            pkceEnabled,
            passwordLogin
        });

        return {
            accessToken,
            refreshToken
        };
    }

    async syncAttributes(req: Request) {
        if (this.user) {
            // Prepare array of external userIds
            const externalUserIds: string[] = [];
            if (this.user.externalAuth0LoginUserId) {
                externalUserIds.push(this.user.externalAuth0LoginUserId);
            }

            if (this.user.externalIDPLoginUserId) {
                externalUserIds.push(this.user.externalIDPLoginUserId);
            }

            if (!externalUserIds.length) {
                return;
            }

            // Fetch access token
            const auth0Service = new Auth0Service(req, false);
            const { accessToken } = await auth0Service.fetchApplicationAccessToken();

            // Loop through all external userIds and update user details
            for (const userId of externalUserIds) {
                // Update user details
                // eslint-disable-next-line no-await-in-loop
                await auth0Service.updateUserDetails({
                    accessToken,
                    userId,
                    data: {
                        userMetadata: {
                            firstName: this.user.firstName,
                            lastName: this.user.lastName,
                            displayName: this.user.displayName,
                            userLocation: this.user.userLocation,
                            officePhone: this.user.officePhone,
                            jobTitle: this.user.jobTitle,
                            organization: this.user.organization,
                            organizationTypes: this.user.organizationTypes?.map(type => type.value),
                            isEnabled: this.user.isEnabled,
                        },
                        name: this.user.name
                    }
                });
            }
        }
    }

    static async verifyToken({ session, userId, token, type, tokenExpiredError = true, user = null, encrypt = false }: {
        userId: string,
        session?: ClientSession | null,
        token: string,
        type: TokenTypesEnum,
        tokenExpiredError?: boolean,
        user?: UserV2Doc | null,
        encrypt?: boolean
    }) {
        const tokenFields: Record<TokenTypesEnum, keyof UserV2Doc> = {
            [TokenTypesEnum.FORGOT_PASSWORD]: "forgotPasswordToken",
            [TokenTypesEnum.EMAIL_VERIFICATION]: "emailVerificationToken",
            [TokenTypesEnum.PASSWORD_SETUP]: "passwordSetupToken",
            [TokenTypesEnum.ACTION_TOKEN]: "actionToken",
        };

        const tokenExpiryFields: Record<TokenTypesEnum, keyof UserV2Doc> = {
            [TokenTypesEnum.FORGOT_PASSWORD]: "forgotPasswordTokenExpiry",
            [TokenTypesEnum.EMAIL_VERIFICATION]: "emailVerificationTokenExpiry",
            [TokenTypesEnum.PASSWORD_SETUP]: "passwordSetupTokenExpiry",
            [TokenTypesEnum.ACTION_TOKEN]: "actionTokenExpiry",
        };

        const tokenFieldName = tokenFields[type];
        const tokenExpiryFieldName = tokenExpiryFields[type];

        // Check user is present or not and check if token is present or not, if not then throw an error
        if(!user) {
            if (session) {
                user = await UserV2.findById(String(userId)).session(session);
            }
            else {
                user = await UserV2.findById(String(userId));
            }
        }

        if (!user || !user[tokenFieldName]) {
            throw new InvalidRequestTokenError(`Invalid Token.`, InvalidRequestTokenErrorCode.INVALID_TOKEN);
        }

        if (!user[tokenExpiryFieldName as keyof UserV2Doc]) {
            throw new InternalServerError();
        }

        // Check whethre token is matching or not
        let tokenMatched = false;
        if(encrypt) {
            const decryptedToken = aesDecrypt(user[tokenFieldName] as string, process.env.RESPONSE_ENCRYPTION_KEY!);
            tokenMatched = token === decryptedToken;
        }
        else {
            tokenMatched = await scryptHashVerify(token, user[tokenFieldName] as string);
        }
        if (!tokenMatched) {
            throw new InvalidRequestTokenError(`Invalid Token.`, InvalidRequestTokenErrorCode.INVALID_TOKEN);
        }

        // Check whether token is expired or not
        const currentDate = new Date();
        if (currentDate > (user[tokenExpiryFieldName] as Date)) {
            if (tokenExpiredError) {
                throw new InvalidRequestTokenError("Token Expired.", InvalidRequestTokenErrorCode.EXPIRED_TOKEN);
            }
            else {
                return {
                    user,
                    tokenExpired: true
                };
            }

        }

        return { user };
    }

    static async getUserLoginOptions(email: string, internalLogin = false) {
        const loginOptions: {
            verified: boolean | null,
            password?: boolean,
            enterpriseConnection?: string,
            enterpriseConnectionType?: string
        } = {
            verified: null
        };

        // If user does not present or user is not enabled or organization is not enabled then set password as true and return
        const user = await UserV2.findOne({ email: String(email) }).lean().exec();
        if (!user || !user.isEnabled || !user.organization) {
            loginOptions.password = true;
            return loginOptions;
        }

        // If user's email is not verified then set emailVerified to false and return
        if(!user.isEmailVerified) {
            loginOptions.verified = false;
            return loginOptions;
        }

        const organization = await OrganizationV2.findById(user.organization.id).lean().exec();
        if (!organization || !organization.isEnabled) {
            loginOptions.password = true;
            return loginOptions;
        }

        // Check whether enterprise connection is enabled for this organization or not, if not then set password to true and return
        const connection = await Connections.findOne({
            "organization.id": String(organization._id)
        });

        if (!connection || !connection.isActive) {
            loginOptions.password = true;
            return loginOptions;
        }

        // If it's business email then set enterpriseConnectionURL
        const businessEmail = isBusinessEmail(email);
        if (businessEmail) {
            loginOptions.enterpriseConnectionType = connection.connectionType.value;
            loginOptions.enterpriseConnection = UserServiceV3.getEnterpriseConnectionURL({
                connection: connection.connectionName ?? "",
                internalLogin
            });
        }
        // Otherwise check enforceAuthenticaiton and based on that return response
        else {
            // If enforceAuthentication is false and user is part of Auth0 Login User then set password as true
            if (!connection.enforceAuthentication && user.externalAuth0LoginUserId) {
                loginOptions.password = true;
            }

            loginOptions.enterpriseConnectionType = connection.connectionType.value;
            loginOptions.enterpriseConnection = UserServiceV3.getEnterpriseConnectionURL({
                connection: connection.connectionName ?? "",
                internalLogin
            });
        }
        return loginOptions;
    }

    static getEnterpriseConnectionURL({ connection, internalLogin = false }: {
        connection: string,
        internalLogin?: boolean
    }) {
        // eslint-disable-next-line max-len
        return `${process.env.AUTH0_DOMAIN}/authorize?client_id=${internalLogin ? process.env.AUTH0_WEB_APP_CLIENT_ID : process.env.AUTH0_SPA_APP_CLIENT_ID}&response_type=code&connection=${connection}&scope=openid%20profile%20phone%20email%20offline_access&redirect_uri=${internalLogin ? Auth0Service.BACKEND_REDIRECT_URI : Auth0Service.REDIRECT_URI}?connection=${connection}`;
    }

    static async destroyAllUserActiveSessions(user: UserV2Doc) {
        user.devices = [];
        user.allowedLoginTokens = [];
    }

    static generateForgotPasswordURL(token: string, u: string) {
        return `${process.env.DOMAIN_URL}/setup-password?token=${token}&u=${u}`;
    }

    static generateWelcomeEmailURL(token: string, u: string) {
        return `${process.env.DOMAIN_URL}/account-setup?token=${token}&u=${u}`;
    }

    static generateEmailChangePasswordSetupURL(token: string, u: string) {
        return `${process.env.DOMAIN_URL}/account-setup?token=${token}&u=${u}&ec=true`;
    }

    static async changeUserEmail({ user, email, req }: {
        user: UserV2Doc, email: string, req: Request
    }) {
        let url: string | null = null;
        let deleteAuth0User = false;
        let updateAuth0userEmail = false;
        let createAuth0user = false;

        // If provided email is same as current user email then throw an errors
        if(user.email === email) {
            throw new BodyInvalidBadRequestError([
                { name: "email", value: email, message: "The provided email is already in use by your account." }
            ]);
        }

        // Check if user is already exists or not, if yes then throw an error
        const userWithNewEmail = await UserV2.findOne({ email: String(email) }, { _id: 1 }).lean().exec();
        if(userWithNewEmail) {
            throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.USER_EMAIL_ALREADY_REGISTERED, "The email is already registered to another account.");
        }

        const connection = await Connections.findOne({ "organization.id": user.organization?.id }).lean().exec();

        /*
         * If connection is added
         *  If enforcement is
         *      true
         *          delete auth0 user if present as for enforcement true every login will be done using IDP
         *      false
         *          If current email is business email
         *              true
         *                  delete auth0 user if present as for business email user we will allow login option with IDP only.
         *              false
         *                  If user is already exist in Auth0 then update user email otherwise create new user
         * Otherwise just update user email in Auth0
         */
        if(connection) {
            if(connection.enforceAuthentication) {
                if(user.externalAuth0LoginUserId) {
                    deleteAuth0User = true;
                }
            }
            else {
                const businessEmail = isBusinessEmail(email);
                const domain = email.split("@")[1];

                // If domain is not valid then throw an error
                if (businessEmail && connection.domains && (connection.domains.length || !connection.domains.includes(domain))) {
                    throw new BodyInvalidBadRequestError([
                        { name: "email", value: email, message: `Email domain must be one of the allowed domains (${connection.domains}).` }
                    ]);
                }

                if(businessEmail) {
                    if(user.externalAuth0LoginUserId) {
                        deleteAuth0User = true;
                    }
                }
                else {
                    if(user.externalAuth0LoginUserId) {
                        updateAuth0userEmail = true;
                    }
                    else {
                        createAuth0user = true;
                    }
                }
            }
        }
        // If connection is not present then just update the user email
        else {
            if(user.externalAuth0LoginUserId) {
                updateAuth0userEmail = true;
            }
        }

        // Perform action
        const auth0Service = new Auth0Service(req);
        const { accessToken } = await auth0Service.fetchApplicationAccessToken();

        if(updateAuth0userEmail) {
            if(!user.externalAuth0LoginUserId) {
                throw new InternalServerError();
            }

            await auth0Service.updateUserDetails({
                accessToken,
                userId: user.externalAuth0LoginUserId,
                data: {
                    email
                }
            });

            // user.isAccountSetupDone = false;
        }
        else if(deleteAuth0User) {
            if(!user.externalAuth0LoginUserId) {
                throw new InternalServerError();
            }

            await auth0Service.removeUser({
                accessToken,
                userId: user.externalAuth0LoginUserId
            });

            if(user.isEmailVerified) {
                user.isAccountSetupDone = true;
            }
            user.externalAuth0LoginUserId = null;
        }
        else if(createAuth0user) {
            const exteranlUserId = await auth0Service.addUser({
                accessToken,
                userEmail: email,
                userDetails: user.toObject(),
                organization: {
                    id: String(user.organization?.id),
                    name: user.organization?.value,
                    organizationTypes: user.organizationTypes ?? []
                }
            });

            if(!exteranlUserId) {
                throw new InternalServerError();
            }

            user.externalAuth0LoginUserId = exteranlUserId;
        }

        // If user's email is not verified then need to send welcome email so, for that geenrate email verification token
        if(!user.isEmailVerified) {
            const { token, expiry } = generateTemporaryToken({
                minutes: 24 * 60
            });
            const hashedToken = await scryptHash(token);

            // Update user's email verification token with expiry
            user.emailVerificationToken = hashedToken;
            user.emailVerificationTokenExpiry = expiry;

            url = UserServiceV3.generateWelcomeEmailURL(token, String(user._id));

        }
        else {
            // If need to create Auth0 user then generate password setup token and expiry.
            if(createAuth0user) {
                const { token, expiry } = generateTemporaryToken({
                    minutes: 24 * 60
                });

                const hashedToken = await scryptHash(token);

                user.passwordSetupToken = hashedToken;
                user.passwordSetupTokenExpiry = expiry;

                url = UserServiceV3.generateEmailChangePasswordSetupURL(token, String(user._id));
            }
        }

        // Save user details
        user.email = email;
        UserServiceV3.destroyAllUserActiveSessions(user);

        return { url };
    }

    getDeviceExpirationDateTime() {
        return new Date(Date.now() + 24 * 60 * 60 * 1000);
    }
}
