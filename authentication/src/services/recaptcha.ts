import axios from "axios";
import { BodyInvalidBadRequestError, InvalidCaptchaTokenError } from "@moxfive-llc/common";

/**
 * Service for validating Google reCAPTCHA tokens
 */
export class ReCaptchaService {
    private static readonly RECAPTCHA_VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";

    /**
     * Validates a reCAPTCHA v3 token
     * @param token - The reCAPTCHA v3 token to validate
     * @param ipAddress - The IP address of the client (optional)
     * @returns Validation result with score
     */
    static async validateV3Token(token: string, ipAddress?: string): Promise<{
        success: boolean;
        score?: number;
        action?: string;
        challengeTs?: string;
        hostname?: string;
        errorCodes?: string[];
    }> {
        if (!token) {
            throw new BodyInvalidBadRequestError([{
                name: "token",
                value: token,
                message: "reCAPTCHA token is required",
            }]);
        }

        try {
            const params = new URLSearchParams();
            params.append("secret", process.env.RECAPTCHA_V3_SECRET_KEY!);
            params.append("response", token);

            if (ipAddress) {
                params.append("remoteip", ipAddress);
            }

            const response = await axios.post(this.RECAPTCHA_VERIFY_URL, params);
            return response.data;
        }
        catch (error) {
            console.error("ReCaptchaService.validateV3Token", error);
            throw new InvalidCaptchaTokenError();
        }
    }

    /**
     * Validates a reCAPTCHA v2 token
     * @param token - The reCAPTCHA v2 token to validate
     * @param ipAddress - The IP address of the client (optional)
     * @returns Validation result
     */
    static async validateV2Token(token: string, ipAddress?: string): Promise<{
        success: boolean;
        challengeTs?: string;
        hostname?: string;
        errorCodes?: string[];
    }> {
        if (!token) {
            throw new BodyInvalidBadRequestError([{
                name: "token",
                value: token,
                message: "reCAPTCHA token is required",
            }]);
        }

        try {
            const params = new URLSearchParams();
            params.append("secret", process.env.RECAPTCHA_V2_SECRET_KEY!);
            params.append("response", token);

            if (ipAddress) {
                params.append("remoteip", ipAddress);
            }

            const response = await axios.post(this.RECAPTCHA_VERIFY_URL, params);
            return response.data;
        }
        catch (error) {
            console.error("ReCaptchaService.validateV2Token", error);
            throw new InvalidCaptchaTokenError();
        }
    }
}
