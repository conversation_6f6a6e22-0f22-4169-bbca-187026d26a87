import mongoose, { ClientSession } from "mongoose";

export class MongoTransaction {
    session: ClientSession | null = null;

    async startTransaction() {
        this.session = await mongoose.startSession();
        this.session.startTransaction({
            readPreference: "primary"
        });
    }

    async commitTransaction() {
        if(this.session) {
            await this.session.commitTransaction();
            await this.session.endSession();
            this.session = null;
        }
    }

    async abortTransaction() {
        if(this.session) {
            await this.session.abortTransaction();
            await this.session.endSession();
            this.session = null;
        }
    }

}
