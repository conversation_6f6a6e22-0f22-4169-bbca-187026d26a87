/* eslint-disable max-len */
/* eslint-disable no-await-in-loop */
import mongoose, { Model } from "mongoose";
import { convertToObjectId, isValidDate } from "@moxfive-llc/common";

interface WaitTillDataSyncOptions {
    collection: Model<any>; // Assuming Model is from Mongoose or similar
    searchIndex: string;
    docId?: string; // ID of a single document to verify
    updatedDoc?: Partial<any>; // Use Partial for flexibility with document updates
    removedIds?: string[]; // IDs of removed documents
    docIds?: string[]; // IDs of documents to check (if applicable)
    explicitTimeOut?: number; // Timeout in milliseconds
    pollInterval?: number; // Polling interval in milliseconds
}

interface VerifyDataOptions {
    collection: Model<any>; // Replace `any` with your document type if known
    searchIndex: string;
    docId?: string; // Single document ID
    updatedDoc?: Record<string, any>; // Partial document update
    removedIds?: string[]; // IDs of removed documents
    docIds?: string[]; // IDs of documents to verify
}

class SyncStatus {
    constructor(
        public isSynced: boolean,
        public timedOut: boolean,
        public error?: Error
    ) { }
}

export class AtlasService {
    /**
     * This function will be used to check whether the write operations are successfully performed on search indexes or not
     * Follow below steps to use this function (Function might misbehave if required fields are missing)
     *
     * For create API:
     *      Required fields are: docId
     *
     * For update API:
     *      Required fields are: docId, updatedDoc(In this object please do not pass string fields like name, description)
     *
     * For delete API:
     *      Required fields are: removedIds
    */
    static async waitTillDataSyncToSearchIndex({ collection, searchIndex, docId, updatedDoc, removedIds, docIds, explicitTimeOut = 3000, pollInterval = 200 }: WaitTillDataSyncOptions): Promise<SyncStatus> {
        // const getStatusOfData = async () => {
        //     let isUpdated = false;
        //     // eslint-disable-next-line @typescript-eslint/no-unused-vars
        //     for (let i = 0; i < 1000; i++) {
        //         if (!isUpdated) {
        //             isUpdated = await AtlasService.verifyData({ collection, searchIndex, docId, updatedDoc, removedIds, docIds });
        //         }
        //         else {
        //             break;
        //         }
        //     }

        //     return isUpdated;
        // };

        // const thresholdTimeout = () => {
        //     return new Promise((resolve, reject) => {
        //         setTimeout(resolve, explicitTimeOut || 3000);
        //     });
        // };

        // const shouldReturn = await Promise.race([getStatusOfData(), thresholdTimeout()]);
        // return shouldReturn;

        const timeout = explicitTimeOut;
        const startTime = Date.now();

        // Polling function to check data sync
        const pollForSync = async (): Promise<SyncStatus> => {
            while (Date.now() - startTime <= timeout) {
                try {
                    const isSynced = await AtlasService.verifyData({
                        collection,
                        searchIndex,
                        docId,
                        updatedDoc: Array.isArray(updatedDoc) ? Object.assign({}, ...updatedDoc) : updatedDoc,
                        removedIds,
                        docIds,
                    });

                    if (isSynced) {
                        return new SyncStatus(true, false); // Data is synced
                    }

                    // Wait before the next poll
                    await new Promise((resolve) => setTimeout(resolve, pollInterval));
                }
                catch (error) {
                    return new SyncStatus(false, false, error as Error); // Error during verification
                }
            }

            return new SyncStatus(false, true); // Timed out
        };

        // Timeout promise (for Promise.race)
        const thresholdTimeout = (): Promise<SyncStatus> => {
            return new Promise((resolve) => {
                setTimeout(() => resolve(new SyncStatus(false, true)), timeout);
            });
        };

        // Race polling against the timeout
        return await Promise.race([pollForSync(), thresholdTimeout()]);
    }

    static async verifyData({ collection, searchIndex, docId, updatedDoc, removedIds, docIds }: VerifyDataOptions): Promise<boolean> {
        const must = [];

        // Handle single docId
        if (docId) {
            must.push({
                in: {
                    path: "_id",
                    value: convertToObjectId(docId)
                }
            });
        }
        // Handle multiple docIds (only if no single docId)
        else if (docIds?.length) {
            must.push({
                in: {
                    path: "_id",
                    value: docIds.map(convertToObjectId)
                }
            });
        }

        // if (docIds) {
        //     must.push({
        //         in: {
        //             path: "_id",
        //             value: docIds.map(docId => convertToObjectId(docId))
        //         }
        //     });
        // }

        // if (Object.keys(updatedDoc ?? {}).length) {
        //     const mustQuery = await AtlasService.prepareMustQueryFromDoc(updatedDoc ?? {});
        //     mustQuery.length && must.push(...mustQuery);
        // }

        // Handle updatedDoc fields
        if (updatedDoc && Object.keys(updatedDoc).length) {
            const mustQuery = await AtlasService.prepareMustQueryFromDoc(updatedDoc);
            if (mustQuery.length) must.push(...mustQuery);
        }

        // Handle removedIds (we expect these to NOT exist)
        if (removedIds?.length) {
            must.push({
                in: {
                    path: "_id",
                    value: removedIds.map(convertToObjectId)
                }
            });
        }

        // Build the search query
        const aggregateQuery: any[] = [{
            $search: {
                index: searchIndex,
                compound: {
                    must: must
                }
            }
        }];

        try {
            const data = await collection.aggregate(aggregateQuery);

            //console.info(JSON.stringify(data));

            // If removeIds are specified then we should not get any documents
            // if (removedIds?.length && !data.length) {
            //     return true;
            // }

            // // If docIds are specified then we should get exact number of documents back
            // if ((!removedIds?.length) && docIds?.length && docIds?.length === data.length) {
            //     return true;
            // }

            // // If docId is specified then we should get the data
            // if ((!removedIds?.length) && (!docIds?.length) && data.length) {
            //     return true;
            // }

            // return false;

            // Case 1: Removed IDs - expect no results
            if (removedIds?.length) {
                return data.length === 0;
            }

            // Case 2: Multiple docIds - expect exact match in count
            if (docIds?.length) {
                return data.length === docIds.length;
            }

            // Case 3: Single docId - expect exactly one result
            if (docId) {
                return data.length === 1;
            }

            // Case 4: Updated doc - expect at least one result (basic check)
            if (updatedDoc && Object.keys(updatedDoc).length) {
                return data.length > 0;
            }

            // No conditions specified, assume failure
            return false;
        }
        catch (error) {
            console.error("Error verifying data in Atlas Search:", error);
            console.error("Failed query:", JSON.stringify(aggregateQuery));
            return false; // Fail gracefully
        }
    }

    static async prepareMustQueryFromDoc(updatedDoc: Record<string, any>): Promise<any[]> {
        const must: any[] = [];

        // Object.keys(updatedDoc).map(property => {
        //     if (updatedDoc[String(property)] instanceof Object) {
        //         // check if it is object Id
        //         // eslint-disable-next-line max-len
        //         if (mongoose.Types.ObjectId.isValid(updatedDoc[String(property)])) {
        //             must.push({
        //                 in: {
        //                     path: `${property}`,
        //                     value: convertToObjectId(updatedDoc[String(property)])
        //                 }
        //             });
        //         }
        //         else {

        //             updatedDoc[String(property)]["id"] && must.push({
        //                 in: {
        //                     path: `${property}.id`,
        //                     value: convertToObjectId(updatedDoc[String(property)]["id"])
        //                 }
        //             });
        //         }
        //     }
        //     else if (Array.isArray(updatedDoc[String(property)]) && updatedDoc[String(property)].length) {
        //         if (updatedDoc[String(property)][0]["id"]) {
        //             must.push({
        //                 in: {
        //                     path: `${property}.id`,
        //                     value: updatedDoc[String(property)].map((item: any) => convertToObjectId(item.id))
        //                 }
        //             });
        //         }
        //     }
        //     else if (isValidDate(String(updatedDoc[String(property)]))) {
        //         must.push({
        //             in: {
        //                 path: property,
        //                 value: new Date(updatedDoc[String(property)])
        //             }
        //         });
        //     }
        //     else {
        //         if (updatedDoc[String(property)] === null) {
        //             must.push({
        //                 equals: {
        //                     path: property,
        //                     value: null
        //                 }
        //             });
        //         }
        //         else {
        //             must.push({
        //                 in: {
        //                     path: property,
        //                     value: updatedDoc[String(property)]
        //                 }
        //             });
        //         }
        //     }
        // });

        for (const [key, value] of Object.entries(updatedDoc)) {
            try {
                if (value === null) {
                    must.push({ equals: { path: key, value: null } });
                }
                else if (mongoose.Types.ObjectId.isValid(value)) {
                    must.push({ in: { path: key, value: convertToObjectId(value) } });
                }
                else if (Array.isArray(value)) {
                    if (value.length && value.every((item) => item?.id)) {
                        must.push({
                            in: {
                                path: `${key}.id`,
                                value: value.map((item) => convertToObjectId(item.id)),
                            },
                        });
                    }
                }
                else if (typeof value === "object" && value?.id) {
                    must.push({
                        in: { path: `${key}.id`, value: convertToObjectId(value.id) },
                    });
                }
                else if (isValidDate(String(value))) {
                    must.push({ in: { path: key, value: new Date(value) } });
                }
                else {
                    must.push({ in: { path: key, value } });
                }
            }
            catch (error) {
                console.warn(`Failed to process field ${key} in updatedDoc for value ${value}:`, error);
                continue; // Skip invalid fields
            }
        }

        return must;
    }
}
