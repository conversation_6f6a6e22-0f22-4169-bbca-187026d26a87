/* eslint-disable security/detect-non-literal-fs-filename */
import { Response } from "express";
import { unlink, writeFile } from "fs/promises";
import { ValueTypesEnum } from "../enums/value-types.enum";

export class exportService {

    static fetchCSVStringOfMultipleSectionsBasedOnPermission = ({ sections, data, assignedActions, isMoxfiveUser }:
        { sections: { path: any, prefix?: string, action?: string }[], data: any, assignedActions?: Set<string>, isMoxfiveUser?: boolean | null }) => {
        const fieldNames: string[] = [];
        const fieldsToFetch: string[] = [];
        const records: any = [];
        const fieldsSet: Set<string> = new Set();
        const dateFields: Set<string> = new Set();

        // Loop through all sections and prepare fieldNames and fieldsToFetch
        sections.forEach(section => {
            if (!assignedActions || !section.action || assignedActions.has(section.action)) {
                section.path.forEach((field: any) => {
                    // CASE: Some fields should be ignored in export and some fields should be for only moxfive users
                    if (!fieldsSet.has(field.name) && !field.ignoreInExport && (!field.moxfiveExclusive || (field.moxfiveExclusive && isMoxfiveUser))) {
                        fieldNames.push(field.displayName);
                        fieldsToFetch.push(field.name);
                        fieldsSet.add(field.name);
                        if (field.type === ValueTypesEnum.DATE) {
                            dateFields.add(field.name);
                        }
                    }
                });
            }
        });

        // Loop through data and prepare records
        data.forEach((record: any) => {
            const recordDetails: any = [];
            fieldsToFetch.forEach((field) => {
                if (dateFields.has(field) && record[String(field)]) {
                    const date = new Date(record[String(field)]);

                    // Convert the Date object to ISO 8601 string in UTC
                    const utcDateString = date.toISOString();
                    recordDetails.push(`"${utcDateString}"`);
                }
                else if(Array.isArray(record[String(field)])) {
                    recordDetails.push(`"${(record[String(field)] ?? []).join(";").toString().replaceAll(`"`, `""`)}"`);
                }
                else {
                    recordDetails.push(`"${(record[String(field)] ?? "").toString().replaceAll(`"`, `""`)}"`);
                }
            });
            records.push(recordDetails);
        });

        // Return CSV String
        return [
            fieldNames,
            ...records
        ].map(e => e.join(","))
            .join("\n");
    };

    static exportFile = async ({ sections, data = [], assignedActions, organizationName, module, res, isMoxfiveUser = null }:
        {
            sections: { path: any, prefix?: string, action?: string }[], data: any, assignedActions?: Set<string>, organizationName: string, module: string,
            res: Response, isMoxfiveUser?: boolean | null
        }) => {

        // Fetch CSV String
        const csvString = exportService.fetchCSVStringOfMultipleSectionsBasedOnPermission({
            sections,
            data,
            assignedActions,
            isMoxfiveUser
        });

        // Define filename and filepath
        const fileName = `${organizationName.replace(/[^\w]/gi, "") }-${module}-${Date.now()}.csv`;
        // For Local
        // const filePath = `${fileName}`;

        const filePath = `/exports/${fileName}`;

        // Set content-type
        res.contentType("text/csv");

        // Prepare and download file
        await writeFile(filePath, csvString);
        res.download(filePath, fileName, (err) => {
            if (err) {
                console.error(err);
            }
            unlink(filePath).then();
        });
    };

}
