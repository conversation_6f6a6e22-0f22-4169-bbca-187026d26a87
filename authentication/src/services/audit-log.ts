import { Request } from "express";
import { FlexibleFieldNameKey } from "../interfaces";
import { TargetType } from "@moxfive-llc/common";

export class AuditLog {
    static fetchFlexibleFieldValuesMap = ({ flexibleFieldsNameKey, req, data, oldData = null }:
        {flexibleFieldsNameKey: FlexibleFieldNameKey[], req: Request, data: any, oldData: any}) => {
        const flexibleFieldValuesMap = new Map();
        const oldFlexibleFieldValuesMap = new Map();

        // If req has flexible field values map
        if(req.flexibleFieldValuesMap) {
            // Loop through all flexible fields name key
            flexibleFieldsNameKey.forEach(field => {
                if(data.hasOwnProperty(field.name)) {
                    // Fetch provided values
                    const providedValues = data[field.name];

                    // Format provided values in id, value object
                    const values = providedValues && providedValues.length ? providedValues.map((val: string) =>{
                        return {
                            id: val,
                            value: req.flexibleFieldValuesMap?.get(String(val)) || ""
                        };
                    })  : [];

                    // Add that in flexible fields values map
                    flexibleFieldValuesMap.set(field.name, values);

                    if(oldData) {
                        // Fetch provided values
                        const providedValues = oldData[field.name];

                        // Format provided values in id, value object
                        const values = providedValues && providedValues.length ? providedValues.map((val: string) =>{
                            return {
                                id: val,
                                value: req.flexibleFieldValuesMap?.get(String(val)) || ""
                            };
                        })  : [];

                        // Add that in flexible fields values map
                        oldFlexibleFieldValuesMap.set(field.name, values);
                    }
                }
            });
        }

        return {
            flexibleFieldValuesMap,
            oldFlexibleFieldValuesMap
        };
    };

    static prepareModifiedProperties = ({ data, req, flexibleFieldsNameKey, target, oldData = null }:
        {data: any, req: Request, flexibleFieldsNameKey: FlexibleFieldNameKey[], target: TargetType, oldData?: any}) => {
        const { flexibleFieldValuesMap, oldFlexibleFieldValuesMap } = AuditLog.fetchFlexibleFieldValuesMap({
            flexibleFieldsNameKey,
            data,
            oldData,
            req
        });

        const modifiedProperties: any = [];
        // eslint-disable-next-line guard-for-in
        for (const field in data) {
            const flexibleFieldValues = flexibleFieldValuesMap.get(field);
            const oldFlexibleFieldValues = oldData ? oldFlexibleFieldValuesMap.get(field) : null;
            // eslint-disable-next-line no-nested-ternary
            const oldDataValue = oldData ? oldFlexibleFieldValues ? oldFlexibleFieldValues : (oldData[String(field)] ?? "") : "";
            const newDataValue = flexibleFieldValues ? flexibleFieldValues : (data[String(field)] ?? "");

            modifiedProperties.push({
                target,
                propertyName: field,
                oldValue: typeof oldDataValue === "string" ? oldDataValue : JSON.stringify(oldDataValue),
                newValue: typeof newDataValue === "string" ? newDataValue : JSON.stringify(newDataValue)
            });
        }

        return modifiedProperties;
    };
}
