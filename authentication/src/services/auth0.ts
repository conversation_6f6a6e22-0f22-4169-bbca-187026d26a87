import {
    BodyInvalidBadRequestError, ExternalServerError, getUserName, InternalServerError, InvalidMFATokenError, InvalidTokenError, MFATokenErrorCode,
    NotFoundCode,
    ReferenceValueV2, ResourceAlreadyExistBadRequestError,
    ResourceNotFoundError
} from "@moxfive-llc/common";
import axios, { AxiosError, AxiosRequestConfig } from "axios";
import qs from "qs";
import { getAuth0Error } from "../util/get-auth0-error";
import { MFATypesEnum } from "../enums/mfa-types";
import { IRefreshTokenParamsV2 } from "../interfaces";
import { MemberDetail } from "../interfaces/add-user-detail-v3";
import { OrganizationV2Doc } from "../models/v2/oragnizations-v2";
import { generateRandomString, isIPAddress } from "../util";
import { UserV2Doc } from "../models/v2/users-v2";
import { Request } from "express";
import app from "../app";

interface IListUserMFAAuthenticatorsUsingMFAToken {
    id: string,
    type: string,
    name: string
}

interface IUpdateUserDetails {
    email?: string,
    password?: string,
    userMetadata?: Record<string, any>,
    name?: string
}

export class Auth0Service {
    static BACKEND_REDIRECT_URI = `${process.env.DOMAIN_URL}/v3/redirect`;
    static REDIRECT_URI = process.env.REDIRECT_URI || Auth0Service.BACKEND_REDIRECT_URI;
    private req?: Request;
    private axiosRequestConfig: AxiosRequestConfig;
    private throwError: boolean;

    constructor(req: Request, throwError = true) {
        this.req = req;
        this.axiosRequestConfig = {
            headers: {}
        };
        this.throwError = throwError;

        if (this.req) {
            // Forward user agent if available
            if (this.req.headers["user-agent"]) {
                this.axiosRequestConfig.headers!["User-Agent"] = this.req.headers["user-agent"] as string;
            }

            // Forward IP address if available (prioritize X-Forwarded-For header)
            const ipAddress = this.req.ip;
            if (ipAddress && isIPAddress(ipAddress)) {
                this.axiosRequestConfig.headers!["X-Forwarded-For"] = Array.isArray(ipAddress) ? ipAddress[0] : ipAddress;
                this.axiosRequestConfig.headers!["auth0-forwarded-for"] = Array.isArray(ipAddress) ? ipAddress[0] : ipAddress;
            }
        }

    }

    async validateUserCredentials({ email, password }: {
        email: string, password: string
    }) {

        console.info("***********");
        console.info("Email", email);
        console.info("X-FORWARDED-FOR", this.req?.headers["x-forwarded-for"]);
        console.info("Req", this.req?.ip);
        console.info("Headers", this.axiosRequestConfig.headers);
        console.info("***********");
        try {
            await axios.post(
                `${process.env.AUTH0_DOMAIN}/oauth/token`,
                qs.stringify({
                    grant_type: "password",
                    username: email,
                    password,
                    client_id: process.env.AUTH0_WEB_APP_CLIENT_ID,
                    client_secret: process.env.AUTH0_WEB_APP_CLIENT_SECRET,
                    scope: "openid offline_access profile email"
                }),
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                }
            );

            return {};
        }
        catch (error) {
            // If the error is from axios and the status is 403
            if (error instanceof AxiosError && error.response?.status === 403) {
                const errorResponse = error.response.data;

                // That Means either username or password invalid
                if (errorResponse.error === "invalid_grant") {
                    return {
                        invalidCredentials: true
                    };
                }
                // That means username and password are valid but in order to login MFA is required.
                else if (errorResponse.error === "mfa_required") {
                    return {
                        mfaToken: errorResponse.mfa_token
                    };
                }
            }
            // Throw External Server Error
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async sendEmailVerificationCode({ email }: {
        email: string
    }) {
        try {
            await axios.post(
                `${process.env.AUTH0_DOMAIN}/passwordless/start`,
                qs.stringify({
                    client_id: process.env.AUTH0_WEB_APP_CLIENT_ID,
                    client_secret: process.env.AUTH0_WEB_APP_CLIENT_SECRET,
                    connection: "email",
                    email,
                    send: "code"
                }),
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                }
            );

            return {};
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async verifyUserEmailCodeOTP({ email, code }: {
        email: string, code: string
    }) {
        try {
            const response = await axios.post(
                `${process.env.AUTH0_DOMAIN}/oauth/token`,
                qs.stringify({
                    "grant_type": "http://auth0.com/oauth/grant-type/passwordless/otp",
                    client_id: process.env.AUTH0_WEB_APP_CLIENT_ID,
                    client_secret: process.env.AUTH0_WEB_APP_CLIENT_SECRET,
                    username: email,
                    "realm": "email",
                    otp: code,
                    scope: "openid profile email"
                }),
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                }
            );

            return {
                idToken: response.data.id_token,
            };
        }
        catch (error) {
            if (error instanceof AxiosError) {
                const errorResponse = error.response?.data;
                // That means provided Code is invalid
                if (errorResponse.error === "invalid_grant") {
                    throw new BodyInvalidBadRequestError([
                        {
                            name: "code",
                            value: code,
                            message: "Invalid six-digit code. Please try again."
                        }
                    ]);
                }
            }
            // Throw External Server Error
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async addUser({ userEmail, userDetails, organization, accessToken }: {
        userEmail: string, userDetails: MemberDetail | UserV2Doc, organization: Partial<OrganizationV2Doc>, accessToken: string
    }): Promise<string | null> {
        try {
            const user: any = await axios.post(
                `${process.env.AUTH0_DOMAIN}/api/v2/users`,
                {
                    "email": userEmail,
                    "user_metadata": {
                        displayName: userDetails.displayName,
                        firstName: userDetails.firstName,
                        lastName: userDetails.lastName,
                        officePhone: userDetails.officePhone,
                        userLocation: userDetails.userLocation ?? null,
                        isEnabled: userDetails.isEnabled,
                        jobTitle: userDetails.jobTitle,
                        organization: {
                            id: String(organization._id),
                            value: organization.name
                        },
                        organizationTypes: (organization.organizationTypes as ReferenceValueV2[]).map(type => type.value),
                    },
                    "email_verified": true,
                    "name": getUserName({
                        firstName: userDetails.firstName ?? null,
                        lastName: userDetails.lastName ?? null,
                        displayName: userDetails.displayName ?? ""
                    }),
                    "password": `${generateRandomString(16)}@${Date.now()}`, // Todo: If want to updated
                    "connection": "Username-Password-Authentication"
                },
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/json",
                        "Accept": "application/json",
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );

            return user.data ? user.data.user_id : null;
        }
        catch (error) {
            console.error(getAuth0Error(error));
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async addConnection({ connectionName, strategy, options, metaData, accessToken }: {
        connectionName: string, strategy: string, options: Record<string, any>, metaData: Record<string, any>, accessToken: string
    }): Promise<{ connectionIdFromAuth0: string; domainUrl: string | null; } | null | undefined> {
        try {
            const connection: any = await axios.post(
                `${process.env.AUTH0_DOMAIN}/api/v2/connections`,
                {
                    strategy,
                    name: connectionName,
                    options,
                    metadata: metaData
                },
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/json",
                        "Accept": "application/json",
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );

            // Enable application for the enterprise connection
            if (connection.data) {
                const payload = JSON.stringify([
                    {
                        "client_id": process.env.AUTH0_WEB_APP_CLIENT_ID,
                        "status": true
                    },
                    {
                        "client_id": process.env.AUTH0_SPA_APP_CLIENT_ID,
                        "status": true
                    }
                ]);

                await axios.patch(
                    `${process.env.AUTH0_DOMAIN}/api/v2/connections/${connection.data.id}/clients`,
                    payload,
                    {
                        headers: {
                            ...this.axiosRequestConfig.headers,
                            "Content-Type": "application/json",
                            "Accept": "application/json",
                            "Authorization": `Bearer ${accessToken}`
                        },
                    }
                );
            }
            if (connection.data) {
                const { id, options } = connection.data;
                const domainUrl: string | null = options.issuer ?? options.domain ?? options.tenant_domain ?? null;

                return { connectionIdFromAuth0: id, domainUrl };
            }

            return null;
        }
        catch (error) {
            if (error instanceof AxiosError) {
                const errorResponse = error.response?.data;
                console.info("errorResponse", errorResponse);

                if (errorResponse.statusCode === 400) {
                    if (errorResponse.errorCode === "invalid_body") {
                        throw new BodyInvalidBadRequestError([
                            { name: "", value: "", message: errorResponse.message }
                        ]);
                    }
                }
            }
            else {
                console.error(error);
                throw new ExternalServerError([getAuth0Error(error)]);
            }
        }
    }

    async getOrganizationConnectionDetails({ accessToken, connectionId }: { accessToken: string, connectionId: string }) {
        try {
            const response = await axios.get(
                `${process.env.AUTH0_DOMAIN}/api/v2/connections/${connectionId}`,
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );

            return response.data;
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async removeOrganizationConnection({ accessToken, connectionId }: { accessToken: string, connectionId: string }) {
        try {
            await axios.delete(
                `${process.env.AUTH0_DOMAIN}/api/v2/connections/${connectionId}`,
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async listUserMFAAuthenticatorsUsingMFAToken(mfaToken: string) {
        try {
            const response = await axios.get(
                `${process.env.AUTH0_DOMAIN}/mfa/authenticators`,
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${mfaToken}`,
                    },
                }
            );

            // Loop through response, transofrm it and return it
            return (response.data as {
                id: string,
                authenticator_type: keyof typeof MFATypesEnum,
                active: boolean
            }[]).reduce((data: Array<IListUserMFAAuthenticatorsUsingMFAToken>, record) => {
                // If it's active then push record in data
                if (record.active) {
                    data.push({
                        id: record.id,
                        name: MFATypesEnum[record.authenticator_type] ?? "",
                        type: record.authenticator_type
                    });
                }

                return data;
            }, []);
        }
        catch (error) {
            if (error instanceof AxiosError) {
                const errorResponse = error.response?.data;

                // That Means MFA Token Invalid
                if (errorResponse.error === "invalid_grant") {
                    throw new InvalidMFATokenError("Invalid MFA token.", MFATokenErrorCode.INVALID_MFA_TOKEN);
                }
            }
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async createUserMFAEnrollment({ mfaToken, authenticatorTypes }: {
        mfaToken: string, authenticatorTypes: (keyof typeof MFATypesEnum)[]
    }) {
        try {
            const response = await axios.post(
                `${process.env.AUTH0_DOMAIN}/mfa/associate`,
                {
                    authenticator_types: authenticatorTypes
                },
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${mfaToken}`,
                    },
                }
            );

            return {
                barcodeURI: response.data.barcode_uri,
                secret: response.data.secret
            };
        }
        catch (error) {
            if (error instanceof AxiosError) {
                const errorResponse = error.response?.data;

                // That means provided MFA Token is invalid
                if (errorResponse.error === "invalid_grant") {
                    throw new InvalidMFATokenError("Invalid MFA token.", MFATokenErrorCode.INVALID_MFA_TOKEN);
                }
                // That means User has already enrolled for MFA
                else if (errorResponse.error === "access_denied") {
                    throw new ResourceAlreadyExistBadRequestError("mfa", authenticatorTypes[0], "User is already enrolled in the specified MFA method.");
                }
            }
            else {
                // Throw External Server Error
                throw new ExternalServerError([getAuth0Error(error)]);
            }
        }
    }

    async verifyMFA({ mfaToken, otp, recoveryCode, login }: {
        mfaToken: string, otp?: string, recoveryCode?: string, login: boolean
    }) {
        try {
            const data: Record<string, string | undefined> = {
                client_id: process.env.AUTH0_WEB_APP_CLIENT_ID,
                client_secret: process.env.AUTH0_WEB_APP_CLIENT_SECRET,
                mfa_token: mfaToken,
                scope: `openid ${login ? "offline_access" : ""} profile email`
            };

            // If otp is provided then set it's details, else recovery code is provided then set it's details
            if (otp) {
                data.grant_type = "http://auth0.com/oauth/grant-type/mfa-otp";
                data.otp = otp;
            }
            else if (recoveryCode) {
                data.grant_type = "http://auth0.com/oauth/grant-type/mfa-recovery-code";
                data.recovery_code = recoveryCode;
            }
            else {
                throw new InternalServerError();
            }

            const response = await axios.post(
                `${process.env.AUTH0_DOMAIN}/oauth/token`,
                qs.stringify(data),
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                }
            );

            // Prepare response data
            const responseData: {
                idToken: string,
                refreshToken: string,
                recoveryCode?: string
            } = {
                idToken: response.data.id_token,
                refreshToken: response.data.refresh_token
            };

            // If recoveryCode is there then we will get new recovery code on successful verification. In that case send it to the user
            if (recoveryCode) {
                responseData.recoveryCode = response.data.recovery_code;
            }

            return responseData;
        }
        catch (error) {
            if (error instanceof AxiosError) {
                const errorResponse = error.response?.data;
                console.info("errorResponse", errorResponse);

                if (errorResponse.error === "invalid_grant") {
                    if (errorResponse.error_description === "Invalid otp_code.") {
                        throw new BodyInvalidBadRequestError([
                            { name: "otp", value: otp ?? "", message: "Invalid six-digit code. Please try again." }
                        ]);
                    }
                    else if (errorResponse.error_description === "MFA Authorization rejected.") {
                        throw new BodyInvalidBadRequestError([
                            { name: "recoveryCode", value: recoveryCode ?? "", message: "Invalid recovery code. Please try again." }
                        ]);
                    }
                    else if (errorResponse.error_description === "Malformed mfa_token") {
                        throw new InvalidMFATokenError("Invalid MFA token.", MFATokenErrorCode.INVALID_MFA_TOKEN);
                    }
                }
                else if (errorResponse.error === "expired_token") {
                    throw new InvalidMFATokenError("MFA token has expired.", MFATokenErrorCode.MFA_TOKEN_EXPIRED);
                }
                else if (errorResponse.error === "unsupported_challenge_type") {
                    throw new ResourceNotFoundError(NotFoundCode.USER_NOT_ENROLLED_IN_MFA, "User is not enrolled in the specified MFA method.");
                }
            }
            else {
                throw new ExternalServerError([getAuth0Error(error)]);
            }
        }
    }

    async refreshToken(data: IRefreshTokenParamsV2) {
        try {
            const requestData: Record<string, string> = {
                grant_type: "refresh_token",
                refresh_token: data.refreshToken,
                client_id: process.env.AUTH0_WEB_APP_CLIENT_ID!,
                scope: "openid offline_access profile email"
            };

            // If PKCE flow is enabled then set client id of SPA app otherwise set client secret
            if (data.pkceEnabled) {
                requestData.client_id = process.env.AUTH0_SPA_APP_CLIENT_ID!;
            }
            else {
                requestData.client_secret = process.env.AUTH0_WEB_APP_CLIENT_SECRET!;

            }
            const response = await axios.post(
                `${process.env.AUTH0_DOMAIN}/oauth/token`,
                qs.stringify(requestData),
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                }
            );

            return {
                idToken: response.data.id_token,
                refreshToken: response.data.refresh_token
            };
        }
        catch (error) {
            if (error instanceof AxiosError) {
                const errorResponse = error.response?.data;
                console.error("oAuth2 Token Error", errorResponse);
            }
            throw new InvalidTokenError();
        }
    }

    async fetchApplicationAccessToken() {
        try {
            // Check whether the token already exist and it"s valid, otherwise call the API and get the token
            if (app.locals.auth0APIToken.accessToken
                && (app.locals.auth0APIToken.expiresIn > new Date().valueOf())) {
                return {
                    accessToken: app.locals.auth0APIToken.accessToken
                };
            }
            else {
                const requestData: Record<string, string> = {
                    grant_type: "client_credentials",
                    client_id: process.env.AUTH0_WEB_APP_CLIENT_ID!,
                    client_secret: process.env.AUTH0_WEB_APP_CLIENT_SECRET!,
                    audience: `${process.env.AUTH0_DOMAIN}/api/v2/`
                };
                const response = await axios.post(
                    `${process.env.AUTH0_DOMAIN}/oauth/token`,
                    requestData,
                    {
                        headers: {
                            ...this.axiosRequestConfig.headers,
                            "Content-Type": "application/json",
                        },
                    }
                );

                app.locals.auth0APIToken = {
                    accessToken: response.data.access_token,
                    expiresIn: new Date().setSeconds(new Date().getSeconds() + response.data.expires_in - (60 * 5)) //Now token will be valid lesser than 5 minutes of actual token.
                };
                return {
                    accessToken: response.data.access_token
                };
            }
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    checkAuth0Id(auth0Id: string) {
        const auth0IDRegex = /^.{3,}$/;
        return auth0IDRegex.test(auth0Id);
    }

    async updateUserDetails({ accessToken, userId, data }: {
        accessToken: string, userId: string, data: IUpdateUserDetails
    }) {
        try {
            const requestData: Record<string, any> = {};

            if (data.email) {
                requestData.email = data.email;
                requestData["email_verified"] = true;
            }
            if (data.password) {
                requestData.password = data.password;
            }
            if (data.userMetadata) {
                requestData.user_metadata = data.userMetadata;
            }
            if (data.name) {
                requestData.name = data.name;
            }

            if (this.checkAuth0Id(String(userId))) {
                await axios.patch(
                    `${process.env.AUTH0_DOMAIN}/api/v2/users/${String(userId)}`,
                    requestData,
                    {
                        headers: {
                            ...this.axiosRequestConfig.headers,
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${accessToken}`
                        },
                    }
                );
            }
            else {
                throw new InternalServerError();
            }
        }
        catch (error) {
            if (this.throwError) {
                throw new ExternalServerError([getAuth0Error(error)]);
            }
        }
    }

    async listUserMFAEnrollments({ accessToken, userId }: {
        accessToken: string, userId: string
    }) {
        try {

            const response = await axios.get(
                `${process.env.AUTH0_DOMAIN}/api/v2/users/${userId}/authentication-methods`,
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );

            // Loop through response data
            return response.data.reduce((data: Array<any>, record: Record<string, any>) => {
                // If response is confirmed
                if (record.confirmed) {
                    // If type is totp then we will consider it as otp otherwise type that we get
                    const type = record.type === "totp" ? "otp" : record.type;

                    // Insert record in data
                    data.push({
                        id: record.id,
                        type,
                        name: MFATypesEnum[type as keyof typeof MFATypesEnum] ?? "",
                        createdAt: record.created_at,
                        lastAuthAt: record.last_auth_at ?? null
                    });
                }

                return data;
            }, []);
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async removeUserMFAEnrollment({ accessToken, userId, authenticationMethodId }: {
        accessToken: string, userId: string, authenticationMethodId?: string
    }) {
        try {

            await axios.delete(
                `${process.env.AUTH0_DOMAIN}/api/v2/users/${userId}/authentication-methods${authenticationMethodId ? `/${authenticationMethodId}` : ""}`,
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async regenerateRecoveryCode({ accessToken, userId }: {
        accessToken: string, userId: string
    }): Promise<string> {
        try {
            const response = await axios.post(
                `${process.env.AUTH0_DOMAIN}/api/v2/users/${userId}/recovery-code-regeneration`,
                {},
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );

            console.info("Re generate recovery code", response);

            return response.data["recovery_code"];
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async removeUser({ userId, accessToken }: {
        userId: string, accessToken: string
    }) {
        try {
            await axios.delete(
                `${process.env.AUTH0_DOMAIN}/api/v2/users/${userId}`,
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Authorization": `Bearer ${accessToken}`
                    },
                }
            );
        }
        catch (error) {
            console.error(error);
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }

    async exchangeAuthorizationCode({ code, codeVerifier }: {
        code: string, codeVerifier?: string | null
    }) {
        try {
            const requestData: Record<string, string> = {
                grant_type: "authorization_code",
                code: code,
                scope: "openid offline_access profile email"
            };

            // If PKCE flow is enabled (codeVerifier is provided), use SPA client ID and code_verifier
            if (codeVerifier) {
                requestData.client_id = process.env.AUTH0_SPA_APP_CLIENT_ID!;
                requestData.code_verifier = codeVerifier;
                requestData.redirect_uri = Auth0Service.REDIRECT_URI;
            }
            // For normal flow, use Web App client ID and client secret
            else {
                requestData.client_id = process.env.AUTH0_WEB_APP_CLIENT_ID!;
                requestData.client_secret = process.env.AUTH0_WEB_APP_CLIENT_SECRET!;
                requestData.redirect_uri = Auth0Service.BACKEND_REDIRECT_URI;
            }

            const response = await axios.post(
                `${process.env.AUTH0_DOMAIN}/oauth/token`,
                qs.stringify(requestData),
                {
                    headers: {
                        ...this.axiosRequestConfig.headers,
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                }
            );

            return {
                idToken: response.data.id_token,
                refreshToken: response.data.refresh_token,
            };
        }
        catch (error) {
            throw new ExternalServerError([getAuth0Error(error)]);
        }
    }
}
