/* eslint-disable @typescript-eslint/no-unused-vars */
import { promisify } from "util";
import { Secret, SignOptions, sign, verify } from "jsonwebtoken";

import { GetAccessTokenParams } from "../interfaces";
import { InvalidTokenError } from "@moxfive-llc/common";

export class jsonWebToken {
    static async getAccessToken({ id, email, organizationId, accessToken }: GetAccessTokenParams) {
        return {
            accessToken: await promisify<GetAccessTokenParams, Secret, SignOptions>(sign)(
                { id, email, organizationId, accessToken }, `${process.env.JWT_SECRET}`,
                { expiresIn: 3600 - (5 * 60), algorithm: "HS256" }) // Expire token 55 minutes, 5 minutes earlier than microsoft expires time
        };
    }

    static async getRefreshToken({ refreshToken, pkceEnabled }: { refreshToken: string, pkceEnabled: boolean }) {
        return {
            refreshToken: await promisify<{ refreshToken: string, pkceEnabled: boolean }, Secret, SignOptions>(sign)(
                { refreshToken, pkceEnabled }, `${process.env.JWT_SECRET}`,
                { expiresIn: (24 * 60 * 60) - (10 * 60), algorithm: "HS256" }) // Expire token 23 Hrs 50 minutes, 10 minutes earlier then microsoft expires time
        };
    }

    static async getDetailsFromAccessToken({ accessToken }: { accessToken: string }) {
        try {
            return verify(accessToken, `${process.env.JWT_SECRET}`) as { accessToken: string };
        }
        catch (err) {
            throw new InvalidTokenError();
        }
    }

    static getRefreshTokenDetails({ refreshToken }: { refreshToken: string }) {
        try {
            return verify(refreshToken, `${process.env.JWT_SECRET}`, { algorithms: ["HS256"] }) as { refreshToken: string, pkceEnabled: boolean };
        }
        catch (err) {
            throw new InvalidTokenError();
        }
    }
}
