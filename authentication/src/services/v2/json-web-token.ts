/* eslint-disable @typescript-eslint/no-unused-vars */
import { promisify } from "util";
import { Secret, SignOptions, sign, verify } from "jsonwebtoken";

import { IAccessTokenParamsV2, IRefreshTokenParamsV2 } from "../../interfaces";
import { InvalidTokenError } from "@moxfive-llc/common";

export class JsonWebTokenV2Service {
    static async getAccessToken(data: IAccessTokenParamsV2) {
        return {
            accessToken: await promisify<IAccessTokenParamsV2, Secret, SignOptions>(sign)(
                data, `${process.env.JWT_SECRET}`,
                { expiresIn: 3600 - (5 * 60), algorithm: "HS256" }) // Expire token 55 minutes, 5 minutes earlier than microsoft expires time
            // TODO: Temporary kept it as 5 minutes for testing purpose
        };
    }

    static async getRefreshToken(data: IRefreshTokenParamsV2) {
        return {
            refreshToken: await promisify<IRefreshTokenParamsV2, Secret, SignOptions>(sign)(
                data, `${process.env.JWT_SECRET}`,
                { expiresIn: (24 * 60 * 60) - (10 * 60), algorithm: "HS256" }) // Expire token 23 Hrs 50 minutes, 10 minutes earlier then microsoft expires time
            // TODO: Temporary kept it as 10 minutes for testing purpose
        };
    }

    static async getTemporaryToken(data: { id: string }) {
        return {
            token: await promisify<{ id: string }, Secret, SignOptions>(sign)(
                data, `${process.env.JWT_SECRET}`,
                { expiresIn: 300, algorithm: "HS256" }) // Expire in 5 minutes
        };
    }

    static async getAccessTokenDetails({ accessToken }: { accessToken: string }) {
        try {
            return verify(accessToken, `${process.env.JWT_SECRET}`) as IAccessTokenParamsV2;
        }
        catch (err) {
            throw new InvalidTokenError();
        }
    }

    static getRefreshTokenDetails({ refreshToken }: { refreshToken: string }) {
        try {
            return verify(refreshToken, `${process.env.JWT_SECRET}`, { algorithms: ["HS256"] }) as IRefreshTokenParamsV2;
        }
        catch (err) {
            throw new InvalidTokenError();
        }
    }

    static async isTemporaryTokenValid({ token, userId }: { token: string, userId: string }) {
        try {
            const tokenDetails = verify(token, `${process.env.JWT_SECRET}`) as { id: string };
            return String(tokenDetails.id) === String(userId);
        }
        catch (err) {
            return false;
        }
    }
}
