import { TargetType } from "@moxfive-llc/common";

export class AuditLogV2 {
    static prepareModifiedProperties = ({ data, target, oldData = null }:
        {data: any, target: TargetType, oldData?: any}) => {

        const modifiedProperties: any = [];
        // eslint-disable-next-line guard-for-in
        for (const field in data) {
            const oldDataValue = oldData ? (oldData[String(field)] ?? "") : "";
            const newDataValue = (data[String(field)] ?? "");

            modifiedProperties.push({
                target,
                propertyName: field,
                oldValue: typeof oldDataValue === "string" ? oldDataValue : JSON.stringify(oldDataValue),
                newValue: typeof newDataValue === "string" ? newDataValue : JSON.stringify(newDataValue)
            });
        }

        return modifiedProperties;
    };
}
