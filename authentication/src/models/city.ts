import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

// An interface that describes the properties that are required to create a new Role
interface CityAttr {
  id: string,
  name: string,
  stateId: string,
}

// An interface that describes the properties that a Role Document has
interface CityDoc extends mongoose.Document {
  name: string,
  stateId: string,
  version: string
}

// An interface that describes the properties that a Role Model has
interface CityModel extends mongoose.Model<CityDoc> {
  build(attrs: CityAttr): CityDoc
}

const citySchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true
        },
        stateId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "state",
            required: true
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

citySchema.set("versionKey", "version");
citySchema.plugin(updateIfCurrentPlugin);

citySchema.statics.build = (attrs: CityAttr) => {
    return new City({
        _id: attrs.id,
        name: attrs.name,
        stateId: attrs.stateId
    });
};

const City = mongoose.model<CityDoc, CityModel>("City", citySchema);

export { City, CityDoc, CityAttr };
