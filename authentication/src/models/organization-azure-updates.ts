import mongoose from "mongoose";

interface OrganizationAzureUpdatesAttrs {
  organizationId: string;
  changeType: "updated";
  status: "Not Started" | "In Progress" | "Completed";
  errorMessage?: string
}

interface OrganizationAzureUpdatesDoc extends mongoose.Document {
  id: string;
  organizationId: string;
  changeType: string;
  status: string;
  createdAt: string;
  errorMessage: string | null;
}

interface OrganizationAzureUpdatesModel extends mongoose.Model<OrganizationAzureUpdatesDoc> {
  build(attrs: OrganizationAzureUpdatesAttrs): OrganizationAzureUpdatesDoc;
}

const organizationAzureUpdatesSchema = new mongoose.Schema(
    {
        organizationId: {
            type: String,
            required: true,
        },
        changeType: {
            type: String,
            required: true,
        },
        status: {
            type: String,
            required: true,
        },
        errorMessage: {
            type: String,
            default: null
        },
        createdAt: {
            type: Date,
            default: Date.now,
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

organizationAzureUpdatesSchema.statics.build = (attrs: OrganizationAzureUpdatesAttrs) => {
    return new OrganizationAzureUpdates(attrs);
};

const OrganizationAzureUpdates = mongoose.model<OrganizationAzureUpdatesDoc, OrganizationAzureUpdatesModel>(
    "organizationazureupdates",
    organizationAzureUpdatesSchema
);

export { OrganizationAzureUpdates, OrganizationAzureUpdatesAttrs, OrganizationAzureUpdatesDoc };
