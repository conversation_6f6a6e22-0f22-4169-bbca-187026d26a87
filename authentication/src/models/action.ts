import mongoose from "mongoose";

// An interface that describes the properties that are required to create a new Action
interface ActionAttrs {
    name: string;
    description: string;
    isEnabled: boolean;
    ruleIds: string[];
    serviceId: string;
    isModuleLevel: boolean;
    accessControlId: string;
    moxfiveExclusive: boolean;
    requireAuthorization: boolean;
}

// An interface that describes the properties that a Action Document has
interface ActionDoc extends mongoose.Document {
    id: string;
    name: string;
    description: string;
    isEnabled: boolean;
    ruleIds: string[];
    serviceId: string;
    isModuleLevel: boolean;
    accessControlId: string;
    moxfiveExclusive: boolean;
    requireAuthorization: boolean;
}

// An interface that describes the properties that a Action Model has
interface ActionModel extends mongoose.Model<ActionDoc> {
  build(attrs: ActionAttrs): ActionDoc
}

const actionSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        description: {
            type: String
        },
        isEnabled: {
            type: Boolean,
            default: true,
            required: true
        },
        ruleIds: {
            type: [mongoose.Types.ObjectId],
            ref: "Rule",
            required: true
        },
        serviceId: {
            type: mongoose.Types.ObjectId,
            ref: "Service",
            required: true
        },
        isModuleLevel: {
            type: Boolean,
            required: true
        },
        accessControlId: {
            type: mongoose.Types.ObjectId,
            ref: "AccessControl",
            required: true
        },
        moxfiveExclusive: {
            type: Boolean
        },
        requireAuthorization: {
            type: Boolean,
            default: true,
            required: true
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

const Action = mongoose.model<ActionDoc, ActionModel>("Action", actionSchema);

export { Action, ActionDoc, ActionAttrs };
