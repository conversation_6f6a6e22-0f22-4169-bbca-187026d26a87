import { referenceValueMongooseSchemaV2, ReferenceValueV2 } from "@moxfive-llc/common";
import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

// An interface that describes the properties to define a new Connections
interface ConnectionsAttrs {
    _id?: string;
    connectionName: string,
    organization: ReferenceValueV2;
    connectionType: ReferenceValueV2;
    externalConnectionId: string;
    configurationFile?: ReferenceValueV2 | null;
    domains: string[] | null;
    enforceAuthentication: boolean;
    isActive: boolean;
    clientSecretExpiration: number | null;
    clientSecretExpirationDate: string | null;
    createdBy: ReferenceValueV2;
    updatedBy: ReferenceValueV2;
    idpDomain?: string | null;
}

// An interface that describes the properties that Connections Document has
interface ConnectionsDoc extends mongoose.Document {
    id: string;
    connectionName: string,
    organization: ReferenceValueV2;
    connectionType: ReferenceValueV2;
    externalConnectionId: string;
    configurationFile: ReferenceValueV2 | null;
    domains: string[] | null;
    enforceAuthentication: boolean;
    isActive: boolean;
    clientSecretExpiration: number | null;
    clientSecretExpirationDate: string | null;
    createdBy: ReferenceValueV2;
    updatedBy: ReferenceValueV2;
    createdAt: string;
    updatedAt: string;
    version: number;
    idpDomain: string | null;
}

// An interface that describes the properties that a Connections Model has
interface ConnectionsModel extends mongoose.Model<ConnectionsDoc> {
    build(attrs: ConnectionsAttrs): ConnectionsDoc;
}

const ConnectionsSchema = new mongoose.Schema(
    {
        connectionName: {
            type: String,
            required: true
        },
        organization: {
            type: referenceValueMongooseSchemaV2(),
            required: true
        },
        connectionType: {
            type: referenceValueMongooseSchemaV2(),
            required: true
        },
        externalConnectionId: {
            type: String,
            required: true
        },
        domains: {
            type: [String],
            default: null
        },
        configurationFile: {
            type: referenceValueMongooseSchemaV2(),
            default: null
        },
        enforceAuthentication: {
            type: Boolean,
            required: true
        },
        isActive: {
            type: Boolean,
            required: true
        },
        clientSecretExpiration: {
            type: Number,
            default: null
        },
        clientSecretExpirationDate: {
            type: Date,
            default: null
        },
        idpDomain: {
            type: String,
            default: null
        },
        createdBy: {
            type: referenceValueMongooseSchemaV2(),
            required: true
        },
        updatedBy: {
            type: referenceValueMongooseSchemaV2(),
            required: true
        },
        createdAt: {
            type: Date,
            default: Date.now,
        },
        updatedAt: {
            type: Date,
            default: Date.now,
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

ConnectionsSchema.set("versionKey", "version");
ConnectionsSchema.plugin(updateIfCurrentPlugin);

ConnectionsSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

ConnectionsSchema.statics.build = (attrs: ConnectionsAttrs) => {
    return new Connections(attrs);
};

const Connections = mongoose.model<ConnectionsDoc, ConnectionsModel>("Connections", ConnectionsSchema);

export { Connections, ConnectionsAttrs, ConnectionsDoc };
