import mongoose from "mongoose";
import { ObjectId } from "bson";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";
import { FlexibleFieldUniqueLevel } from "../enums/flexible-field-unique-level.enum";
// An interface that describes the properties
// that are required to create a new Permission
interface ValuesArr {
    value: string,
    _id: string,
}

interface OrganizationFlexibleEventAttr {
    id: string,
    version: number
}

// An interface that describes the properties that Permission Type Document has
interface FlexibleValueAttr {
    name: string,
    key: string,
    values: ValuesArr[],
    multiChoice: boolean,
    uniqueLevel?: string,
    defaultValues?: string[],
    creatable?: boolean
}

// An interface that describes the properties
// that a Permission Document has
interface OrganizationFlexibleFieldDoc extends mongoose.Document {
    _id: ObjectId,
    id: string,
    name: string,
    key: string,
    values: ValuesArr[],
    uniqueLevel: string,
    defaultValues: string[],
    multiChoice: boolean,
    creatable: boolean,
    createdAt: string,
    updatedAt: string,
    version: number
}

// An interface that describes the properties
// that a Permission Model has
interface FlexibleFieldModel extends mongoose.Model<OrganizationFlexibleFieldDoc> {
    build(attrs: FlexibleValueAttr): OrganizationFlexibleFieldDoc;
    findByEvent(event: OrganizationFlexibleEventAttr): Promise<OrganizationFlexibleFieldDoc>;
}

const ValuesSchema = new mongoose.Schema({
    value: {
        type: String,
        required: true
    }
},
{
    versionKey: false,
    toJSON: {
        transform(doc, ret) {
            ret.id = ret._id;
            delete ret._id;
        },
    },
});

const OrganizationFlexibleFieldSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        key: {
            type: String,
            unique: true,
            required: true,
        },
        values: {
            type: [ValuesSchema],
            required: true
        },
        multiChoice: {
            type: Boolean,
            required: true
        },
        uniqueLevel: {
            type: String,
            default: FlexibleFieldUniqueLevel.PLATFORM
        },
        defaultValues: {
            type: [mongoose.Schema.Types.ObjectId],
            default: []
        },
        creatable: {
            type: Boolean,
            default: false
        },
        createdAt: {
            type: Date,
            default: Date.now,
        },
        updatedAt: {
            type: Date,
            default: Date.now,
        },
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

OrganizationFlexibleFieldSchema.set("versionKey", "version");
OrganizationFlexibleFieldSchema.plugin(updateIfCurrentPlugin);

OrganizationFlexibleFieldSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

OrganizationFlexibleFieldSchema.statics.build = (attrs: FlexibleValueAttr) => {
    return new OrganizationFlexibleField(attrs);
};

OrganizationFlexibleFieldSchema.statics.findByEvent = (event: OrganizationFlexibleEventAttr) => {
    return OrganizationFlexibleField.findOne({ _id: event.id, version: event.version - 1 });
};

const OrganizationFlexibleField = mongoose.model<OrganizationFlexibleFieldDoc, FlexibleFieldModel>("OrganizationFlexibleFields", OrganizationFlexibleFieldSchema);

export { OrganizationFlexibleField, OrganizationFlexibleFieldDoc, ValuesArr };
