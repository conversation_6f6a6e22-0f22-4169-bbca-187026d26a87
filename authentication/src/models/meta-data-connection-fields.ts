import { referenceValueMongooseSchemaV2, ReferenceValueV2 } from "@moxfive-llc/common";
import mongoose from "mongoose";

// An interface that describes the properties that are required to create a new MetaDataConnectionFields
interface MetaDataConnectionFieldsAttrs {
    name: string;
    connectionType: ReferenceValueV2;
    displayName: string;
    description: string;
    type: ReferenceValueV2;
    key: string;
    sequence: number;
    required: boolean;
    values: any[];
    placeholder: string;
    conditions: { field: string, value: string }[] | null;
    validations: any[]; // Todo: need to set some optional fields for particular field validations
    neededInPlatform: boolean;
    conditionOperator: string;
    isCopyable: boolean;
    defaultValue: any;
    isMasked: boolean;
    isDomain: boolean;
}

// An interface that describes the properties that a MetaDataConnectionFields Document has
interface MetaDataConnectionFieldsDoc extends mongoose.Document {
    id: string;
    name: string;
    connectionType: ReferenceValueV2;
    displayName: string;
    description: string;
    type: ReferenceValueV2;
    key: string;
    sequence: number;
    required: boolean;
    values: any[];
    placeholder: string;
    conditions: { field: string, value: string }[] | null;
    validations: any[]; // Todo: need to set some optional fields for particular field validations
    neededInPlatform: boolean;
    conditionOperator: string;
    isCopyable: boolean;
    defaultValue: any;
    isMasked: boolean;
    isDomain: boolean;
}

// An interface that describes the properties that a MetaDataConnectionFields Model has
interface MetaDataConnectionFieldsModel extends mongoose.Model<MetaDataConnectionFieldsDoc> {
  build(attrs: MetaDataConnectionFieldsAttrs): MetaDataConnectionFieldsDoc
}

const conditionSchema = new mongoose.Schema(
    {
        field: {
            type: String,
            required: true
        },
        value: {
            type: String,
            required: true
        }
    },
    {
        _id: false,
        versionKey: false
    }
);

const MetaDataConnectionFieldsSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true
        },
        connectionType: {
            type: referenceValueMongooseSchemaV2(),
            required: true
        },
        displayName: {
            type: String,
            required: true
        },
        description: {
            type: String
        },
        key: {
            type: String,
            default: null
        },
        type: {
            type: referenceValueMongooseSchemaV2("MetaDataConnectionFieldsType"),
            required: true
        },
        sequence: {
            type: Number,
            required: true
        },
        required: {
            type: Boolean,
            required: true
        },
        values: {
            type: [mongoose.Schema.Types.Mixed],
            required: true
        },
        placeholder: {
            type: String
        },
        conditions: {
            type: [conditionSchema]
        },
        validations: {
            type: [mongoose.Schema.Types.Mixed]
        },
        neededInPlatform: {
            type: Boolean,
            default: true
        },
        conditionOperator: {
            type: String,
            required: true
        },
        isCopyable: {
            type: Boolean,
            default: false
        },
        defaultValue: {
            type: mongoose.Schema.Types.Mixed
        },
        isMasked: {
            type: Boolean,
            default: false
        },
        isDomain: {
            type: Boolean,
            default: false
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

const MetaDataConnectionFields = mongoose.model<MetaDataConnectionFieldsDoc, MetaDataConnectionFieldsModel>("MetaDataConnectionFields", MetaDataConnectionFieldsSchema);

export { MetaDataConnectionFields, MetaDataConnectionFieldsDoc, MetaDataConnectionFieldsAttrs };
