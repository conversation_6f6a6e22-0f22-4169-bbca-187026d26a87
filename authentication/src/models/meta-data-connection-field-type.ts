import { referenceValueMongooseSchemaV2, ReferenceValueV2 } from "@moxfive-llc/common";
import mongoose from "mongoose";

// An interface that describes the properties that are required to create a new MetaDataConnectionFieldsType
interface MetaDataConnectionFieldsTypeAttrs {
    name: string;
    validations: any; // Todo: need to set some optional fields for particular field validations
}

// An interface that describes the properties that a MetaDataConnectionFieldsType Document has
interface MetaDataConnectionFieldsTypeDoc extends mongoose.Document {
    id: string;
    name: string;
    validations: any; // Todo: need to set some optional fields for particular field validations
}

// An interface that describes the properties that a MetaDataConnectionFieldsType Model has
interface MetaDataConnectionFieldsTypeModel extends mongoose.Model<MetaDataConnectionFieldsTypeDoc> {
  build(attrs: MetaDataConnectionFieldsTypeAttrs): MetaDataConnectionFieldsTypeDoc
}

const MetaDataConnectionFieldsTypeSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        validations: {
            type: mongoose.Schema.Types.Mixed
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

const MetaDataConnectionFieldsType = mongoose.model<MetaDataConnectionFieldsTypeDoc, MetaDataConnectionFieldsTypeModel>("MetaDataConnectionFieldsType", MetaDataConnectionFieldsTypeSchema);

export { MetaDataConnectionFieldsType, MetaDataConnectionFieldsTypeDoc, MetaDataConnectionFieldsTypeAttrs };
