import { ReferenceValueV2 } from "@moxfive-llc/common";
import mongoose from "mongoose";
import { referenceValueMongooseSchemaV2 } from "../util/v2/reference-value-mongoose-schema-v2";

interface AuthLogAttrs {
  type: string;
  eventCode: string;
  user: ReferenceValueV2 | null,
  email: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  details: any;
  isFailure: boolean;
  description?: string | null;
  organization: ReferenceValueV2 | null,
}

interface AuthLogModel extends mongoose.Model<AuthLogDoc> {
  build(attrs: AuthLogAttrs): AuthLogDoc;
}

interface AuthLogDoc extends mongoose.Document {
  type: string;
  eventCode: string;
  user: ReferenceValueV2 | null,
  email: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  details: any;
  isFailure: boolean;
  description: string | null;
  organization: ReferenceValueV2 | null,
}

const authLogSchema = new mongoose.Schema(
    {
        type: {
            type: String,
            required: true,
        },
        eventCode: {
            type: String,
            required: true,
        },
        user: {
            type: referenceValueMongooseSchemaV2("UserV2"),
            default: null,
        },
        email: {
            type: String,
            default: null,
        },
        ipAddress: {
            type: String,
            required: true,
        },
        userAgent: {
            type: String,
            required: true,
        },
        timestamp: {
            type: Date,
            required: true,
        },
        details: {
            type: mongoose.Schema.Types.Mixed,
            required: true,
        },
        isFailure: {
            type: Boolean,
            required: true,
        },
        description: {
            type: String,
            default: null,
        },
        organization: {
            type: referenceValueMongooseSchemaV2("OrganizationV2"),
            default: null,
        },
        _ts: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
        timestamps: true,
    }
);

authLogSchema.index({ timestamp: -1 });
authLogSchema.index({ userId: 1 });
authLogSchema.index({ email: 1 });
authLogSchema.index({ status: 1 });
authLogSchema.index({ organizationId: 1 });

authLogSchema.index({ _ts: 1 }, { expireAfterSeconds: 180 * 24 * 60 * 60 });

authLogSchema.statics.build = (attrs: AuthLogAttrs) => {
    return new AuthLog(attrs);
};

const AuthLog = mongoose.model<AuthLogDoc, AuthLogModel>("AuthLog", authLogSchema);

export { AuthLog, AuthLogDoc, AuthLogAttrs };
