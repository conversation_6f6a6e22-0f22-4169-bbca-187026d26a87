import mongoose from "mongoose";

// An interface that describes the properties that are required to create a new Metadata connection type
interface MetadataConnectionTypeAttrs {
    name: string;
    strategy: string;
    logo: string;
    isActive: boolean;
    defaultData: Record<string, any> | null;
}

// An interface that describes the properties that a Metadata connection type has
interface MetadataConnectionTypeDoc extends mongoose.Document {
    id: string;
    name: string;
    strategy: string;
    logo: string;
    isActive: boolean;
    defaultData: Record<string, any> | null;

}

// An interface that describes the properties that a Metadata connection type has
interface MetadataConnectionTypeModel extends mongoose.Model<MetadataConnectionTypeDoc> {
  build(attrs: MetadataConnectionTypeAttrs): MetadataConnectionTypeDoc
}

const MetadataConnectionTypeSchema = new mongoose.Schema(
    {
        name: {
            type: String,
        },
        strategy: {
            type: String,
            required: true
        },
        logo: {
            type: String,
            required: true
        },
        isActive: {
            type: Boolean,
            default: true
        },
        defaultData: {
            type: mongoose.Schema.Types.Mixed,
            default: null
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

const MetadataConnectionType = mongoose.model<MetadataConnectionTypeDoc, MetadataConnectionTypeModel>("MetadataConnectionType", MetadataConnectionTypeSchema);

export { MetadataConnectionType, MetadataConnectionTypeDoc, MetadataConnectionTypeAttrs };

