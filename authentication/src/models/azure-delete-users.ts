import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

interface AzureDeleteUserAttrs {
  owner: boolean,
  groupId: string,
  userId: string
}

interface AzureDeleteUserDoc extends mongoose.Document {
  id: string,
  owner: boolean,
  groupId: string,
  userId: string,
  noOfAttempts: number,
  version: string,
  createdAt: string,
  updatedAt: string
}

interface AzureDeleteUserModel extends mongoose.Model<AzureDeleteUserDoc> {
  build(attrs: AzureDeleteUserAttrs): AzureDeleteUserDoc,
}

const azureDeleteUserSchema = new mongoose.Schema(
    {
        owner: {
            type: Boolean,
            required: true
        },
        groupId: {
            type: String,
            required: true
        },
        userId: {
            type: String,
            required: true
        },
        noOfAttempts: {
            type: Number,
            default: 0
        },
        createdAt: {
            type: Date,
            required: true,
            default: Date.now
        },
        updatedAt: {
            type: Date,
            required: true,
            default: Date.now
        },
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

azureDeleteUserSchema.set("versionKey", "version");
azureDeleteUserSchema.plugin(updateIfCurrentPlugin);

azureDeleteUserSchema.statics.build = (attrs: AzureDeleteUserAttrs) => {
    return new AzureDeleteUser(attrs);
};

azureDeleteUserSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

const AzureDeleteUser = mongoose.model<AzureDeleteUserDoc, AzureDeleteUserModel>("azureDeleteUsers", azureDeleteUserSchema);

export { AzureDeleteUser, AzureDeleteUserAttrs, AzureDeleteUserDoc };
