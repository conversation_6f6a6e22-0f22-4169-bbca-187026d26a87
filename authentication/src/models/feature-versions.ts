import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

interface FeatureVersionAttrs {
    name: string,
    currentVersion: string
}

interface FeatureVersionDoc extends mongoose.Document {
    id: string,
    name: string,
    currentVersion: string,
    version: number
}

interface FeatureVersionModel extends mongoose.Model<FeatureVersionDoc> {
  build(attrs: FeatureVersionAttrs): FeatureVersionDoc,
}

const announcementSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true
        },
        currentVersion: {
            type: String,
            required: true
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

announcementSchema.set("versionKey", "version");
announcementSchema.plugin(updateIfCurrentPlugin);

announcementSchema.pre("save", function (done) {
    done();
});

announcementSchema.statics.build = (attrs: FeatureVersionAttrs) => {
    return new FeatureVersion(attrs);
};

const FeatureVersion = mongoose.model<FeatureVersionDoc, FeatureVersionModel>("FeatureVersion", announcementSchema);

export { FeatureVersion, FeatureVersionDoc, FeatureVersionAttrs };
