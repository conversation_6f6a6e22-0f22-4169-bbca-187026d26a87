import { User } from "../models/user";
import { ClientSession } from "mongoose";
import { UserV2Doc } from "../models/v2/users-v2";
import { OperationTypesEnums } from "../enums/operation-types.enum";

export const userV2ToV1Sync = async ({ user, operationType, session }:
    { user: UserV2Doc, operationType: OperationTypesEnums, session: ClientSession | null }) => {
    if (operationType === OperationTypesEnums.INSERT || operationType === OperationTypesEnums.UPDATE) {
        // If User Exists
        if (user) {
            const data: Record<string, any> = { ...user };

            // Transofrm role, add organizationId
            if (data.role) {
                data.role = [data.role.id];
            }

            data.organizationId = data.organization.id;

            // Delete organization, organizationTypes, isOwner
            delete data.organization;
            delete data.organizationTypes;
            // delete data.isOwner;

            // Update user data in v1
            await User.findByIdAndUpdate(String(user._id), data, { upsert: true, session });
        }
    }
    else if (operationType === OperationTypesEnums.DELETE) {
        await User.findByIdAndDelete(String(user._id), {
            session
        });
    }
};
