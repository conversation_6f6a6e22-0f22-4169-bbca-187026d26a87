import { typeAllowedFiltersMapping } from "./type-allowed-filters-mapping";
import { ValueTypesEnum } from "../enums/value-types.enum";
import { FilterFields } from "../interfaces";

export const fetchSectionFilterFields = (path: any) => {
    const fields :FilterFields[] = [];

    if(Array.isArray(path)) {
        // Loop through all fields of that section
        path.forEach((field: any) => {
            if(field.filterable && field.type) {
                const isFlexibleField = Boolean(field.flexibleField);
                fields.push({
                    name: field.name,
                    displayName: field.displayName,
                    isFlexibleField: isFlexibleField,
                    key: isFlexibleField ? field.key : null,
                    allowedFilters: typeAllowedFiltersMapping[field.type as ValueTypesEnum] || [],
                    type: field.type,
                    userFlexibleField: field.userFlexibleField,
                    organizationFlexibleField: field.organizationFlexibleField,
                    organizationTypes: field.organizationTypes,
                    moxfiveUserAssign: field.moxfiveUserAssign,
                    organizations: field.organizations,
                    ignoreSanitize: field.ignoreSanitize,
                    isQuickFilter: !!field.isQuickFilter,
                    isBooleanField: !!field.isBooleanField,
                    authenticationLogsTypes: !!field.authenticationLogsTypes,
                    users: !!field.users
                });
            }
        });
    }
    return fields;
};
