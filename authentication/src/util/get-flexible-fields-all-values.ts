import { OrganizationFlexibleField } from "../models/organization-flexible-fields";

export const getFlexibleFieldsAllValues = async (fieldKeys: string[]) => {
    const flexibleFields = await OrganizationFlexibleField.find({ key: { $in: [...fieldKeys] } }, { _id: 0, values: 1 }).lean().exec();
    const flexibleFieldsValuesMap = new Map();
    flexibleFields.forEach(flexibleField => {
        flexibleField.values.forEach(value => {
            flexibleFieldsValuesMap.set(String(value._id), value.value);
        });
    });
    return flexibleFieldsValuesMap;
};

