const dataLabelColors: Record<string, string> = {
    "#F26A26": "#FFFFFF",
    "#DF6F37": "#FFFFFF",
    "#EA882E": "#2E2E2E",
    "#F69767": "#2E2E2E",
    "#FF9058": "#2E2E2E",
    "#E8E8E8": "#5D5D5D",
    "#D3D3D3": "#4D4D4D",
    "#BFBFBF": "#4D4D4D",
    "#A6A6A6": "#2E2E2E",
    "#89D5FF": "#4D4D4D",
    "#59C4FF": "#3E3E3E",
    "#29B3FE": "#3E3E3E",
    "#0B95DE": "#1F1F1F",
    "#69BCFF": "#3E3E3E",
    "#50A2E5": "#2E2E2E",
    "#3885C3": "#1F1F1F",
    "#2469A1": "#FFFFFF",
    "#477CAD": "#FFFFFF",
    "#2F5F8B": "#FFFFFF",
    "#1C4469": "#FFFFFF",
};
export const colorMappings: { [key: number]: { color: string, dataLabelColor: string }[] } = {
    0: [],
    1: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }],
    2: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }],
    3: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    4: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    5: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    6: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    7: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    8: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    9: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    10: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    11: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    12: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    13: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    14: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    15: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#89D5FF",
        dataLabelColor: dataLabelColors["#89D5FF"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    16: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#89D5FF",
        dataLabelColor: dataLabelColors["#89D5FF"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#69BCFF",
        dataLabelColor: dataLabelColors["#69BCFF"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    17: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#89D5FF",
        dataLabelColor: dataLabelColors["#89D5FF"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#69BCFF",
        dataLabelColor: dataLabelColors["#69BCFF"]
    }, {
        color: "#50A2E5",
        dataLabelColor: dataLabelColors["#50A2E5"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    18: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#89D5FF",
        dataLabelColor: dataLabelColors["#89D5FF"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#69BCFF",
        dataLabelColor: dataLabelColors["#69BCFF"]
    }, {
        color: "#50A2E5",
        dataLabelColor: dataLabelColors["#50A2E5"]
    }, {
        color: "#3885C3",
        dataLabelColor: dataLabelColors["#3885C3"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    19: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#89D5FF",
        dataLabelColor: dataLabelColors["#89D5FF"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#69BCFF",
        dataLabelColor: dataLabelColors["#69BCFF"]
    }, {
        color: "#50A2E5",
        dataLabelColor: dataLabelColors["#50A2E5"]
    }, {
        color: "#3885C3",
        dataLabelColor: dataLabelColors["#3885C3"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    }, {
        color: "#2F5F8B",
        dataLabelColor: dataLabelColors["#2F5F8B"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }],
    20: [{
        color: "#F26A26",
        dataLabelColor: dataLabelColors["#F26A26"]
    }, {
        color: "#DF6F37",
        dataLabelColor: dataLabelColors["#DF6F37"]
    }, {
        color: "#EA882E",
        dataLabelColor: dataLabelColors["#EA882E"]
    }, {
        color: "#F69767",
        dataLabelColor: dataLabelColors["#F69767"]
    }, {
        color: "#FF9058",
        dataLabelColor: dataLabelColors["#FF9058"]
    }, {
        color: "#E8E8E8",
        dataLabelColor: dataLabelColors["#E8E8E8"]
    }, {
        color: "#D3D3D3",
        dataLabelColor: dataLabelColors["#D3D3D3"]
    }, {
        color: "#BFBFBF",
        dataLabelColor: dataLabelColors["#BFBFBF"]
    }, {
        color: "#A6A6A6",
        dataLabelColor: dataLabelColors["#A6A6A6"]
    }, {
        color: "#89D5FF",
        dataLabelColor: dataLabelColors["#89D5FF"]
    }, {
        color: "#59C4FF",
        dataLabelColor: dataLabelColors["#59C4FF"]
    }, {
        color: "#29B3FE",
        dataLabelColor: dataLabelColors["#29B3FE"]
    }, {
        color: "#0B95DE",
        dataLabelColor: dataLabelColors["#0B95DE"]
    }, {
        color: "#69BCFF",
        dataLabelColor: dataLabelColors["#69BCFF"]
    }, {
        color: "#50A2E5",
        dataLabelColor: dataLabelColors["#50A2E5"]
    }, {
        color: "#3885C3",
        dataLabelColor: dataLabelColors["#3885C3"]
    }, {
        color: "#2469A1",
        dataLabelColor: dataLabelColors["#2469A1"]
    }, {
        color: "#477CAD",
        dataLabelColor: dataLabelColors["#477CAD"]
    }, {
        color: "#2F5F8B",
        dataLabelColor: dataLabelColors["#2F5F8B"]
    },  {
        color: "#1C4469",
        dataLabelColor: dataLabelColors["#1C4469"]
    }]
};
