/* eslint-disable security/detect-object-injection */
import { BodyInvalidBadRequestError, InvalidResourceIdBadRequestError } from "@moxfive-llc/common";
import { NextFunction, Request, Response } from "express";
import { FlexibleFieldNameKey } from "../interfaces";
import { OrganizationFlexibleField } from "../models/organization-flexible-fields";

export const flexibleFieldValidation = (fields: FlexibleFieldNameKey[] = [], setValues = false) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const data: any = {};
            const fieldsExist: string[] = [];
            const valuesMap: Map<string, string> = new Map();

            // Loop through all fields and req body has that field then add it in data
            fields.forEach(field => {
                if(req.body.hasOwnProperty(field.name)) {
                    const key = field.key;

                    data[field.name] = {
                        ...req.body[field.name],
                        key: key
                    };

                    fieldsExist.push(key);
                }
            });

            // If there is any flexible fields provided
            if(fieldsExist.length) {
                // Fetch flexible fields info
                const flexibleFields = await OrganizationFlexibleField.find({ key: { $in: fieldsExist } });
                const invalidIdsError = [];
                const multiSelectError = [];
                const invalidValuesError = [];
                const valueDocs: { id: string, value: string }[] = [];

                // Loop through all  flexible fields provided in req body
                // eslint-disable-next-line guard-for-in
                for(const field in data) {
                    // Chcek whether povided id is valid ot not
                    const flexibleField = flexibleFields.find(f => data[field].key === f.key && String(f._id) === String(data[field].id));
                    // If invalid Id then push to invalidIds
                    if(!flexibleField) {
                        invalidIdsError.push({
                            name: field,
                            value: data[field].id,
                            message: "These id is invalid.",
                        });
                        continue;
                    }

                    // If only single select is there and multiple values are provided
                    if(!flexibleField.multiChoice && data[field].valueIds && data[field].valueIds.length > 1) {
                        multiSelectError.push({
                            name: field,
                            value: data[field].valueIds,
                            message: "Only one value can be selected for the specified field."
                        });
                        continue;
                    }

                    // Check provided values are valid or not
                    for(const value of data[field].valueIds) {
                        const valueDoc = flexibleField.values.find(v => String(v._id) === String(value));
                        flexibleField.values.forEach(v => valuesMap.set(String(v._id), v.value));
                        // eslint-disable-next-line max-depth
                        if(!valueDoc) {
                            invalidValuesError.push({
                                name: field,
                                value: data[field].valueIds,
                                message: "These values are invalid."
                            });
                            continue;
                        }
                        else if (setValues) {
                            valueDocs.push({
                                id: String(valueDoc._id),
                                value: valueDoc.value
                            });
                            continue;
                        }
                    }

                    // Set valueIds in req body
                    if(data[field].valueIds && data[field].valueIds.length) {
                        req.body[field] = setValues ? valueDocs : data[field].valueIds;
                    }
                    else {
                        req.body[field] = null;
                    }

                }
                if(invalidIdsError.length) {
                    throw new InvalidResourceIdBadRequestError(invalidIdsError);
                }

                if(multiSelectError.length) {
                    throw new BodyInvalidBadRequestError(multiSelectError);
                }

                if(invalidValuesError.length) {
                    throw new InvalidResourceIdBadRequestError(invalidValuesError);
                }

                req.flexibleFieldValuesMap = valuesMap;
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
