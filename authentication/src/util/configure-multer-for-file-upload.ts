import multer, { DiskStorageOptions, FileFilterCallback } from "multer";
import { Request, Express } from "express";
import { BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, validExtensions } from "@moxfive-llc/common";

const validMimeTypes = ["image/jpeg", "image/png", "image/svg+xml"];

export const configureMulterForFileUpload = () => {
    // if (!fs.existsSync("/uploads/")) {
    //     fs.mkdirSync("/uploads/");
    // }

    const diskStorageConfig: DiskStorageOptions = {
        destination: (req: Request, file: Express.Multer.File, callback) => {
            callback(null, `/uploads/`);
        },
        filename: (req: Request, file: Express.Multer.File, callback) => {
            callback(null, `${Date.now()}-${file.originalname}`);
        }
    };

    const storage = multer.diskStorage(diskStorageConfig);
    const upload = multer({
        storage,
        limits: {
            fileSize: 2097152 // 2 MB
        },
        fileFilter(req: Request, file: Express.Multer.File, callback: FileFilterCallback) {
            // Check mimetype is allowed or not
            if (validMimeTypes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.ALLOWED_FILE_TYPES, "Invalid file type."
                ));
            }
            // Fetch file extension
            const fileExtension = file.originalname.split(".").pop() ?? "";

            // Check File extension is allowed or not
            if ((validExtensions[file.mimetype] ?? []).includes(fileExtension.toLowerCase())) {
                callback(null, true);
            }
            else {
                callback(new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.ALLOWED_FILE_TYPES, "Invalid file type."
                ));
            }
        }
    });

    return upload;
};
