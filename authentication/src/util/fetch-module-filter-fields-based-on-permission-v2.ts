/* eslint-disable no-await-in-loop */
import { fetchSection<PERSON>ilterFields } from "./fetch-single-section-filter-fields";
import { FilterFields } from "../interfaces";
import { sortByPropertyInObject } from "./common";
import { OrganizationFlexibleField } from "../models/organization-flexible-fields";
import { OrganizationV2 } from "../models/v2/oragnizations-v2";
import { OrganizationType } from "../models/organization-type";
import { UserFlexibleField } from "../models/user-flexible-fields";
// import { MilestonesV2 } from "../../models/v2/milestones-v2";

export const fetchModuleFilterFieldsBasedOnPermissionV2 = async ({ sections, assignedActions }:
    {
        sections: { path: any, action?: string, name?: string }[], assignedActions: Set<string>
    }) => {
    let fields: FilterFields[] = [];
    sections.forEach(section => {
        if (section.name === "commonBase") {
            const commonBaseFields = fetchSectionFilterFields(section.path);
            // Generally, for commonBase no permission would be checked, but there are scenarios where permission check is needed for some fields.
            // Those fields should have checkPermission property as true in incident-fields.json file.
            if (section.action && (!assignedActions.has(section.action)) && Array.isArray(section.path)) {
                section.path.forEach(field => {
                    if (field.checkPermission) {
                        const removeFieldIndex = commonBaseFields.findIndex(f => f.name === field.name);
                        if (removeFieldIndex !== -1) {
                            commonBaseFields.splice(removeFieldIndex, 1);
                        }
                    }
                });
            }
            fields = fields.concat(commonBaseFields);
        }
        else if (!section.action || assignedActions.has(section.action)) {
            const sectionFields = fetchSectionFilterFields(section.path);
            fields = fields.concat(sectionFields);
        }
    });

    const keys = fields.map(field => field.key).filter(val => Boolean(val));
    const userFlexibleFieldsMap = new Map();
    const orgFlexibleFieldsMap = new Map();
    if(keys.length) {
        const flexibleFields = await UserFlexibleField.find({ key: { $in: keys } }, { values: 1, key: 1 });
        flexibleFields.forEach(field => {
            userFlexibleFieldsMap.set(field.key, field.values.sort(sortByPropertyInObject("value")));
        });

        const orgFlexibleFields = await OrganizationFlexibleField.find({ key: { $in: keys } }, { values: 1, key: 1 });
        orgFlexibleFields.forEach(field => {
            orgFlexibleFieldsMap.set(field.key, field.values.sort(sortByPropertyInObject("value")));
        });
    }

    const formattedFields: FilterFields[] = [];

    for(const field of fields) {
        let values: {id: string, value: string}[] | null = null;

        // Fetch organization types
        if(field.organizationTypes) {
            const organizationTypes = await OrganizationType.find().select("name").lean().exec();
            values = organizationTypes.map(type => ({
                id: String(type._id),
                value: type.name
            }));
        }
        // Fetch organizations
        else if(field.organizations) {
            const organizations = await OrganizationV2.find().select("name").lean().exec();
            values = organizations.map(org => ({
                id: String(org._id),
                value: org.name
            }));
        }
        else if(field.moxfiveUserAssign) {
            const userIds: Set<string> = new Set();

            // Fetch org who as either PM, TA or sales sponsor
            const organizations = await OrganizationV2.find({
                $or: [
                    { moxfivePMSponsor: { $ne: null } },
                    { moxfiveTASponsor: { $ne: null } },
                    { moxfiveSalesSponsor: { $ne: null } }
                ]
            });

            // Make user Ids
            organizations.forEach(org => {
                if (org.moxfivePMSponsor && userIds.has(String(org.moxfivePMSponsor.id))) {
                    values?.push(org.moxfivePMSponsor);
                }
                if (org.moxfiveTASponsor && userIds.has(String(org.moxfiveTASponsor.id))) {
                    values?.push(org.moxfiveTASponsor);
                }
                if (org.moxfiveSalesSponsor && userIds.has(String(org.moxfiveSalesSponsor.id))) {
                    values?.push(org.moxfiveSalesSponsor);
                }
            });
        }
        // If field is flexible field & it has key then fetch values of it from flexibleFieldsMap
        else if(field.isFlexibleField && field.key) {
            if(field.userFlexibleField) {
                values = userFlexibleFieldsMap.get(field.key) || null;
            }
            else if(field.organizationFlexibleField) {
                values = orgFlexibleFieldsMap.get(field.key) || null;
            }
            else {
                values = null;
            }
        }

        formattedFields.push({
            displayName: field.displayName || field.name,
            name: field.name,
            isFlexibleField: field.isFlexibleField,
            values,
            allowedFilters: field.allowedFilters,
            type: field.type,
            isQuickFilter: !!field.isQuickFilter,
            isBooleanField: !!field.isBooleanField
        });
    }

    return formattedFields;
};
