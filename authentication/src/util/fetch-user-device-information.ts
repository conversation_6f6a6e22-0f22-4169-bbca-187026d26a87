import { UAParser } from "ua-parser-js";
import { camalize } from "./common";

export const fetchUserDeviceInformation = (userAgent: string) => {
    let result: string = "Unknown";
    const { browser, device, ua, os } = UAParser(userAgent);
    // Handle Postman
    if (ua.includes("Postman")) {
        result = `API Client - Postman`;
    }
    // If both browser and os names are present
    else if (browser.name && os.name) {
        // Prepare browser and os string
        const browserOS = `${browser.name} - ${os.name}`;

        // If device type is present set result
        if (device.type) {
            result = `${camalize(device.type)} - ${browserOS}`;
        }
        // Otherwise fetch device type from user agent
        else {
            let deviceType = "";
            if (ua.match(/iPhone/i)) {
                deviceType = "iPhone";
            }
            else if (ua.match(/iPad/i)) {
                deviceType = "iPad";
            }
            else if (ua.match(/iPod/i)) {
                deviceType = "iPod";
            }
            else if (ua.match(/Android/i)) {
                deviceType = "Android";
            }
            // Tablets
            else if (ua.match(/Tablet|Tab/i)) {
                deviceType = "Tablet";
            }
            // Laptops/Desktops (crude detection based on OS and lack of mobile/tablet indicators)
            else if (ua.match(/Windows|Macintosh|Linux/i) && !ua.match(/Mobile|Tablet/i)) {
                deviceType = "Laptop/Desktop";
            }
            result = `${deviceType} - ${browserOS}`;
        }
    }
    return result;
};
