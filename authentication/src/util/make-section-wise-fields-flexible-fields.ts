/* eslint-disable @typescript-eslint/ban-ts-comment */
import { FlexibleFieldNameKey } from "../interfaces";

export const MakeSectionWiseFieldsFlexibleFields = (path: any) => {
    const fields: { [key: string] : string[] } = {};
    const flexibleFieldsNameKey: FlexibleFieldNameKey[] = [];
    if(typeof path === "object" && !Array.isArray(path) && path !== null) {
        for(const section in path) {
            // @ts-ignore
            if(path[String(section)]) {
                // @ts-ignore1
                path[String(section)].forEach(field => {
                    if(!field.ignore) {
                        if(field.flexibleField) {
                            flexibleFieldsNameKey.push({
                                name: field.name,
                                key: field.key || ""
                            });
                        }
                        if(!fields.hasOwnProperty(String(section))) {
                            fields[String(section)] = [field.name];
                        }
                        else {
                            fields[String(section)].push(field.name);
                        }
                    }
                });
            }
        }
    }
    return {
        fields,
        flexibleFieldsNameKey
    };
};
