import { isValidMongoObjectId } from "./index";

export const everyElementMongoId = (valueIds: string[]) => {
    return valueIds.every(valueId => {
        return isValidMongoObjectId(valueId);
    });
};

export const urlValidation = (value: string) => {
    /* eslint-disable-next-line */
    const urlValidation = /^$|^((ftp|http|https):\/\/)?(www\.)?([\w$+!*'(),#%{}|\\^~[\]`<>-]+(\.[\w$+!*'(),#%{}|\\^~[\]`<>-]+)+)(\/[\w$+!*'(),#%{}|\\^~[\]`<>.-]*)*(\?[\w$+!*'(),#%{}|\\^~[\]`<>-]+=[^&=]+(&[\w$+!*'(),#%{}|\\^~[\]`<>-]+=[^&=]+)*)?$/im;
    return urlValidation.test(value);
};

// Function to check if a single IP address is valid
const isValidIpAddress = (ip: string): boolean => {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
    return ipRegex.test(ip);
};

// Function to validate an array of IP addresses
export const validateIpAddresses = (ipAddresses: string[]): boolean => {
    return ipAddresses.every(ip => isValidIpAddress(ip));
};
