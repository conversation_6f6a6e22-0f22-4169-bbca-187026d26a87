import crypto from "crypto";

export const aesDecrypt = (text: string, ENCRYPTION_KEY: string) => {
    try {
        const textParts = text.split(":");
        const iv = Buffer.from(textParts.shift() as string, "hex");
        const encryptedText = Buffer.from(textParts.join(":"), "hex");
        const decipher = crypto.createDecipheriv("aes-256-cbc", Buffer.from(ENCRYPTION_KEY), iv);
        let decrypted = decipher.update(encryptedText);

        decrypted = Buffer.concat([decrypted, decipher.final()]);

        return decrypted.toString();
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    catch(_: any) {
        return null;
    }

};
