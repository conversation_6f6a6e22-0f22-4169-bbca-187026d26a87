/* eslint-disable no-useless-escape */
import mongoose from "mongoose";
import { SortQuery } from "../interfaces";
import { randomBytes, scrypt, timingSafeEqual } from "crypto";
import { promisify } from "util";

const hasProtocol = new RegExp("^([a-z]+://|//)", "i");

const scryptAsync = promisify(scrypt);

export const ipV46Regex =
    // eslint-disable-next-line max-len,security/detect-unsafe-regex
    /(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-fA-F\d]{1,4}:){7}(?:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,2}|:)|(?:[a-fA-F\d]{1,4}:){4}(?:(?::[a-fA-F\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,3}|:)|(?:[a-fA-F\d]{1,4}:){3}(?:(?::[a-fA-F\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,4}|:)|(?:[a-fA-F\d]{1,4}:){2}(?:(?::[a-fA-F\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,5}|:)|(?:[a-fA-F\d]{1,4}:){1}(?:(?::[a-fA-F\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,6}|:)|(?::(?:(?::[a-fA-F\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,7}|:)))(?:%[0-9a-zA-Z]{1,})?$)/gm;

export const fetchDefaultSortObject = (sort = ""): SortQuery => {
    if (sort) {
        return {};
    }
    return { _id: 1 };
};

const doesURIContainsProtocol = (str: string) => {
    return hasProtocol.test(str);
};

export function convertToObjectId(id: string) {
    return new mongoose.Types.ObjectId(id);
}

export const sanitizeIP = (ipAddress: string) => {
    return ipAddress.replace(/\.(?=[^\.]+$)/, "[.]");
};

export const unSanitizeIP = (sanitizedIP: string) => {
    return sanitizedIP.replace(/\[.]/, ".");
};

export const sortByPropertyInObject = (property: string) => {
    return (a: { [key: string]: any }, b: { [key: string]: any }) =>
        a[String(property)].localeCompare(b[String(property)]);
};

export const sanitizeURI = (URIString: string) => {
    let sanitizedString = URIString;
    if (doesURIContainsProtocol(sanitizedString)) {
        sanitizedString = sanitizedString.replace(/tt/, "xx");
        sanitizedString = sanitizedString.replace(/\:/, "[:]");
    }

    sanitizedString = sanitizedString.replace(/\./g, "[.]");
    return sanitizedString;
};

export const sanitizeEmail = (email: string) => {
    return email.replace(/\./g, "[.]");
};

export const unSanitizeURI = (sanitizedString: string) => {
    let actualString = sanitizedString.replace(/\[:]/, ":");
    if (doesURIContainsProtocol(actualString)) {
        actualString = actualString.replace(/xx/, "tt");
    }

    actualString = actualString.replace(/\[.]/g, ".");
    return actualString;
};

export const unSanitizeEmail = (sanitizedEmail: string) => {
    return sanitizedEmail.replace(/\[.]/g, ".");
};

export const getUserName = ({
    firstName,
    lastName,
    displayName,
}: {
    firstName: string | null;
    lastName: string | null;
    displayName: string;
}) => {
    if (!firstName && !lastName) {
        return displayName;
    }
    let name = "";
    if (firstName) {
        name += firstName;
        if (lastName) {
            name += " ";
        }
    }
    if (lastName) {
        name += lastName;
    }
    return name;
};

export const intersectArrays = (a: string[], b: string[]) => {
    const setB = new Set(b);
    return [...new Set(a)].filter(x => setB.has(x));
};

export const restrictSpacesAfterPeriod = (number: number, spaces = 2) => {
    const splitNumber = String(number).split(".");
    if (splitNumber.length > 2) {
        return false;
    }

    if (splitNumber.length === 2 && splitNumber[1].length !== +spaces) {
        return false;
    }

    return true;
};

export const isIPAddress = (value: string) => ipV46Regex.test(value || "");

export const flattenObject = ({ obj, fields = [], excludeFields = [] }: {
    obj: any
    fields?: string[],
    excludeFields?: string[]
}) => {
    const fieldsSet = new Set(fields);
    const excludeFieldsSet = new Set(excludeFields);

    const result: Record<any, any> = {};

    for (const [key, val] of Object.entries(obj)) {
        // Determine should we need to flatten this object or not based on fieldsSet and excludedFieldsSet
        let shouldInclude = fieldsSet.size ? fieldsSet.has(key) : true;
        if (excludeFieldsSet.has(key)) {
            shouldInclude = false;
        }

        //  First check val is object or not, if it is object and if it should be flatten then continue
        if (val !== null && typeof val === "object" && Object.prototype === Object.getPrototypeOf(val) && shouldInclude) {
            Object.assign(result, val);
        }
        else {
            result[String(key)] = val;
        }
    }

    return result;
};

export const generateObjectId = () => {
    return new mongoose.Types.ObjectId();
};

export function generateRandomString(length = 32) {
    return randomBytes(length).toString("hex");
}

export async function scryptHash(token: string) {
    const salt = randomBytes(16).toString("hex");
    const derivedKey = await scryptAsync(token, salt, 64);
    return salt + ":" + (derivedKey as Buffer).toString("hex");
}

export async function scryptHashVerify(token: string, hash: string) {
    const [salt, key] = hash.split(":");
    const keyBuffer = Buffer.from(key, "hex");
    const derivedKey = await scryptAsync(token, salt, 64);
    return timingSafeEqual(keyBuffer, derivedKey as Buffer);
}

export const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

export const generateTemporaryToken = ({ length = 42, minutes = 20 }: {
    length?: number, minutes?: number
}) => {
    const token = generateRandomString(length);
    return {
        token,
        expiry: Date.now() + minutes * 60 * 1000
    };
};

// List of common free email domains
export const freeEmailDomains = [
    "rapidops.com",
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "protonmail.com",
    "mail.com",
    "yandex.com",
    "zoho.com",
    "live.com",
    "msn.com",
    "comcast.net",
    "att.net"
];

export const isBusinessEmail = (email: string) => {
    // Convert email to lowercase for consistent checking
    // eslint-disable-next-line no-param-reassign
    email = email.toLowerCase().trim();

    // Extract domain from email
    const domain = email.split("@")[1];

    // Check if domain is in free email list
    return !freeEmailDomains.includes(domain);
};

export const camalize = function camalize(str: string) {
    return str.toLowerCase().replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase());
};

export const countLastXMinutesDateRecordsFromArray = (dates: string[], minutes = 1440) => { // Means it's default to 24 hours
    const lastXMinutes = Date.now() - (minutes * 60 * 1000); // 24 hours in milliseconds
    return dates.reduce((count: number, date: string) => {
        return new Date(date).getTime() >= lastXMinutes ? count + 1 : count;
    }, 0);
};

export const fieldRegex = /^[a-zA-Z0-9_]+$/;
