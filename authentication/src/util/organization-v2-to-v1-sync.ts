import { OrganizationV2Doc } from "../models/v2/oragnizations-v2";
import { OperationTypesEnums } from "../enums/operation-types.enum";
import { ClientSession } from "mongoose";
import { Organization } from "../models/organization";

export const organizationV2ToV1Sync = async ({ organization, operationType, session }:
    { organization: OrganizationV2Doc, operationType: OperationTypesEnums, session: ClientSession | null }) => {
    if (operationType === OperationTypesEnums.INSERT || operationType === OperationTypesEnums.UPDATE) {
        const data: Record<string, any> = { ...organization };

        // Add organizationTypeIds, transform industry, serviceLines
        if (data.hasOwnProperty("organizationTypes")) {
            data.organizationTypeIds = data.organizationTypes ? data.organizationTypes.map((record: any) => record.id) : null;
        }

        if (data.hasOwnProperty("industry")) {
            data.industry = data.industry ? [data.industry.id] : null;
        }

        if (data.hasOwnProperty("serviceLines")) {
            data.serviceLines = data.serviceLines ? data.serviceLines.map((record: any) => record.id) : null;
        }

        if (data.hasOwnProperty("partnerType")) {
            data.partnerType = data.partnerType ? [data.partnerType.id] : null;
        }

        if (data.hasOwnProperty("coverageStates")) {
            data.coverageStates = data.coverageStates ? data.coverageStates.map((record: any) => record.id) : null;
        }

        if (data.hasOwnProperty("offerings")) {
            data.offerings = data.offerings ? data.offerings.map((record: any) => record.id) : null;
        }

        if (data.hasOwnProperty("moxfivePMSponsor")) {
            data.moxfivePMSponsor = data.moxfivePMSponsor ? data.moxfivePMSponsor.id : null;
        }

        if (data.hasOwnProperty("moxfiveTASponsor")) {
            data.moxfiveTASponsor = data.moxfiveTASponsor ? data.moxfiveTASponsor.id : null;
        }

        if (data.hasOwnProperty("moxfiveSalesSponsor")) {
            data.moxfiveSalesSponsor = data.moxfiveSalesSponsor ? data.moxfiveSalesSponsor.id : null;
        }

        if (data.hasOwnProperty("languages")) {
            data.languages = data.languages ? data.languages.map((record: any) => record.id) : null;
        }

        // Delete organizationTypes
        delete data.organizationTypes;

        // Update organizations data in v1
        await Organization.findByIdAndUpdate(String(organization._id), data, { upsert: true, session });

    }
    else if (operationType === OperationTypesEnums.DELETE) {
        await Organization.findByIdAndDelete(String(organization._id), { session });
    }
};
