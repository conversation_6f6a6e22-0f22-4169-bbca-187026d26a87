import { flexibleFieldValue } from "../interfaces/flexible-field-value";
import { ValuesArr } from "../models/organization-flexible-fields";

export const getFlexibleFieldMultipleValues = async ({ values, fieldValues }:
    { values: string[], fieldValues: ValuesArr[] }) => {

    // Create map of fields all values
    const valuesMap: Map<string, string> = new Map();
    fieldValues.forEach(record => {
        valuesMap.set(String(record._id), record.value);
    });

    const flexibleFieldValues: flexibleFieldValue[] = [];
    values.forEach(valueId => {
        // Fetch value from valuesMap and if value found then add it in flexibleFieldValues
        const stringValueId = String(valueId);

        const value = valuesMap.get(stringValueId);
        if(value) {
            flexibleFieldValues.push({
                id: stringValueId,
                value
            });
        }
    });

    return flexibleFieldValues;
};
