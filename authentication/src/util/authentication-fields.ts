import { OrganizationFlexibleFields } from "../enums/organization-flexible-field.enum";
import { ValueTypesEnum } from "../enums/value-types.enum";
import { UserFlexibleFields } from "../enums/user-flexible-field.enum";

export const authenticationFields = {
    organizations: [
        // Company Information
        { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
        {
            name: "organizationTypeIds", displayName: "Type", filterable: true, type: ValueTypesEnum.DROPDOWN, organizationTypes: true, flexibleField: false,
            required: false, key: null
        },
        {
            name: "industry", displayName: "Industry", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
            key: OrganizationFlexibleFields.Industry, organizationFlexibleField: true, multiple: false
        },
        {
            name: "serviceLines", displayName: "Service Lines", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
            key: OrganizationFlexibleFields.ServiceLines, organizationFlexibleField: true, multiple: true
        },
        { name: "website", displayName: "Website", filterable: true, type: ValueTypesEnum.URL, flexibleField: false, required: false, key: null, sortable: true },
        { name: "highLevelCompanyInformation", displayName: "High Level Company Information", flexibleField: false, required: false, key: null },
        {
            name: "descriptionOfEnvironment", displayName: "Description of Environment", flexibleField: false, required: false,
            key: null
        },
        { name: "officeLocations", displayName: "Office Locations", flexibleField: false, required: false, key: null },
        {
            name: "numberOfEmployees", displayName: "Number Of Employees", filterable: true, type: ValueTypesEnum.NUMBER, flexibleField: false, required: false,
            key: null, sortable: true
        },
        {
            name: "numberOfITStaff", displayName: "Number Of IT Staff", filterable: true, type: ValueTypesEnum.NUMBER, flexibleField: false, required: false,
            key: null, sortable: true
        },
        { name: "itStaffLocation", displayName: "IT Staff Location", flexibleField: false, required: false, key: null },
        { name: "activePartner", displayName: "Active Partner", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },

        // Hotline details
        {
            name: "moxfiveHotline", displayName: "MOXFIVE Hotline", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null,
            sortable: true
        },
        { name: "hotlineEmail", displayName: "Hotline Email", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, key: null, sortable: true },
        {
            name: "hotlinePhoneNumber", displayName: "Hotline Phone Number", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
            sortable: true
        },

        // Contact details
        { name: "msaSignatureDate", displayName: "MSA Signature Date", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, required: false, key: null },
        {
            name: "billingContactName", displayName: "Billing Contact Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
            sortable: true
        },
        {
            name: "billingContactEmail", displayName: "Billing Contact Email", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, key: null,
            sortable: true
        },
        { name: "billingContactPhone", displayName: "Billing Contact Phone", flexibleField: false, required: false, key: null },
        { name: "billingAddresses", displayName: "Billing Addresses", flexibleField: false, required: false, key: null },

        // Partner details
        {
            name: "partnerType", displayName: "Partner Type", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
            key: OrganizationFlexibleFields.PartnerType, organizationFlexibleField: true, partnerField: true, multiple: false
        },
        { name: "onboardedDate", displayName: "Onboarded Date", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, required: false, key: null, sortable: true },
        {
            name: "coverageStates", displayName: "Coverage States", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
            key: OrganizationFlexibleFields.CoverageStates, organizationFlexibleField: true, partnerField: true, multiple: true
        },
        {
            name: "offerings", displayName: "Offerings", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
            key: OrganizationFlexibleFields.Offerings, organizationFlexibleField: true, partnerField: true, multiple: true
        },
        { name: "partnerEula", displayName: "Partner Eula", flexibleField: false, required: false, key: null },
        { name: "partnerTermsConditions", displayName: "Partner Terms & Conditions", flexibleField: false, required: false, key: null },
        {
            name: "inboundRequestInfo", displayName: "Inbound Request Info", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, key: null,
            ignoreSanitize: true, sortable: true
        },
        {
            name: "moxfivePMSponsor", displayName: "MOXFIVE PM Sponsor", filterable: true, type: ValueTypesEnum.DROPDOWN, moxfiveUserAssign: true, flexibleField: false,
            required: false, key: null
        },
        {
            name: "moxfiveTASponsor", displayName: "MOXFIVE TA Sponsor", filterable: true, type: ValueTypesEnum.DROPDOWN, moxfiveUserAssign: true, flexibleField: false,
            required: false, key: null
        },
        {
            name: "moxfiveSalesSponsor", displayName: "MOXFIVE Sales Sponsor", filterable: true, type: ValueTypesEnum.DROPDOWN, moxfiveUserAssign: true, flexibleField: false,
            required: false, key: null
        },
        {
            name: "languages", displayName: "Languages", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
            key: OrganizationFlexibleFields.Languages, organizationFlexibleField: true, partnerField: true, multiple: true
        },

        // General
        { name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },

    ],
    organizationUsers: [
        { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
        {
            name: "email", displayName: "Email Address", filterable: true, type: ValueTypesEnum.EMAIL, ignoreSanitize: true, flexibleField: false, required: false, key: null,
            sortable: true
        },
        {
            name: "role", displayName: "Role", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, userFlexibleField: true, required: false,
            key: UserFlexibleFields.Role
        },
        { name: "jobTitle", displayName: "Job title", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null },
        // { name: "isOwner", displayName: "User Type (Owner)", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },
        { name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },
        { name: "officePhone", displayName: "Phone", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null },
    ],
    // Use this for list user APIs for V2-filtering, as we consider boolean properties as dropdown
    organizationUsersV2: {
        commonBase: [
            { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
            {
                name: "email", displayName: "Email Address", filterable: true, type: ValueTypesEnum.EMAIL, ignoreSanitize: true, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                name: "role", displayName: "Role", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, userFlexibleField: true, required: false,
                key: UserFlexibleFields.Role
            },
            { name: "jobTitle", displayName: "Job title", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null },
            {
                // name: "isOwner", displayName: "User Type (Owner)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true
            },
            {
                name: "isEnabled", displayName: "Status", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true, isQuickFilter: true
            },
            { name: "officePhone", displayName: "Phone", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null },
            {
                name: "isEmailVerified", displayName: "Email Verified", filterable: true, sortable: true,
                type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null, isBooleanField: true
            },
            {
                name: "isAccountSetupDone", displayName: "Account Setup Done", filterable: true, sortable: true,
                type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null, isBooleanField: true
            },
            {
                name: "isAccountLocked", displayName: "Account Locked", filterable: true, sortable: true,
                type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null, isBooleanField: true
            },
        ],
        modifiedAt: [
            { name: "createdAt", displayName: "Created Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
            { name: "updatedAt", displayName: "Updated Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        ]
    },
    users: [
        { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
        {
            name: "email", displayName: "Email Address", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, ignoreSanitize: true,
            key: null, sortable: true
        },
        {
            name: "organization", displayName: "Organization", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, organizations: true,
            required: false, key: null
        },
        {
            name: "organizationTypes", displayName: "Organization Types", filterable: true, type: ValueTypesEnum.DROPDOWN, organizationTypes: true, flexibleField: false,
            required: false, key: null
        },
        {
            name: "role", displayName: "Role", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, userFlexibleField: true, required: false,
            key: UserFlexibleFields.Role
        },
        {
            name: "jobTitle", displayName: "Job title", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
            sortable: true
        },
        // { name: "isOwner", displayName: "User Type (Owner)", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },
        { name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },
        { name: "officePhone", displayName: "Phone", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null },
    ],
    modifiedAt: [
        { name: "createdAt", displayName: "Created Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        { name: "updatedAt", displayName: "Updated Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
    ],
    environment: {
        general: [
            { name: "numberOfADUsers", displayName: "Number 0f AD Users", flexibleField: false, required: true, key: null },
            { name: "numberOfPhysicalServers", displayName: "Number Of Physical Servers", flexibleField: false, required: false, key: null },
            { name: "numberOfVirtualServers", displayName: "Number Of Virtual Servers", flexibleField: false, required: false, key: null },
            { name: "virtualization", displayName: "Virtualization", flexibleField: true, required: false, key: "incidentManagement-environment-general-virtualization" },
            { name: "serverOSs", displayName: "Server OSs", flexibleField: true, required: false, key: "incidentManagement-environment-general-serverOSs" },
            { name: "numberOfWorkstations", displayName: "Number Of Workstations", flexibleField: false, required: false, key: null },
            { name: "workstationOSs", displayName: "Workstation OSs", flexibleField: true, required: false, key: "incidentManagement-environment-general-workstationOSs" },
            // eslint-disable-next-line max-len
            { name: "outOfBandManagement", displayName: "Out-Of-BandManagement", flexibleField: true, required: false, key: "incidentManagement-environment-general-outOfBandManagement" },
            { name: "outOfBandManagementNotes", displayName: "Out-Of-Band Management Notes", flexibleField: false, required: false, key: null },
            { name: "totalNumberOfSystems", displayName: "Total Number Of Systems", flexibleField: false, required: false, key: null, ignore: true }
        ],
        activeDirectory: [
            { name: "numberOfDCs", displayName: "Number Of DCs", flexibleField: false, required: false, key: null },
            { name: "numberOfDomains", displayName: "Number Of Domains", flexibleField: false, required: false, key: null },
            // eslint-disable-next-line max-len
            { name: "trustRelationships", displayName: "Trust Relationships", flexibleField: true, required: false, key: "incidentManagement-environment-activeDirectory-trustRelationships" },
            // eslint-disable-next-line max-len
            { name: "adFunctionalLevel", displayName: "AD Functional Level", flexibleField: true, required: false, key: "incidentManagement-environment-activeDirectory-adFunctionalLevel" },
            { name: "azureAD", displayName: "Azure AD", flexibleField: false, required: false, key: null },
            { name: "azurePasswordWriteback", displayName: "Azure Password Writeback", flexibleField: false, required: false, key: null },
            { name: "adNotes", displayName: "AD Notes", flexibleField: false, required: false, key: null },
        ],
        backups: [
            { name: "backupSolution", displayName: "Backup Solution", flexibleField: true, required: false, key: "incidentManagement-environment-backups-backupSolution" },
            { name: "backupSource", displayName: "Backup Source", flexibleField: true, required: false, key: "incidentManagement-environment-backups-backupSource" },
            { name: "backupType", displayName: "Backup Type", flexibleField: true, required: false, key: "incidentManagement-environment-backups-backupType" },
            // eslint-disable-next-line max-len
            { name: "backupsDomainJoined", displayName: "Backups Domain Joined", flexibleField: true, required: false, key: "incidentManagement-environment-backups-backupsDomainJoined" },
            { name: "backupNotes", displayName: "Backup Notes", flexibleField: false, required: false, key: null }
        ],
        email: [
            { name: "emailSolution", displayName: "Email Solution", flexibleField: true, required: false, key: "incidentManagement-environment-email-emailSolution" },
            // eslint-disable-next-line max-len
            { name: "emailAuthentication", displayName: "Email Authentication", flexibleField: true, required: false, key: "incidentManagement-environment-email-emailAuthentication" },
            { name: "emailNotes", displayName: "Email Notes", flexibleField: false, required: false, key: null },
        ],
        solutions: [
            { name: "firewallSolution", displayName: "Firewall Solution", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-firewallSolution" },
            { name: "firewallNotes", displayName: "Firewall Notes", flexibleField: false, required: false, key: null },
            // eslint-disable-next-line max-len
            { name: "endpointSecurityProducts", displayName: "Endpoint Security Products", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-endpointSecurityProducts", multiple: true },
            { name: "antivirusNotes", displayName: "Antivirus Notes", flexibleField: false, required: false, key: null },
            { name: "edrNotes", displayName: "EDR Notes", flexibleField: false, required: false, key: null },
            { name: "mfaDeployed", displayName: "MFA Deployed", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-mfaDeployed" },
            { name: "mfaSolutions", displayName: "MFA Solutions", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-mfaSolutions" },
            // eslint-disable-next-line max-len
            { name: "remoteAccessSolution", displayName: "Remote Access Solution", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-remoteAccessSolution" },
            { name: "remoteAccessNotes", displayName: "Remote Access Notes", flexibleField: false, required: false, key: null },
            // eslint-disable-next-line max-len
            { name: "systemManagementSolution", displayName: "System Management Solution", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-systemManagementSolution" },
            { name: "systemManagementNotes", displayName: "System Management Notes", flexibleField: false, required: false, key: null },
            { name: "pamSolution", displayName: "PAM Solution", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-pamSolution" },
            { name: "pamSolutionNotes", displayName: "PAM Solution Notes", flexibleField: false, required: false, key: null },
            // eslint-disable-next-line max-len
            { name: "segmentationSolution", displayName: "Segmentation Solution", flexibleField: true, required: false, key: "incidentManagement-environment-solutions-segmentationSolution" },
            { name: "segmentationSolutionNotes", displayName: "Segmentation Solution Notes", flexibleField: false, required: false, key: null },
        ],
    },
    usersV2: {
        commonBase: [
            { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
            {
                name: "email", displayName: "Email Address", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, ignoreSanitize: true,
                key: null, sortable: true
            },
            {
                name: "organization", displayName: "Organization", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, organizations: true,
                required: false, key: null, sortable: true, keepSame: true
            },
            {
                name: "organizationTypes", displayName: "Organization Types", filterable: true, type: ValueTypesEnum.DROPDOWN, organizationTypes: true, flexibleField: false,
                required: false, key: null, sortable: true, multiple: true
            },
            {
                name: "role", displayName: "Role", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, userFlexibleField: true, required: false,
                key: UserFlexibleFields.Role, sortable: true
            },
            {
                name: "jobTitle", displayName: "Job title", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                // name: "isOwner", displayName: "User Type (Owner)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true
            },
            {
                name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true, isQuickFilter: true
            },
            { name: "officePhone", displayName: "Phone", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
            {
                name: "isEmailVerified", displayName: "Email Verified", filterable: true, sortable: true,
                type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null, isBooleanField: true
            },
            {
                name: "isAccountSetupDone", displayName: "Account Setup Done", filterable: true, sortable: true,
                type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null, isBooleanField: true
            },
            {
                name: "isAccountLocked", displayName: "Account Locked", filterable: true, sortable: true,
                type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null, isBooleanField: true
            }
        ],
        modifiedAt: [
            { name: "createdAt", displayName: "Created Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
            { name: "updatedAt", displayName: "Updated Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        ]
    },
    organizationsV2: {
        commonBase: [
            // Company Information
            { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
            { name: "connection", displayName: "Connection", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: true, key: null, sortable: true },
            {
                name: "organizationTypes", displayName: "Type", filterable: true, type: ValueTypesEnum.DROPDOWN, organizationTypes: true, flexibleField: false,
                required: false, key: null, multiple: true, sortable: true
            },
            {
                name: "industry", displayName: "Industry", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
                key: OrganizationFlexibleFields.Industry, organizationFlexibleField: true, multiple: false, sortable: true
            },
            {
                name: "serviceLines", displayName: "Service Lines", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
                key: OrganizationFlexibleFields.ServiceLines, organizationFlexibleField: true, multiple: true, sortable: true
            },
            {
                name: "website", displayName: "Website", filterable: true, type: ValueTypesEnum.URL, flexibleField: false, required: false, key: null, sortable: true,
                sanitized: true
            },
            {
                name: "highLevelCompanyInformation", displayName: "High Level Company Information", filterable: true, type: ValueTypesEnum.TEXTAREA, flexibleField: false,
                required: false, key: null, sortable: true
            },
            {
                name: "descriptionOfEnvironment", displayName: "Description of Environment", filterable: true, type: ValueTypesEnum.TEXTAREA, flexibleField: false, required: false,
                key: null, sortable: true
            },
            { name: "officeLocations", displayName: "Office Locations", flexibleField: false, required: false, key: null },
            {
                name: "numberOfEmployees", displayName: "Number Of Employees", filterable: true, type: ValueTypesEnum.NUMBER, flexibleField: false, required: false,
                key: null, sortable: true
            },
            {
                name: "numberOfITStaff", displayName: "Number Of IT Staff", filterable: true, type: ValueTypesEnum.NUMBER, flexibleField: false, required: false,
                key: null, sortable: true
            },
            { name: "itStaffLocation", displayName: "IT Staff Location", flexibleField: false, required: false, key: null },
            {
                name: "activePartner", displayName: "Active Partner", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true
            },

            // Hotline details
            {
                name: "moxfiveHotline", displayName: "MOXFIVE Hotline", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true
            },
            {
                name: "hotlineEmail", displayName: "Hotline Email", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, key: null, sortable: true,
                sanitized: true
            },
            {
                name: "hotlinePhoneNumber", displayName: "Hotline Phone Number", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
                sortable: true
            },

            // Contact details
            {
                name: "msaSignatureDate", displayName: "MSA Signature Date", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                name: "billingContactName", displayName: "Billing Contact Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                name: "billingContactEmail", displayName: "Billing Contact Email", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, key: null,
                sortable: true, sanitized: true
            },
            { name: "billingContactPhone", displayName: "Billing Contact Phone", flexibleField: false, required: false, key: null, sortable: true },
            { name: "billingAddresses", displayName: "Billing Addresses", flexibleField: false, required: false, key: null },

            // Partner details
            {
                name: "partnerType", displayName: "Partner Type", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
                key: OrganizationFlexibleFields.PartnerType, organizationFlexibleField: true, partnerField: true, multiple: false, sortable: true
            },
            {
                name: "onboardedDate", displayName: "Onboarded Date", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                name: "coverageStates", displayName: "Coverage States", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
                key: OrganizationFlexibleFields.CoverageStates, organizationFlexibleField: true, partnerField: true, multiple: true, sortable: true
            },
            {
                name: "offerings", displayName: "Offerings", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
                key: OrganizationFlexibleFields.Offerings, organizationFlexibleField: true, partnerField: true, multiple: true, sortable: true
            },
            { name: "partnerEula", displayName: "Partner Eula", flexibleField: false, required: false, key: null, sortable: true },
            { name: "partnerTermsConditions", displayName: "Partner Terms & Conditions", flexibleField: false, required: false, key: null, sortable: true },
            {
                name: "inboundRequestInfo", displayName: "Inbound Request Info", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, key: null,
                ignoreSanitize: true, sortable: true
            },
            {
                name: "moxfivePMSponsor", displayName: "MOXFIVE PM Sponsor", filterable: true, type: ValueTypesEnum.DROPDOWN, moxfiveUserAssign: true, flexibleField: false,
                required: false, key: null, multiple: false, sortable: true
            },
            {
                name: "moxfiveTASponsor", displayName: "MOXFIVE TA Sponsor", filterable: true, type: ValueTypesEnum.DROPDOWN, moxfiveUserAssign: true, flexibleField: false,
                required: false, key: null, multiple: false, sortable: true
            },
            {
                name: "moxfiveSalesSponsor", displayName: "MOXFIVE Sales Sponsor", filterable: true, type: ValueTypesEnum.DROPDOWN, moxfiveUserAssign: true, flexibleField: false,
                required: false, key: null, multiple: false, sortable: true
            },
            {
                name: "languages", displayName: "Languages", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, required: false,
                key: OrganizationFlexibleFields.Languages, organizationFlexibleField: true, partnerField: true, multiple: true, sortable: true
            },

            // General
            {
                name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true
            },
        ],
        modifiedAt: [
            { name: "createdAt", displayName: "Created Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
            { name: "updatedAt", displayName: "Updated Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        ]
    },
    usersV3: {
        commonBase: [
            { name: "firstName", displayName: "First Name", filterable: false, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: false },
            { name: "lastName", displayName: "Last Name", filterable: false, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: false },
            { name: "displayName", displayName: "Display Name", filterable: false, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: false },
            { name: "userLocation", displayName: "User Location", filterable: false, flexibleField: false, required: false, key: null, sortable: false },
            {
                name: "email", displayName: "Email Address", filterable: true, type: ValueTypesEnum.EMAIL, flexibleField: false, required: false, ignoreSanitize: true,
                key: null, sortable: true
            },
            {
                name: "organization", displayName: "Organization", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, organizations: true,
                required: false, key: null, sortable: true, keepSame: true
            },
            {
                name: "organizationTypes", displayName: "Organization Types", filterable: true, type: ValueTypesEnum.DROPDOWN, organizationTypes: true, flexibleField: false,
                required: false, key: null, sortable: true, multiple: true
            },
            {
                name: "role", displayName: "Role", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: true, userFlexibleField: true, required: false,
                key: UserFlexibleFields.Role, sortable: true
            },
            {
                name: "jobTitle", displayName: "Job title", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                // name: "isOwner", displayName: "User Type (Owner)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true
            },
            {
                name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false, key: null,
                sortable: true, isBooleanField: true, isQuickFilter: true
            },
            { name: "officePhone", displayName: "Phone", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
        ],
        modifiedAt: [
            { name: "createdAt", displayName: "Created Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
            { name: "updatedAt", displayName: "Updated Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        ]
    },
    connection: {
        commonBase: [
            {
                name: "organization", displayName: "Organization", filterable: true, type: ValueTypesEnum.DROPDOWN,
                flexibleField: false, required: true, key: null, sortable: true
            },
            {
                name: "connectionType", displayName: "Enterprise Connection", filterable: true, type: ValueTypesEnum.DROPDOWN,
                flexibleField: false, required: true, key: null, sortable: true
            },
            // todo: filterable true for domain check ideas
            {
                name: "domains", displayName: "Domain", filterable: false, type: ValueTypesEnum.STRING,
                flexibleField: false, required: false, key: null, sortable: true
            },
            {
                name: "enforceAuthentication", displayName: "Enforce Authentication", filterable: true, type: ValueTypesEnum.DROPDOWN,
                flexibleField: false, required: false, key: null, sortable: true, isBooleanField: true
            },
            {
                name: "isActive", displayName: "Status", filterable: true, type: ValueTypesEnum.DROPDOWN,
                flexibleField: false, required: false, key: null, sortable: true, isBooleanField: true
            },
            // Special Case: if using this then this will be the date for applying filters don't consider this for months which was passed
            {
                name: "clientSecretExpirationDate", displayName: "Client Secret Expiration", type: ValueTypesEnum.DATE,
                filterable: true, flexibleField: false, required: false, key: null, sortable: true
            },
        ],
        modifiedAt: [
            { name: "createdAt", displayName: "Created Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
            { name: "updatedAt", displayName: "Updated Time", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        ],
        modifiedBy: [
            { name: "createdBy", displayName: "Created By", type: ValueTypesEnum.DROPDOWN, sortable: true },
            { name: "updatedBy", displayName: "Updated By", type: ValueTypesEnum.DROPDOWN, sortable: true },
        ]
    },
    authenticationLogs: {
        commonBase: [
            {
                name: "type", displayName: "Type", filterable: true, type: ValueTypesEnum.STRING_WITH_EQ,
                flexibleField: false, required: true, key: null, sortable: true, authenticationLogsTypes: true, ignoreSanitize: true
            },
            {
                name: "user", displayName: "User", filterable: true, type: ValueTypesEnum.STRING_WITH_EQ,
                flexibleField: false, required: false, key: null, sortable: true, users: true
            },
            {
                name: "email", displayName: "Email", filterable: true, type: ValueTypesEnum.STRING,
                flexibleField: false, required: true, key: null, sortable: true
            },
            {
                name: "ipAddress", displayName: "IP Address", filterable: true, type: ValueTypesEnum.IP_ADDRESS,
                flexibleField: false, required: true, key: null, sortable: true
            },
            {
                name: "userAgent", displayName: "User Agent", filterable: false, type: ValueTypesEnum.STRING,
                flexibleField: false, required: true, key: null, sortable: false
            },
            {
                name: "timestamp", displayName: "Activity Date", filterable: true, type: ValueTypesEnum.DATE,
                flexibleField: false, required: true, key: null, sortable: true
            },
            {
                name: "isFailure", displayName: "Failed", filterable: true, type: ValueTypesEnum.BOOLEAN,
                flexibleField: false, required: true, key: null, sortable: true
            },
            {
                name: "description", displayName: "Description", filterable: false, type: ValueTypesEnum.STRING,
                flexibleField: false, required: false, key: null, sortable: false
            },
            {
                name: "organization", displayName: "Organization", filterable: true, type: ValueTypesEnum.STRING_WITH_EQ,
                flexibleField: false, required: false, key: null, sortable: true, organizations: true
            }
        ]
    }
};
