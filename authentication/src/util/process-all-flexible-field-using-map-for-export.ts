import { getFlexibleFieldValuesFromIds } from "./get-flexible-field-values-from-ids";

export const processAllFlexibleFieldUsingMapForExport = (fields: string[], flexibleFieldsValuesMap: Map<string, string>, data: any) => {
    fields.forEach(field => {
        const values = getFlexibleFieldValuesFromIds(data[String(field)] as string[], flexibleFieldsValuesMap);
        if (values && values.length) {
            const assignedValues = values.map(val => val.value);
            data[String(field)] = assignedValues.join(";");
        }
        else {
            data[String(field)] = "";
        }
    });

    return data;
};
