import { MakeSingleSectionFieldsFlexibleFields } from "./make-single-section-fields-flexible-fields-environments";

export const getFlexibleFieldsBasedOnActions = (actions: string[], actionToFlexibleFieldArrayObj: { [key: string]: any }) => {
    let flexibleFields: {
        name: string,
        key: string,
        conditionalKeys?: { [key: string]: string } | null
    }[] = [];
    actions.forEach(action => {
        const { flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(actionToFlexibleFieldArrayObj[String(action)]);
        flexibleFields = flexibleFields.concat(flexibleFieldsNameKey);
    });

    return flexibleFields;
};

