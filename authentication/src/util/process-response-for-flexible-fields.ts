import { getFlexibleFieldMultipleValues } from "./get-flexible-field-multiple-values";
import { OrganizationFlexibleField, ValuesArr } from "../models/organization-flexible-fields";

export const processResponseForFlexibleFields = async (flexibleFields: { name: string, key: string }[], data: any) => {
    // Fetch all the flexible fields details
    const keys = flexibleFields.map(field => field.key);

    const flexibleFieldDetails = await OrganizationFlexibleField.find(
        { key: { $in: keys } },
        { key: 1, values: 1 }
    ).lean().exec();

    const flexibleFieldsMap: Map<string, ValuesArr[]> = new Map();

    flexibleFieldDetails.forEach(field => {
        flexibleFieldsMap.set(field.key, field.values);
    });

    // Loop through all flexible fields and get values for each
    await Promise.all(flexibleFields.map(async (field) => {
        if (data[field.name] && data[field.name].length) {
            data[field.name] = await getFlexibleFieldMultipleValues({
                values: data[field.name] as string[],
                fieldValues: flexibleFieldsMap.get(field.key) ?? []
            });
        }
    }));

    return data;
};
