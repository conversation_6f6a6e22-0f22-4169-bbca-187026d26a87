import multer, { DiskStorageOptions, FileFilterCallback } from "multer";
import { Request, Express } from "express";
import { BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, validExtensions } from "@moxfive-llc/common";

const validMimeTypes = [
    "application/x-x509-ca-cert",
    "application/json",
    "application/pkix-cert",
    "application/xml",
    "application/octet-stream",
    "text/xml",
    "application/x-pem-file",
    "application/x-x509-user-cert",
    "application/rss+xml",
    "application/atom+xml",
    "application/xslt+xml",
    "application/mathml+xml",
    "application/xhtml+xml"
];

export const fileUploadedMulter = (validMimeTypesPassed?: string[], fileSize?: number) => {
    // if (!fs.existsSync("/uploads/")) {
    //     fs.mkdirSync("/uploads/");
    // }

    const diskStorageConfig: DiskStorageOptions = {
        destination: (req: Request, file: Express.Multer.File, callback) => {
            callback(null, `/uploads/`);
        },
        filename: (req: Request, file: Express.Multer.File, callback) => {
            callback(null, `${Date.now()}-${file.originalname}`);
        }
    };

    const storage = multer.diskStorage(diskStorageConfig);
    const upload = multer({
        storage,
        limits: {
            fileSize: fileSize ? fileSize : 5242900 // 5 MB
        },
        fileFilter(req: Request, file: Express.Multer.File, callback: FileFilterCallback) {
            console.log("multer file upload ", validMimeTypes, file.mimetype);
            // Check mimetype is allowed or not
            if (validMimeTypesPassed?.includes(file.mimetype) || validMimeTypes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.ALLOWED_FILE_TYPES, "Invalid file type."
                ));
            }
            // Fetch file extension
            const fileExtension = file.originalname.split(".").pop() ?? "";
            console.log("multer file validExtensions ", validExtensions, fileExtension, (validExtensions[file.mimetype] ?? []).includes(fileExtension.toLowerCase()));
            // Check File extension is allowed or not
            if ((validExtensions[file.mimetype] ?? []).includes(fileExtension.toLowerCase())) {
                callback(null, true);
            }
            else {
                // eslint-disable-next-line no-console
                console.log("error");
                callback(new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.ALLOWED_FILE_TYPES, "Invalid file type."
                ));
            }
        }
    });

    return upload;
};
