export const getReaminingDaysUsingDate = (date: string | Date): number => {
    const today = new Date();
    const targetDate = new Date(date);

    // Set the target date to midnight to avoid time differences affecting the calculation
    targetDate.setUTCHours(0, 0, 0, 0);

    // Calculate the difference in time (milliseconds)
    const timeDifference = targetDate.getTime() - today.getTime();

    // Convert milliseconds to days and return
    const daysLeft = Math.floor(timeDifference / (1000 * 3600 * 24));

    return daysLeft;
};
