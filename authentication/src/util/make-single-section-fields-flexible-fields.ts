import { FlexibleFieldNameKey } from "../interfaces";

export const MakeSingleSectionFieldsFlexibleFields = (path: any) => {
    const fields: string[] = [];
    const flexibleFieldsNameKey: FlexibleFieldNameKey[] = [];

    if(Array.isArray(path)) {
        path.forEach(field => {
            if(!field.ignore) {
                if(field.flexibleField) {
                    flexibleFieldsNameKey.push({
                        name: field.name,
                        key: field.key || "",
                        conditionalKeys: field.conditionalKeys || null
                    });
                }
                fields.push(field.name);
            }
        });
    }

    return {
        fields,
        flexibleFieldsNameKey
    };
};
