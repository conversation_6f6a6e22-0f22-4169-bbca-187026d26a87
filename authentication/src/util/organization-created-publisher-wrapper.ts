import { OrganizationCreatedPublisher } from "../events/publishers/organization-created-publisher";
import { OrganizationEventData } from "../interfaces";
import { OrganizationDoc } from "../models/organization";
import { OrganizationFlexibleField } from "../models/organization-flexible-fields";
import { natsWrapper } from "../nats-wrapper";

export const OrganizationCreatedPublisherWrapper = async (org: OrganizationDoc) => {
    const organization = org.toObject();

    const data: OrganizationEventData = {
        id: String(organization._id),
        name: organization.name,
        version: organization.version,
        isEnabled: organization.isEnabled,
        organizationTypeIds: organization.organizationTypeIds.map(String),
        profile: organization.profile,
        favicon: organization.favicon
    };

    if (organization.hasOwnProperty("officeLocations")) {
        data["officeLocations"] = organization.officeLocations;
    }
    if (organization.hasOwnProperty("website")) {
        data["website"] = organization.website;
    }
    if (organization.hasOwnProperty("numberOfEmployees")) {
        data["numberOfEmployees"] = organization.numberOfEmployees;
    }
    if (organization.hasOwnProperty("numberOfITStaff")) {
        data["numberOfITStaff"] = organization.numberOfITStaff;
    }
    if (organization.hasOwnProperty("itStaffLocation")) {
        data["itStaffLocation"] = organization.itStaffLocation;
    }
    if (organization.hasOwnProperty("highLevelCompanyInformation")) {
        data["highLevelCompanyInformation"] = organization.highLevelCompanyInformation;
    }
    if (organization.hasOwnProperty("descriptionOfEnvironment")) {
        data["descriptionOfEnvironment"] = organization.descriptionOfEnvironment;
    }
    if (organization.hasOwnProperty("serviceLines")) {
        data["serviceLines"] = organization.serviceLines;
    }
    if (organization.hasOwnProperty("activePartner")) {
        data["activePartner"] = organization.activePartner;
    }
    if (organization.hasOwnProperty("partnerEula")) {
        data["partnerEula"] = organization.partnerEula;
    }
    if (organization.hasOwnProperty("partnerTermsConditions")) {
        data["partnerTermsConditions"] = organization.partnerTermsConditions;
    }
    if (organization.hasOwnProperty("shortDescription")) {
        data["shortDescription"] = organization.shortDescription;
    }

    if (organization.hasOwnProperty("industry")) {
        if (organization.industry && organization.industry.length) {
            const industryDetails = await OrganizationFlexibleField.findOne(
                { key: "industry", "values._id": organization.industry[0] },
                { _id: 0, values: { $elemMatch: { _id: organization.industry[0] } } }
            );

            data["industry"] = industryDetails?.values[0].value;
        }
        else {
            data["industry"] = "";
        }
    }

    if (organization.hasOwnProperty("partnerType")) {
        if (organization.partnerType && organization.partnerType.length) {
            const partnerDetails = await OrganizationFlexibleField.findOne(
                { key: "partnerType", "values._id": organization.partnerType[0] },
                { _id: 0, values: { $elemMatch: { _id: organization.partnerType[0] } } }
            );

            data["partnerType"] = partnerDetails?.values[0].value;
        }
        else {
            data["partnerType"] = "";
        }
    }

    await new OrganizationCreatedPublisher(natsWrapper.client).publish(data);
};
