export const MakeSingleSectionFieldsFlexibleFields = (path: any) => {
    const fields: string[] = [];
    const flexibleFieldsNameKey: {
        name: string,
        key: string,
        conditionalKeys?: { [key: string]: string } | null
    }[] = [];
    const organizationFlexibleFieldsNameKey: {
        name: string,
        key: string,
        conditionalKeys?: { [key: string]: string } | null
    }[] = [];
    if(Array.isArray(path)) {
        path.forEach(field => {
            if(!field.ignore) {
                if(field.flexibleField) {
                    if(field.organizationFlexibleField) {
                        organizationFlexibleFieldsNameKey.push({
                            name: field.name,
                            key: field.key || "",
                            conditionalKeys: field.conditionalKeys || null
                        });
                    }
                    else {
                        flexibleFieldsNameKey.push({
                            name: field.name,
                            key: field.key || "",
                            conditionalKeys: field.conditionalKeys || null
                        });
                    }
                }
                fields.push(field.name);
            }
        });
    }

    return {
        fields,
        flexibleFieldsNameKey,
        organizationFlexibleFieldsNameKey
    };
};
