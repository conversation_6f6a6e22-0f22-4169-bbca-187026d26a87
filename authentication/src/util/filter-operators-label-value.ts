import { FilterOperators } from "../enums/filter-operators";

export const filterOperatorsLabelValue = Object.freeze({
    [FilterOperators.EQ]: {
        label: "Equals",
        value: FilterOperators.EQ,
    },
    [FilterOperators.NE]: {
        label: "Does not equal",
        value: FilterOperators.NE,
    },
    [FilterOperators.GE]: {
        label: "Greater or equal",
        value: FilterOperators.GE,
    },
    [FilterOperators.LE]: {
        label: "Less or equal",
        value: FilterOperators.LE,
    },
    [FilterOperators.STARTS_WITH]: {
        label: "Starts With",
        value: FilterOperators.STARTS_WITH,
    },
    [FilterOperators.ENDS_WITH]: {
        label: "Ends With",
        value: FilterOperators.ENDS_WITH,
    },
    [FilterOperators.CONTAINS]: {
        label: "Contains",
        value: FilterOperators.CONTAINS,
    },
    [FilterOperators.IN]: {
        label: "In",
        value: FilterOperators.IN,
    },
    [FilterOperators.NIN]: {
        label: "Not In",
        value: FilterOperators.NIN,
    },
    [FilterOperators.IS_BLANK]: {
        label: "Is Blank",
        value: FilterOperators.IS_BLANK,
    }
});
