import rateLimit, { Op<PERSON>, RateLimitR<PERSON><PERSON><PERSON><PERSON><PERSON>, MemoryStore } from "express-rate-limit";
import { NextFunction, Request, Response } from "express";
import { publishErrorLog } from "@moxfive-llc/common";

export const rateLimiter = (maxRequestsInOneMinute = 600): RateLimitRequestHandler => {
    return rateLimit({
        // Rate limiter configuration
        windowMs: 60 * 1000, // 1 minute
        max: maxRequestsInOneMinute, // Limit each IP to 100 requests per `window` (here, per 15 minutes)
        standardHeaders: false, // Don't return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false, // Disable the `X-RateLimit-*` headers
        store: new MemoryStore(),
        // Handle request in case limit is reached
        handler: async (request: Request, response: Response, next: NextFunction, options: Options) => {
            const respBody = {
                "title": "Too Many Requests",
                "detail": "Rate limit exceeded.",
                "type": "tooManyRequests",
                "code": 4291
            };
            response.status(options.statusCode).send(respBody);

            return await publishErrorLog(request, options.statusCode, options.message, JSON.stringify(respBody));
        }
    });
};
