import { UserAzureDetails } from "../../interfaces";

export const mapUserDetailsV2 = (userDetails: UserAzureDetails) => {
    return {
        email: userDetails.mail,
        displayName: userDetails.displayName,
        firstName: userDetails.givenName,
        lastName: userDetails.surname,
        // userLocation: {
        //     addressline1: userDetails.streetAddress,
        //     addressline2: null,
        //     country: userDetails.country,
        //     countryShortName: null,
        //     state: userDetails.state,
        //     stateShortName: null,
        //     city: userDetails.city,
        //     cityShortName: null,
        //     zip: userDetails.postalCode,
        //     latitude: null,
        //     longitude: null
        // },
        officePhone: userDetails.mobilePhone,
        isEnabled: userDetails.accountEnabled,
        jobTitle: userDetails.jobTitle,
    };
};
