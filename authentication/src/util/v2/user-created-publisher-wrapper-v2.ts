import { UserCreatedPublisher } from "../../events/publishers/user-created-publisher";
import { UserV2Doc } from "../../models/v2/users-v2";
import { natsWrapper } from "../../nats-wrapper";

export const userCreatedPublisherV2Wrapper = async (user: UserV2Doc) => {
    try {
        const data = {
            id: user.id,
            version: user.version,
            displayName: user.displayName,
            email: user.email,
            isEnabled: user.isEnabled,
            firstName: user.firstName,
            lastName: user.lastName,
            name: user.name,
            organizationId: user.organization?.id ?? null,
            keys: user.keys,
            jobTitle: user.jobTitle,
            officePhone: user.officePhone,
            role: user.role?.value ?? null,
            allowedLoginTokens: (user.allowedLoginTokens as string[]) ?? []
        };

        await new UserCreatedPublisher(natsWrapper.client).publish(data);
    }
    catch(error) {
        console.error("Error sending user created NATS Event", error);
    }
};
