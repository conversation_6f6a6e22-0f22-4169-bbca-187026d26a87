import { UserUpdatedPublisher } from "../../events/publishers/user-updated-publisher";
import { UserEventData } from "../../interfaces";
import { UserV2Doc } from "../../models/v2/users-v2";
import { natsWrapper } from "../../nats-wrapper";

export const userUpdatedPublisherV2Wrapper = async (userDetails: UserV2Doc) => {
    try {
        const data: UserEventData = {
            id: userDetails.id,
            version: userDetails.version,
            email: userDetails.email,
            displayName: userDetails.displayName,
            isEnabled: userDetails.isEnabled,
            firstName: userDetails.firstName,
            lastName: userDetails.lastName,
            name: userDetails.name,
            organizationId: userDetails.organization?.id ?? null,
            keys: userDetails.keys,
            jobTitle: userDetails.jobTitle,
            officePhone: userDetails.officePhone,
            role: userDetails.role?.value ?? null,
            allowedLoginTokens: (userDetails.allowedLoginTokens as string[]) ?? []
        };

        // if (userDetails.role) {
        //     const roleDetails = await UserFlexibleField.findOne(
        //         { key: "role", "values._id": userDetails.role },
        //         { _id: 0, values: { $elemMatch: { _id: userDetails.role } } }
        //     );

        //     data["role"] = roleDetails?.values[0].value || null;
        // }

        await new UserUpdatedPublisher(natsWrapper.client).publish(data);
    }
    catch(error) {
        console.error("Error sending user updated NATS Event", error);
    }
};
