/* eslint-disable security/detect-object-injection */
import { BodyInvalidBadRequestError, InvalidResourceIdBadRequestError } from "@moxfive-llc/common";
import { NextFunction, Request, Response } from "express";
import { FlexibleFieldNameKey } from "../../interfaces";
import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
import { sortByPropertyInObject } from "../common";
import { UserFlexibleField } from "../../models/user-flexible-fields";

export const flexibleFieldValidationV2 = (fields: FlexibleFieldNameKey[] = []) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const data: any = {};
            const fieldsExist: string[] = [];
            const valuesMap: Map<string, string> = new Map();
            const singleSelectFieldsSet = new Set();

            // Loop through all fields and req body has that field then add it in data
            fields.forEach(field => {
                if (req.body.hasOwnProperty(field.name)) {
                    const key = field.key;

                    data[field.name] = {
                        ...req.body[field.name],
                        key: key
                    };

                    fieldsExist.push(key);
                }
            });

            // If there is any flexible fields provided
            if (fieldsExist.length) {
                // Fetch flexible fields info
                const organizationFlexibleFields = await OrganizationFlexibleField.find({ key: { $in: fieldsExist } });
                const userFlexibleFields = await UserFlexibleField.find({ key: { $in: fieldsExist } });
                const flexibleFields = [...organizationFlexibleFields, ...userFlexibleFields];

                const invalidIdsError = [];
                const multiSelectError = [];
                const invalidValuesError = [];

                // Loop through all  flexible fields provided in req body
                // eslint-disable-next-line guard-for-in
                for (const field in data) {
                    // Chcek whether povided id is valid ot not
                    const flexibleField = flexibleFields.find(f => data[field].key === f.key && String(f._id) === String(data[field].id));
                    // If invalid Id then push to invalidIds
                    if (!flexibleField) {
                        invalidIdsError.push({
                            name: field,
                            value: data[field].id,
                            message: "These id is invalid.",
                        });
                        continue;
                    }

                    if (!flexibleField.multiChoice) {
                        singleSelectFieldsSet.add(field);
                    }

                    // If only single select is there and multiple values are provided
                    if (!flexibleField.multiChoice && data[field].valueIds && data[field].valueIds.length > 1) {
                        multiSelectError.push({
                            name: field,
                            value: data[field].valueIds,
                            message: "Only one value can be selected for the specified field."
                        });
                        continue;
                    }

                    // Set all values of flexible field in fieldValuesMap
                    const fieldValuesMap: Map<string, string> = new Map();
                    flexibleField.values.forEach(record => fieldValuesMap.set(String(record._id), record.value));

                    // Check provided values are valid or not
                    let valueDocs: { id: string, value: string }[] = [];
                    for (const value of data[field].valueIds) {
                        const valueDoc = fieldValuesMap.get(String(String(value)));
                        flexibleField.values.forEach(v => valuesMap.set(String(v._id), v.value));
                        // eslint-disable-next-line max-depth
                        if (!valueDoc) {
                            invalidValuesError.push({
                                name: field,
                                value: data[field].valueIds,
                                message: "These values are invalid."
                            });
                            continue;
                        }

                        valueDocs.push({
                            id: String(value),
                            value: valueDoc
                        });
                    }

                    valueDocs = valueDocs.sort(sortByPropertyInObject("value"));

                    // Set valueIds in req body
                    if (data[field].valueIds && data[field].valueIds.length && singleSelectFieldsSet.has(field)) {
                        req.body[field] = valueDocs.length ? valueDocs[0] : null;
                    }
                    else if (data[field].valueIds && data[field].valueIds.length) {
                        req.body[field] = valueDocs;
                    }
                    else {
                        req.body[field] = null;
                    }
                }
                if (invalidIdsError.length) {
                    throw new InvalidResourceIdBadRequestError(invalidIdsError);
                }

                if (multiSelectError.length) {
                    throw new BodyInvalidBadRequestError(multiSelectError);
                }

                if (invalidValuesError.length) {
                    throw new InvalidResourceIdBadRequestError(invalidValuesError);
                }

                req.flexibleFieldValuesMap = valuesMap;
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
