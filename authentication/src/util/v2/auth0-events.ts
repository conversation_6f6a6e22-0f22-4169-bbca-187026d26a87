// Single source of truth for Auth0 events
// Maps event codes to their descriptive names and categories
export const AUTH0_EVENTS: Record<string, {
    name: string;
    category: string;
    isFailure: boolean;
}> = {
    // Login related events
    "s": { name: "Success Login", category: "authentication", isFailure: false },
    "f": { name: "Failed Login", category: "authentication", isFailure: true },
    "fp": { name: "Failed Login (Wrong Password)", category: "authentication", isFailure: true },
    "fu": { name: "Failed Login (Invalid Email/Username)", category: "authentication", isFailure: true },
    "seotpft": { name: "Success Login", category: "authentication", isFailure: false },
    "sepft": { name: "Success Login", category: "authentication", isFailure: false },
    "sercft": { name: "Success Login", category: "mfa", isFailure: false },

    // MFA related events
    // "gd_auth_failed": { name: "M<PERSON> Auth failed", category: "mfa", isFailure: true },
    "gd_enrollment_complete": { name: "MFA enrollment complete", category: "mfa", isFailure: false },
    "gd_otp_rate_limit_exceed": { name: "Too many MFA failures", category: "mfa", isFailure: true },
    "gd_recovery_rate_limit_exceed": { name: "Multi-factor recovery code has failed too many times", category: "mfa", isFailure: true },
    "too_many_records": { name: "Max Amount of Authenticators", category: "mfa", isFailure: true },
    "feotpft": { name: "Invalid OTP Challenge", category: "mfa", isFailure: true },
    "fercft": { name: "Invalid Recovery Code", category: "mfa", isFailure: true },

    // Account security events
    "limit_wc": { name: "Blocked Account", category: "security", isFailure: true },
    "limit_mu": { name: "Too Many Calls to /userinfo Endpoint", category: "security", isFailure: true },
    "signup_pwd_leak": { name: "Breached Password on Signup", category: "security", isFailure: true },

    // Account changes
    "sce": { name: "Success Change Email", category: "account_change", isFailure: false },
    "scp": { name: "Success Change Password", category: "account_change", isFailure: false }
};
