/* eslint-disable security/detect-object-injection */
export const processUpdatedDataForAtlasSync = ({
    data,
    section,
    multiSections,
}: {
    data: any;
    section?: string;
    multiSections?: { name: string; data: any }[];
}) => {
    let doc = {};

    if (section) {
        Object.keys(data).forEach((property) => {
            doc = { ...doc, [`${section}.${property}`]: data[property] };
        });
    }
    else if (multiSections && multiSections.length) {
        multiSections.forEach((section) => {
            Object.keys(section.data).forEach((property) => {
                doc = { ...doc, [`${section.name}.${property}`]: section.data[property] };
            });
        });
    }
    else {
        Object.keys(data).forEach((property) => {
            if (property !== "_id" && property !== "__v") {
                doc = { ...doc, [property]: data[property] };
            }
        });
    }

    return doc;
};
