/* eslint-disable no-await-in-loop */
import { fetchSectionFilterFields } from "./fetch-single-section-filter-fields";
import { FilterFields } from "../interfaces";
import { UserFlexibleField } from "../models/user-flexible-fields";
import { sortByPropertyInObject } from "./common";
import { OrganizationFlexibleField } from "../models/organization-flexible-fields";
import { OrganizationType } from "../models/organization-type";
import { Organization } from "../models/organization";
import { UserV2 } from "../models/v2/users-v2";
import { getUsersByIds } from "./get-users-by-ids";
import { AUTH0_EVENTS } from "./v2/auth0-events";
import { ReferenceValueV2 } from "../interfaces/v2/reference-value-v2";

export const fetchModuleFilterFieldsBasedOnPermission = async ({ sections, assignedActions }:
    { sections: { path: any, action?: string }[], assignedActions: Set<string> }) => {
    let fields: FilterFields[] = [];
    sections.forEach(section => {
        if (!section.action || assignedActions.has(section.action)) {
            const sectionFields = fetchSectionFilterFields(section.path);
            fields = fields.concat(sectionFields);
        }
    });

    const keys = fields.map(field => field.key).filter(val => Boolean(val));
    const userFlexibleFieldsMap = new Map();
    const orgFlexibleFieldsMap = new Map();
    if (keys.length) {
        const flexibleFields = await UserFlexibleField.find({ key: { $in: keys } }, { values: 1, key: 1 });
        flexibleFields.forEach(field => {
            userFlexibleFieldsMap.set(field.key, field.values.sort(sortByPropertyInObject("value")));
        });

        const orgFlexibleFields = await OrganizationFlexibleField.find({ key: { $in: keys } }, { values: 1, key: 1 });
        orgFlexibleFields.forEach(field => {
            orgFlexibleFieldsMap.set(field.key, field.values.sort(sortByPropertyInObject("value")));
        });
    }

    const formattedFields: FilterFields[] = [];

    for (const field of fields) {
        let values: { id: string, value: string }[] | null = null;

        // Fetch organization types
        if (field.organizationTypes) {
            const organizationTypes = await OrganizationType.find().select("name").lean().exec();
            values = organizationTypes.map(type => ({
                id: String(type._id),
                value: type.name
            }));
        }
        // Fetch organizations
        else if (field.organizations) {
            const organizations = await Organization.find().select("name").lean().exec();
            values = organizations.map(org => ({
                id: String(org._id),
                value: org.name
            }));
        }
        // Fetch Users
        else if (field.users) {
            const users = await UserV2.find().select("name").lean().exec();
            values = users.map(user => ({
                id: String(user._id),
                value: user.name
            }));
        }
        else if (field.moxfiveUserAssign) {
            const userIds: string[] = [];

            // Fetch org who as either PM, TA or sales sponsor
            const organizations = await Organization.find({
                $or: [
                    { moxfivePMSponsor: { $ne: null } },
                    { moxfiveTASponsor: { $ne: null } },
                    { moxfiveSalesSponsor: { $ne: null } }
                ]
            });

            // Make user Ids
            organizations.forEach(org => {
                if (org.moxfivePMSponsor) {
                    userIds.push(org.moxfivePMSponsor);
                }
                if (org.moxfiveTASponsor) {
                    userIds.push(org.moxfiveTASponsor);
                }
                if (org.moxfiveSalesSponsor) {
                    userIds.push(org.moxfiveSalesSponsor);
                }
            });
            const usersMap = await getUsersByIds(userIds);

            // Prepare values array
            values = [];
            usersMap.forEach((name, id) => {
                values?.push({
                    id,
                    value: name
                });
            });
        }
        // If field is flexible field & it has key then fetch values of it from flexibleFieldsMap
        else if (field.isFlexibleField && field.key) {
            if (field.userFlexibleField) {
                values = userFlexibleFieldsMap.get(field.key) || null;
            }
            else if (field.organizationFlexibleField) {
                values = orgFlexibleFieldsMap.get(field.key) || null;
            }
            else {
                values = null;
            }
        }
        else if (field.authenticationLogsTypes) {
            const nameSet = new Set();
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            values = Object.entries(AUTH0_EVENTS).reduce((acc, [_, value]) => {
                // If name is already in the set, then skip
                if(nameSet.has(value.name)) {
                    return acc;
                }

                // Add to the set
                nameSet.add(value.name);

                // Add to the array
                acc.push({
                    id: value.name,
                    value: value.name,
                });
                return acc;
            }, [] as ReferenceValueV2[]);
        }
        formattedFields.push({
            displayName: field.displayName || field.name,
            name: field.name,
            isFlexibleField: field.isFlexibleField,
            isQuickFilter: field.isQuickFilter,
            isBooleanField: field.isBooleanField,
            values,
            allowedFilters: field.allowedFilters,
            type: field.type
        });
    }

    return formattedFields;
};
