import { body, Validation<PERSON>hain } from "express-validator";
import { isValidMongoObjectId } from "./index";

export const flexibleFieldReqBodyValidation = (fields: {name: string, optional?: boolean, nullable?: boolean, minValuesLength?: number, ifConditions?: any}[] = [],
    validations:ValidationChain[] = []) => {
    fields.forEach(field => {
        const { name, optional = true, nullable = true, minValuesLength = 1, ifConditions = [] } = field;
        let validator: Validation<PERSON>hain;
        validator = body(name);

        ifConditions.forEach((ifCondition: any) => {
            validator = validator.if(ifCondition);
        });

        if(optional) {
            if (nullable) {
                validator = validator.optional({ nullable: true });
            }
            else {
                validator = validator.optional();
            }
        }
        else {
            validator = validator.exists();
        }
        validations.push(validator);

        validations.push(
            body(`${name}.id`)
                .if(body(name).exists())
                .exists().bail()
                .isMongoId().withMessage("This id is invalid."),
        );

        validations.push(
            body(`${name}.valueIds`)
                .if(body(name).exists())
                .exists().bail()
                .isArray({ min: minValuesLength }).withMessage("This value ids are invalid.").bail()
                .custom((valueIds: string[]) => {
                    return valueIds.every(valueId => {
                        return isValidMongoObjectId(valueId);
                    });
                }).withMessage("This value ids are invalid.")
        );
    });
    return validations;
};
