import { UserUpdatedPublisher } from "../events/publishers/user-updated-publisher";
import { UserEventData } from "../interfaces";
import { UserDoc } from "../models/user";
import { UserFlexibleField } from "../models/user-flexible-fields";
import { natsWrapper } from "../nats-wrapper";

export const userUpdatedPublisherWrapper = async (userDetails: UserDoc) => {
    const data: UserEventData = {
        id: userDetails.id,
        version: userDetails.version,
        email: userDetails.email,
        displayName: userDetails.displayName,
        isEnabled: userDetails.isEnabled,
        firstName: userDetails.firstName,
        lastName: userDetails.lastName,
        name: userDetails.name,
        organizationId: userDetails.organizationId,
        keys: userDetails.keys,
        jobTitle: userDetails.jobTitle,
        officePhone: userDetails.officePhone,
        role: null,
        allowedLoginTokens: (userDetails.allowedLoginTokens as string[]) ?? []
    };

    if (userDetails.role) {
        const roleDetails = await UserFlexibleField.findOne(
            { key: "role", "values._id": userDetails.role },
            { _id: 0, values: { $elemMatch: { _id: userDetails.role } } }
        );

        data["role"] = roleDetails?.values[0].value || null;
    }

    await new UserUpdatedPublisher(natsWrapper.client).publish(data);
};
