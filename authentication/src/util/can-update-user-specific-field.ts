import { InsufficientPrivilagesError, InvalidResourceIdBadRequestError, NotFoundCode, ResourceNotFoundError } from "@moxfive-llc/common";
import { Organization } from "../models/organization";
import { User } from "../models/user";

export const canUpdateUserSpecificField = async (organizationId: string, userId: string) => {
    // Fetch org details of logged in user
    const organization = await Organization.findById(organizationId);
    if (!organization) {
        throw new InvalidResourceIdBadRequestError([{ name: "organizationId", value: organizationId, message: "Organization not found" }]);
    }

    // Fetch user details of logged in user
    const user = await User.findById(userId);
    if (!user) {
        throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
    }

    // Check whether user is member & not owner and if it is then throw error
    // if (organization.member.includes(user.azureId) || !organization.owner.includes(user.azureId)) {
    //     throw new InsufficientPrivilagesError();
    // }
    if(user.organizationId !== organizationId) {
        throw new InsufficientPrivilagesError();
    }

    return true;
};
