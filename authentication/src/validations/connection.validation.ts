import { BodyInvalidBadRequestError, ExpressValidator<PERSON>rapper, InternalServerError } from "@moxfive-llc/common";
import { ValidationChain, validationResult } from "express-validator";
import { MetaDataConnectionFields } from "../models/meta-data-connection-fields";
import { NextFunction, Request, Response } from "express";
import { validateIpAddresses } from "../util/common-custom-validators";
import { isValidMongoObjectId } from "../util";

// Define the shape of our validation rule object
interface ValidationRule {
    customValidators?: any[];
    name: string;
    mandatory?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    regex?: string;
    nullable?: boolean;
    message: string;
}

export const validateFieldsMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    const validationChain = await connectionsValidation(req);
    for (const validation of validationChain) {
        // eslint-disable-next-line no-await-in-loop
        await validation.run(req);
    }
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
    }
    else {
        next();
    }
};

// export const validateFieldsMiddlewareForUpdate = async (req: Request, res: Response, next: NextFunction) => {
//     const update = true;
//     const validationChain = await connectionsValidation(req, update);
//     for (const validation of validationChain) {
//         await validation.run(req);
//     }
//     const errors = validationResult(req);
//     if (!errors.isEmpty()) {
//         res.status(400).json({ errors: errors.array() });
//     } else {
//         next();
//     }
// }

const connectionsValidation = async (
    req: Request
) => {
    const validationChain: ValidationChain[] = [];

    const { connectionTypeId, data } = req.body;

    if(connectionTypeId && !isValidMongoObjectId(connectionTypeId)) {
        throw new BodyInvalidBadRequestError([
            { name: "connectionTypeId", value: connectionTypeId, message: `Connection Type Id must be valid` }
        ]);
    }

    const connectionFields = await MetaDataConnectionFields.find({ "connectionType.id": String(connectionTypeId) }).lean().exec();
    if (!connectionFields.length) {
        throw new InternalServerError();
    }

    connectionFields.forEach((field) => {
        // Case: Check if condition exists then check if that perticular field has the condition value if not then no validation should be added if condition is null then continue
        if (!field.conditions || (field.conditionOperator === "and" && field.conditions.length && field.conditions.every(condition => data[condition.field] === condition.value))
            || (field.conditionOperator === "or" && field.conditions.length && field.conditions.some(condition => data[condition.field] === condition.value))) {
            const validation: ValidationRule = {
                name: `data.${field.name}`,
                mandatory: field.required,
                message: `${field.displayName} must be valid.`
            };

            if (field.validations) {
                field.validations.forEach((validationField: { type: string, value: number | string }) => {
                    if (validationField.type === "minLength" && typeof (validationField.value) === "number") {
                        validation.minLength = validationField.value;
                    }
                    if (validationField.type === "maxLength" && typeof (validationField.value) === "number") {
                        validation.maxLength = validationField.value;
                    }
                    if (validationField.type === "min" && typeof (validationField.value) === "number") {
                        validation.min = validationField.value;
                    }
                    if (validationField.type === "max" && typeof (validationField.value) === "number") {
                        validation.max = validationField.value;
                    }
                    if (validationField.type === "nullable" && typeof (validationField.value) === "boolean") {
                        validation.nullable = validationField.value;
                    }
                    if (validationField.type === "regex" && typeof (validationField.value) === "string") {
                        validation.regex = validationField.value;
                    }
                });
            }
            const fieldType = field.type.value.toLowerCase();

            switch (fieldType) {
                case "url":
                    // If regex exists then check if its value is regex approved or not
                    if (validation.regex) {
                        validation.customValidators = [(inputString: string): boolean => {
                            if (validation.regex) {
                                // eslint-disable-next-line security/detect-non-literal-regexp
                                const regex = new RegExp(validation.regex);
                                return regex.test(inputString);
                            }
                            else {
                                return true;
                            }
                        }];
                    }
                    if (validation.maxLength) {
                        validation.message = `${field.displayName} must be a valid URL and can be of max ${validation.maxLength} characters long.`;
                    }
                    else {
                        validation.message = `${field.displayName} must be a valid URL.`;
                    }
                    ExpressValidatorWrapper.urlValidator([validation], validationChain);
                    break;
                case "checkbox":
                case "radio":
                    // eslint-disable-next-line no-case-declarations
                    const optionValues = field.values.map(value => value.id);

                    // Adding custom validators for array of ip addresses
                    validation.customValidators = [(value: string): boolean => {
                        return optionValues.includes(value);
                    }];
                    validation.message = `${field.displayName} must be a valid value.`;
                    ExpressValidatorWrapper.fieldExistsValidator([validation], validationChain);
                    break;
                case "boolean":
                case "switch":
                    validation.message = `${field.displayName} must be boolean.`;
                    ExpressValidatorWrapper.booleanValidator([validation], validationChain);
                    break;
                case "number":
                    if (validation.min || validation.max) {
                        if (validation.min && !validation.max) {
                            validation.message = `${field.displayName} must be a positive number and minimum ${validation.min}.`;
                        }
                        else if (validation.max && !validation.min) {
                            validation.message = `${field.displayName} must be a positive number and maximum ${validation.max}.`;
                        }
                        else {
                            validation.message = `${field.displayName} must be a positive number and between ${validation.min} to ${validation.max}.`;
                        }
                    }
                    else {
                        validation.message = `${field.displayName} must be a positive number.`;
                    }
                    ExpressValidatorWrapper.numberValidator([validation], validationChain);
                    break;
                case "ipranges":
                    // Adding custom validators for array of ip addresses
                    validation.customValidators = [validateIpAddresses];
                    validation.message = `${field.displayName} must be valid ip addresses.`;
                    ExpressValidatorWrapper.arrayValidator([validation], validationChain);
                    break;
                case "file":
                    validation.message = `${field.displayName} must be valid mongo ID.`;
                    ExpressValidatorWrapper.mongoIDValidator([validation], validationChain);
                    break;
                case "date":
                    validation.message = `${field.displayName} must be valid date.`;
                    ExpressValidatorWrapper.dateValidator([validation], validationChain);
                    break;
                case "stringarray":
                    validation.customValidators = [(stringArray: string[]): boolean => {
                        if (validation.regex) {
                            // eslint-disable-next-line security/detect-non-literal-regexp
                            const regex = new RegExp(validation.regex);
                            return stringArray.every(str => typeof str === "string" && str.trim().length > 0 && regex.test(str));
                        }
                        else {
                            return stringArray.every(str => typeof str === "string" && str.trim().length > 0);
                        }
                    }];
                    validation.message = `${field.displayName} must be valid array.`;
                    ExpressValidatorWrapper.arrayValidator([validation], validationChain);
                    break;
                case "dropdown":
                    // eslint-disable-next-line no-case-declarations
                    const options = field.values.map(value => value.id);

                    // Adding custom validators for array of ip addresses
                    validation.customValidators = [(value: string): boolean => {
                        return options.includes(value);
                    }];
                    if (validation.minLength || validation.maxLength) {
                        if (validation.minLength && !validation.maxLength) {
                            validation.message = `${field.displayName} must be string and of minimum ${validation.minLength} characters long.`;
                        }
                        else if (validation.maxLength && !validation.minLength) {
                            validation.message = `${field.displayName} must be string and of maximum ${validation.maxLength} characters long.`;
                        }
                        else {
                            validation.message = `${field.displayName} must be string and between ${validation.minLength} to ${validation.maxLength} characters long.`;
                        }
                    }
                    else {
                        validation.message = `${field.displayName} must be string.`;
                    }
                    ExpressValidatorWrapper.stringValidator([validation], validationChain);
                    break;
                default:
                    if (validation.minLength || validation.maxLength) {
                        if (validation.minLength && !validation.maxLength) {
                            validation.message = `${field.displayName} must be string and of minimum ${validation.minLength} characters long.`;
                        }
                        else if (validation.maxLength && !validation.minLength) {
                            validation.message = `${field.displayName} must be string and of maximum ${validation.maxLength} characters long.`;
                        }
                        else {
                            validation.message = `${field.displayName} must be string and between ${validation.minLength} to ${validation.maxLength} characters long.`;
                        }
                    }
                    else {
                        validation.message = `${field.displayName} must be string.`;
                    }
                    ExpressValidatorWrapper.stringValidator([validation], validationChain);
            }
        }
    });

    return validationChain;
};
