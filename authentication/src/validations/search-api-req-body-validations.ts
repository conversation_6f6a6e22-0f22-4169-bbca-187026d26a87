import { ExpressValidatorWrapper } from "@moxfive-llc/common";

export const SearchAPIRequestBodyValidation = [
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "search",
            query: true,
            minLength: 1,
            message: "Search term must not be blank."
        }
    ]),

    ...ExpressValidatorWrapper.numberValidator([
        {
            name: "page",
            query: true,
            min: 1,
            message: "Page parameter must be a positive number."
        },
        {
            name: "limit",
            query: true,
            min: 1,
            max: 999,
            message: "Limit parameter must be a positive number and between 1 to 999."
        }
    ])
];
