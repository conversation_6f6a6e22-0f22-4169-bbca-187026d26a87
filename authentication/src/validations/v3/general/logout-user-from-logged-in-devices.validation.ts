import { ExpressValidatorWrapper } from "@moxfive-llc/common";
import { everyElementMongoId } from "../../../util/common-custom-validators";
import { Meta } from "express-validator";

export const LogoutUserFromLoggedinDevicesValidation = [
    ...ExpressValidatorWrapper.arrayValidator([
        {
            name: "devices",
            // If Body does not have signOutFromAllDevices then devices is mandatory
            ifConditions: [(_: any, meta: Meta) => {
                return !meta.req.body.hasOwnProperty("signOutFromAllDevices");
            }],
            mandatory: true,
            customValidators: [everyElementMongoId],
            minLength: 1,
            maxLength: 500,
            message: "Devices must be an array of valid mongo Ids."
        }
    ]),
    ...ExpressValidatorWrapper.booleanValidator([
        {
            name: "signOutFromAllDevices",
            nullable: true,
            message: "SignOutFromAllDevices must be a boolean."
        }
    ])
];
