import { ExpressValidatorWrapper } from "@moxfive-llc/common";
import { MFATypesEnum } from "../../../enums/mfa-types";
import { Meta } from "express-validator";

export const VerifyMFAValidation = [
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "mfaToken",
            mandatory: true,
            minLength: 1,
            message: "mfaToken must be string."
        },
        {
            name: "type",
            mandatory: true,
            minLength: 1,
            message: `Type must be string and one of the ${Object.keys(MFATypesEnum)}.`,
            customValidators: [(value: keyof typeof MFATypesEnum) => {
                // eslint-disable-next-line security/detect-object-injection
                if (!MFATypesEnum[value]) {
                    throw new Error("");
                }
                return true;
            }]
        },
        {
            name: "otp",
            ifConditions: [
                (_: any, meta: Meta) => {
                    return meta.req.body.type === "otp";
                }
            ],
            mandatory: true,
            minLength: 6,
            maxLength: 6,
            message: "OTP must be number and of 6 digits."
        },
        {
            name: "recoveryCode",
            ifConditions: [
                (_: any, meta: Meta) => {
                    return meta.req.body.type === "recovery-code";
                }
            ],
            mandatory: true,
            minLength: 1,
            message: "Recovery Code must be string."
        },
    ])
];

