import { ExpressValidatorWrapper } from "@moxfive-llc/common";
import { MFATypesEnum } from "../../../enums/mfa-types";

export const AssociateMFAEnrollmentValidation = [
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "mfaToken",
            mandatory: true,
            minLength: 1,
            message: "mfaToken must be string."
        },
        {
            name: "type",
            mandatory: true,
            minLength: 1,
            message: `Type must be string and one of the ${Object.keys(MFATypesEnum).filter(type => type !== "recovery-code")}.`,
            customValidators: [(value: keyof typeof MFATypesEnum) => {
                // eslint-disable-next-line security/detect-object-injection
                if(!MFATypesEnum[value] || value === "recovery-code") {
                    return false;
                }
                return true;
            }]
        }
    ])
];
