import { ExpressValidatorWrapper } from "@moxfive-llc/common";

export const VerifyCodeValidation = [
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "code",
            mandatory: true,
            minLength: 1,
            message: "Authorization code must be a string."
        },
        {
            name: "codeVerifier",
            nullable: true,
            minLength: 43,
            maxLength: 43,
            message: "Code Verifier must be string and of 43 characters long."
        }
    ])
];
