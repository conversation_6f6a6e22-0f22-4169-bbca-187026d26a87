import { ExpressValidatorWrapper } from "@moxfive-llc/common";

export const VerifyUserEmailCodeValidation = [
    ...ExpressValidatorWrapper.emailValidator([
        {
            name: "email",
            mandatory: true,
            message: "Email must be a valid email."
        }
    ]),
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "code",
            mandatory: true,
            minLength: 6,
            maxLength: 6,
            message: "Code must be number and of 6 digits."
        },
    ])
];
