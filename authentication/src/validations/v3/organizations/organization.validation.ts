import { ExpressValidatorWrapper } from "@moxfive-llc/common";
import { body, ValidationChain } from "express-validator";
import { OrganizationFields } from "../../../services/organization-fields";
import { flexibleFieldReqBodyValidation } from "../../../util/incident-flexible-field-req-body-validation";
import { authenticationFields } from "../../../util/authentication-fields";

export const organizationValidation = (update: Partial<boolean> | boolean = false) => {
    const validation: ValidationChain[] = [];

    // Company Information
    ExpressValidatorWrapper.stringValidator([
        {
            name: "name",
            ifConditions: [OrganizationFields.fieldValidator],
            mandatory: update ? false : true,
            minLength: 1,
            maxLength: 100,
            message: "Name can only contain letters, dashes, numbers, spaces, dot and can be of max 100 characters long."
        },
        {
            name: "highLevelCompanyInformation",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            maxLength: 2000,
            message: "High level company information can be of max 2000 characters long."
        },
        {
            name: "descriptionOfEnvironment",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            maxLength: 2000,
            message: "Description of environment can be of max 2000 characters long."
        }
    ], validation);

    ExpressValidatorWrapper.urlValidator([
        {
            name: "website",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            minLength: 1,
            maxLength: 250,
            message: "Website must be a valid URL and can be of max 250 characters long."
        }
    ], validation);

    // Office Locations
    ExpressValidatorWrapper.arrayValidator([
        {
            name: "officeLocations",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "Office locations must be array"
        }
    ], validation);

    ExpressValidatorWrapper.stringValidator([
        {
            name: "officeLocations.*.addressline1",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 128,
            message: "Office Locations addressline1 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."
        },
        {
            name: "officeLocations.*.addressline2",
            ifConditions: [body("officeLocations").exists()],
            nullable: true,
            minLength: 1,
            maxLength: 128,
            message: "Office Locations addressline2 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."
        },
        {
            name: "officeLocations.*.city",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Office Locations city can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "officeLocations.*.cityShortName",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Office Locations city short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "officeLocations.*.state",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Office Locations state can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "officeLocations.*.stateShortName",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Office Locations state short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "officeLocations.*.country",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Office Locations country can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "officeLocations.*.countryShortName",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Office Locations country short name can only contain letters and can be of max 64 characters long."
        }
    ], validation);

    ExpressValidatorWrapper.usZipValidator([
        {
            name: "officeLocations.*.zip",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            message: "Office Locations zip must be valid US zip."
        }
    ], validation);

    ExpressValidatorWrapper.floatValidator([
        {
            name: "officeLocations.*.latitude",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            min: -90,
            max: 90,
            lengthAfterDecimal: 15,
            message: "Office Locations latitude should be valid and range between -90 to 90 degrees with maximum precision of 15."
        },
        {
            name: "officeLocations.*.longitude",
            ifConditions: [body("officeLocations").exists()],
            mandatory: true,
            min: -180,
            max: 180,
            lengthAfterDecimal: 15,
            message: "Office Locations longitude should be valid and range between -180 to 180 degrees with maximum precision of 15."
        }
    ], validation);

    // Employee/IT Staff
    ExpressValidatorWrapper.numberValidator([
        {
            name: "numberOfEmployees",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of employees must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfITStaff",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            min: 0,
            maxLength: 16,
            message: "Number of It Staff must be positive number and of max 16 digits long."
        }
    ], validation);

    // IT Staff Locations
    ExpressValidatorWrapper.arrayValidator([
        {
            name: "itStaffLocation",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "IT Staff Locations must be array."
        }
    ], validation);

    ExpressValidatorWrapper.stringValidator([
        {
            name: "itStaffLocation.*.addressline1",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 128,
            message: "IT Staff Locations addressline1 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."
        },
        {
            name: "itStaffLocation.*.addressline2",
            ifConditions: [body("itStaffLocation").exists()],
            nullable: true,
            minLength: 1,
            maxLength: 128,
            message: "IT Staff Locations addressline2 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."
        },
        {
            name: "itStaffLocation.*.city",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "IT Staff Locations city can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "itStaffLocation.*.cityShortName",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "IT Staff Locations city short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "itStaffLocation.*.state",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "IT Staff Locations state can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "itStaffLocation.*.stateShortName",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "IT Staff Locations state short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "itStaffLocation.*.country",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "IT Staff Locations country can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "itStaffLocation.*.countryShortName",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "IT Staff Locations country short name can only contain letters and can be of max 64 characters long."
        }
    ], validation);

    ExpressValidatorWrapper.usZipValidator([
        {
            name: "itStaffLocation.*.zip",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            message: "IT Staff Locations zip must be valid US zip."
        }
    ], validation);

    ExpressValidatorWrapper.floatValidator([
        {
            name: "itStaffLocation.*.latitude",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            min: -90,
            max: 90,
            lengthAfterDecimal: 15,
            message: "IT Staff Locations latitude should be valid and range between -90 to 90 degrees with maximum precision of 15."
        },
        {
            name: "itStaffLocation.*.longitude",
            ifConditions: [body("itStaffLocation").exists()],
            mandatory: true,
            min: -180,
            max: 180,
            lengthAfterDecimal: 15,
            message: "IT Staff Locations longitude should be valid and range between -180 to 180 degrees with maximum precision of 15."
        }
    ], validation);

    // Hotline and Partner Status
    ExpressValidatorWrapper.booleanValidator([
        {
            name: "activePartner",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "Active Partner must be boolean."
        },
        {
            name: "moxfiveHotline",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "MOXFIVE Hotline must be boolean."
        }
    ], validation);

    // Hotline Contact
    ExpressValidatorWrapper.emailValidator([
        {
            name: "hotlineEmail",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "MOXFIVE Hotline Email must be valid."
        }
    ], validation);

    ExpressValidatorWrapper.usNumberValidator([
        {
            name: "hotlinePhoneNumber",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "MOXFIVE Hotline Phone Number must be valid US number."
        }
    ], validation);

    // Billing Contact Details
    ExpressValidatorWrapper.dateValidator([
        {
            name: "msaSignatureDate",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "MSA signature date must be valid ISO-8601 date."
        }
    ], validation);

    ExpressValidatorWrapper.stringValidator([
        {
            name: "billingContactName",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            maxLength: 50,
            message: "Billing Contact Name can only contain letters, dashes, numbers, spaces, dot, and can be of max 50 characters long."
        }
    ], validation);

    ExpressValidatorWrapper.emailValidator([
        {
            name: "billingContactEmail",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "Billing Contact Email must be valid."
        }
    ], validation);

    ExpressValidatorWrapper.usNumberValidator([
        {
            name: "billingContactPhone",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "Billing contact phone must be valid US number"
        }
    ], validation);

    // Billing Addresses
    ExpressValidatorWrapper.arrayValidator([
        {
            name: "billingAddresses",
            ifConditions: [OrganizationFields.fieldValidator],
            nullable: true,
            message: "Billing addresses must be array"
        }
    ], validation);

    ExpressValidatorWrapper.stringValidator([
        {
            name: "billingAddresses.*.addressline1",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 128,
            message: "Billing addressline1 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."
        },
        {
            name: "billingAddresses.*.addressline2",
            ifConditions: [body("billingAddresses").exists()],
            nullable: true,
            minLength: 1,
            maxLength: 128,
            message: "Billing addressline2 can only contain letters, dashes, numbers, spaces, dot, and can be of max 128 characters long."
        },
        {
            name: "billingAddresses.*.city",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Billing city can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "billingAddresses.*.cityShortName",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Billing city short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "billingAddresses.*.state",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Billing state can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "billingAddresses.*.stateShortName",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Billing state short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "billingAddresses.*.country",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Billing country can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "billingAddresses.*.countryShortName",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "Billing country short name can only contain letters and can be of max 64 characters long."
        }
    ], validation);

    ExpressValidatorWrapper.usZipValidator([
        {
            name: "billingAddresses.*.zip",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            message: "Billing zip must be valid US zip."
        }
    ], validation);

    ExpressValidatorWrapper.floatValidator([
        {
            name: "billingAddresses.*.latitude",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            min: -90,
            max: 90,
            lengthAfterDecimal: 15,
            message: "Billing Addresses latitude should be valid and range between -90 to 90 degrees with maximum precision of 15."
        },
        {
            name: "billingAddresses.*.longitude",
            ifConditions: [body("billingAddresses").exists()],
            mandatory: true,
            min: -180,
            max: 180,
            lengthAfterDecimal: 15,
            message: "Billing Addresses longitude should be valid and range between -180 to 180 degrees with maximum precision of 15."
        }
    ], validation);

    // Partner Details
    ExpressValidatorWrapper.dateValidator([
        {
            name: "onboardedDate",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            message: "Onboarded date must be valid ISO-8601 date."
        }
    ], validation);

    ExpressValidatorWrapper.urlValidator([
        {
            name: "partnerEula",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            minLength: 1,
            maxLength: 1000,
            message: "Partner EULA must be valid URL."
        },
        {
            name: "partnerTermsConditions",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            minLength: 1,
            maxLength: 250,
            message: "Partner terms & conditions must be valid URL."
        }
    ], validation);

    ExpressValidatorWrapper.emailValidator([
        {
            name: "inboundRequestInfo",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            message: "Inbound request info must be valid email."
        }
    ], validation);

    ExpressValidatorWrapper.numberValidator([
        {
            name: "numberOfPMs",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            min: 1,
            maxLength: 16,
            message: "Number of PMs must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfLeads",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            min: 1,
            maxLength: 16,
            message: "Number of Leads must be positive number and of max 16 digits long."
        },
        {
            name: "numberOfEngineers",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            min: 1,
            maxLength: 16,
            message: "Number of Engineers must be positive number and of max 16 digits long."
        }
    ], validation);

    ExpressValidatorWrapper.mongoIDValidator([
        {
            name: "moxfivePMSponsor",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            message: "MOXFIVE PM Sponsor must be valid MOXFIVE user."
        },
        {
            name: "moxfiveTASponsor",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            message: "MOXFIVE TA Sponsor must be valid MOXFIVE user."
        },
        {
            name: "moxfiveSalesSponsor",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            message: "MOXFIVE Sales Sponsor must be valid MOXFIVE user."
        }
    ], validation);

    ExpressValidatorWrapper.stringValidator([
        {
            name: "shortDescription",
            ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
            nullable: true,
            maxLength: 2000,
            message: "Short Description can be of max 2000 characters long."
        }
    ], validation);

    // Flexible Fields
    validation.push(
        ...flexibleFieldReqBodyValidation(
            authenticationFields.organizations
                .filter((f) => f.flexibleField && !f.partnerField)
                .map((f) => {
                    return {
                        name: f.name,
                        minValuesLength: f.required ? 1 : 0,
                        ifConditions: [OrganizationFields.fieldValidator],
                        nullable: true
                    };
                })
        )
    );

    validation.push(
        ...flexibleFieldReqBodyValidation(
            authenticationFields.organizations
                .filter((f) => f.flexibleField && f.partnerField)
                .map((f) => {
                    return {
                        name: f.name,
                        minValuesLength: f.required ? 1 : 0,
                        ifConditions: [OrganizationFields.fieldValidator, OrganizationFields.canSendPartnerDetailsField],
                        nullable: true
                    };
                })
        )
    );

    return validation;
};
