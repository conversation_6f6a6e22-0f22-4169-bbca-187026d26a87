import { ExpressValidatorWrapper, isValidMongoObjectId } from "@moxfive-llc/common";
import { body } from "express-validator";

export const addOrganizationMemberBodyValidation = [
    ...ExpressValidatorWrapper.emailValidator([
        {
            name: "email",
            mandatory: true,
            minLength: 1,
            maxLength: 256,
            message: "Email must be valid email and of max 256 characters long."
        }
    ]),

    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "displayName",
            ifConditions: [body("userLocation").exists()],
            mandatory: true,
            minLength: 1,
            maxLength: 256,
            message: "Display name must be string and of max 256 characters long."
        },
        {
            name: "firstName",
            nullable: true,
            minLength: 1,
            maxLength: 64,
            message: "First name must be string."
        },
        {
            name: "lastName",
            nullable: true,
            minLength: 1,
            maxLength: 64,
            message: "Last name must be string."
        },
        {
            name: "userLocation.addressline1",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 512,
            message: "Address line 1 must be string."
        },
        {
            name: "userLocation.addressline2",
            nullable: true,
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 512,
            message: "Address line 2 must be string."
        },
        {
            name: "userLocation.country",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 64,
            message: "Country must be string."
        },
        {
            name: "userLocation.countryShortName",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 64,
            message: "Country short name must be string."
        },
        {
            name: "userLocation.state",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 64,
            message: "State must be string.",
        },
        {
            name: "userLocation.stateShortName",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 64,
            message: "State short name must be string."
        },
        {
            name: "userLocation.city",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 64,
            message: "City must be string."
        },
        {
            name: "userLocation.cityShortName",
            ifConditions: [body("userLocation").exists()],
            minLength: 1,
            maxLength: 64,
            message: "City short name must be string."
        },
        // Todo: ask what is where no.
        {
            name: "jobTitle",
            nullable: true,
            message: "Job Title must be string."
        },
    ]),

    ...ExpressValidatorWrapper.usZipValidator([
        {
            name: "userLocation.zip",
            ifConditions: [body("userLocation").exists()],
            message: "Zip must be valid US zip."
        }
    ]),

    ...ExpressValidatorWrapper.usNumberValidator([
        {
            name: "officePhone",
            nullable: true,
            message: "Phone number must be valid US number."
        }
    ]),

    ...ExpressValidatorWrapper.mongoIDValidator([
        {
            name: "role.id",
            ifConditions: [body("role").exists()],
            message: "Role not found."
        },
        {
            name: "role.valueIds",
            ifConditions: [body("role").exists()],
            customValidators: [(valueIds: string[]) => {
                return valueIds.every(valueId => {
                    return isValidMongoObjectId(valueId);
                });
            }],
            max: 1,
            message: "Role values must be valid array with max 1 element."
        }
    ]),
];
