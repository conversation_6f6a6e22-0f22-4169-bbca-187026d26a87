import { ExpressValidatorWrapper } from "@moxfive-llc/common";

export const connectionTypeAndOrganizationBodyValidation = [
    ...ExpressValidatorWrapper.mongoIDValidator([
        {
            name: "connectionTypeId",
            mandatory: true,
            message: "Connection type id  must be a valid mongo id."
        },
        {
            name: "organizationId",
            mandatory: true,
            message: "Organization Id must be a valid mongo id."
        }
    ])
];
