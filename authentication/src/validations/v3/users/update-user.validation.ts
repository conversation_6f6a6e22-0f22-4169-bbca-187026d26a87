import { ExpressValidatorWrapper } from "@moxfive-llc/common";
import { body } from "express-validator";

const isUserLocationExists = (value: any, { req }: { req: any }) => !!req.body.userLocation;

export const updateUserValidation = [
    // Basic String Fields
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "firstName",
            minLength: 1,
            maxLength: 64,
            message: "First Name must be string and of max 64 characters long."
        },
        {
            name: "lastName",
            minLength: 1,
            maxLength: 64,
            message: "Last Name must be string and of max 64 characters long."
        },
        {
            name: "displayName",
            minLength: 1,
            maxLength: 128,
            message: "Display Name must be string and of max 128 characters long."
        },
        {
            name: "jobTitle",
            nullable: true,
            minLength: 1,
            maxLength: 64,
            message: "Job Title must be string and can be of max 64 characters long."
        }
    ]),

    // User Location Object (using raw body validator)
    body("userLocation")
        .optional({ nullable: true })
        .isObject().withMessage("User location must be an object."),

    // User Location Nested Fields
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "userLocation.addressline1",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 512,
            message: "User Locations addressline1 must be string and can be of max 512 characters long."
        },
        {
            name: "userLocation.addressline2",
            ifConditions: [isUserLocationExists],
            nullable: true,
            minLength: 1,
            maxLength: 512,
            message: "User Locations addressline2 must be string and can be of max 512 characters long."
        },
        {
            name: "userLocation.country",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "User Location country can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "userLocation.countryShortName",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "User Locations country short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "userLocation.state",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "User Locations state can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "userLocation.stateShortName",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "User Locations state short name can only contain letters and can be of max 64 characters long."
        },
        {
            name: "userLocation.city",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "User Locations city can only contain letters, spaces, and can be of max 64 characters long."
        },
        {
            name: "userLocation.cityShortName",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            minLength: 1,
            maxLength: 64,
            message: "User Locations city short name can only contain letters and can be of max 64 characters long."
        }
    ]),
    ...ExpressValidatorWrapper.usZipValidator([
        {
            name: "userLocation.zip",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            message: "Postal Code must be valid US zip."
        }
    ]),
    ...ExpressValidatorWrapper.floatValidator([
        {
            name: "userLocation.latitude",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            min: -90,
            max: 90,
            lengthAfterDecimal: 15,
            message: "User Locations latitude should be valid and range between -90 to 90 degrees with maximum precision of 15."
        },
        {
            name: "userLocation.longitude",
            ifConditions: [isUserLocationExists],
            mandatory: true,
            min: -180,
            max: 180,
            lengthAfterDecimal: 15,
            message: "User Locations longitude should be valid and range between -180 to 180 degrees with maximum precision of 15."
        }
    ]),

    // Office Phone
    ...ExpressValidatorWrapper.usNumberValidator([
        {
            name: "officePhone",
            nullable: true,
            message: "Office Phone must be valid US number."
        }
    ]),

    // Role Fields
    ...ExpressValidatorWrapper.mongoIDValidator([
        {
            name: "role.id",
            ifConditions: [body("role").exists()],
            mandatory: true,
            message: "Role not found."
        }
    ]),
    ...ExpressValidatorWrapper.arrayValidator([
        {
            name: "role.valueIds",
            ifConditions: [body("role").exists()],
            mandatory: true,
            maxLength: 1,
            message: "Role values must be valid array with max 1 element."
        }
    ]),
    ...ExpressValidatorWrapper.mongoIDValidator([
        {
            name: "role.valueIds.*",
            ifConditions: [body("role").exists()],
            mandatory: true,
            message: "Role values must contain valid MongoDB IDs."
        }
    ])
];
