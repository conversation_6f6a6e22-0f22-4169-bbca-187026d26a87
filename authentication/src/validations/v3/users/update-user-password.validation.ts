import { ExpressValidatorWrapper } from "@moxfive-llc/common";

export const UpdateUserPasswordValidation = [
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "token",
            mandatory: true,
            minLength: 1,
            maxLength: 500,
            message: "Token must be string and of max 500 characters long."
        },
        {
            name: "password",
            mandatory: true,
            minLength: 1,
            maxLength: 500,
            message: "Password must be string and of max 500 characters long."
        }
    ]),
];
