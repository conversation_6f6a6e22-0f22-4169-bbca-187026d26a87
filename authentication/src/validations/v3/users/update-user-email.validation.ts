import { ExpressValidatorWrapper } from "@moxfive-llc/common";

export const UpdateUserEmailValidation = [
    ...ExpressValidatorWrapper.stringValidator([
        {
            name: "token",
            nullable: true,
            minLength: 1,
            maxLength: 500,
            message: "Token must be string and of max 500 characters long."
        }
    ]),
    ...ExpressValidatorWrapper.emailValidator([
        {
            name: "email",
            mandatory: true,
            message: "email must be valid email."
        }
    ]),
];
