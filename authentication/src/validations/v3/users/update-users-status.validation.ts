import { ExpressValidatorWrapper } from "@moxfive-llc/common";
import { body } from "express-validator";

export const UpdateUsersStatusValidation = [
    ...ExpressValidatorWrapper.arrayValidator([
        {
            name: "users",
            mandatory: true,
            minLength: 1,
            maxLength: 20,
            message: "At a given time max 20 users status can be updated"
        }
    ]),
    ...ExpressValidatorWrapper.mongoIDValidator([
        {
            name: "users.*",
            ifConditions: [body("users").exists()],
            mandatory: true,
            message: "These users ids are invalid"
        }
    ]),
    ...ExpressValidatorWrapper.booleanValidator([
        {
            name: "isEnabled",
            mandatory: true,
            message: "isEnabled must be boolean"
        }
    ])
];
