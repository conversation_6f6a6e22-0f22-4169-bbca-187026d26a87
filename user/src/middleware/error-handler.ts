import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import {
  AppError,
  ValidationError,
  AuthenticationError,
  InternalServerError,
} from "../utils/error";
import { Database, logger } from "../config";

// Interface for standardized error response
interface ErrorResponse {
  success: boolean;
  code: number;
  message: string;
  details?: any;
  error?: string; // Only included in development
}

export const errorHandler = async (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  let statusCode = (err as AppError).statusCode || 500;
  let response: ErrorResponse = {
    success: false,
    code: statusCode,
    message: "Internal server error",
  };

  // Handle specific error types
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    response = err.serialize();
  } else if (err.name === "JsonWebTokenError" || err.name === "TokenExpiredError") {
    statusCode = 401;
    response = new AuthenticationError("Invalid or expired token").serialize();
  } else if (err.name === "SequelizeUniqueConstraintError") {
    statusCode = 400;
    response = new ValidationError([], "Duplicate entry detected").serialize();
  } else {
    // Treat unhandled errors as internal server errors
    response = new InternalServerError().serialize();
  }

  // Log to audit_logs
  try {
    const requestId = req.request?.requestId || uuidv4();
    await Database.query(
      `INSERT INTO audit_logs (
        id, requestId, userId, method, route, statusCode,
        requestBody, responseBody, createdAt
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        uuidv4(),
        requestId,
        req.currentUser?.id || null,
        req.method,
        req.originalUrl.split("?").shift() || req.path,
        statusCode,
        req.request?.body || "{}",
        JSON.stringify(response),
        new Date(),
      ]
    );
  } catch (logError: unknown) {
    logger.error("Failed to log error to audit_logs", { error: (logError as Error).message });
  }

  // Log the error
  logger.error({
    error: err.name,
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    statusCode,
  });

  // Include detailed error in development
  if (process.env.NODE_ENV === "development") {
    response.error = err.stack || err.message;
  }

  // Send response
  res.status(statusCode).json(response);
};