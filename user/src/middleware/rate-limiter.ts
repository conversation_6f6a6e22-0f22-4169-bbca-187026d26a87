import rateLimit, {
	Op<PERSON>,
	RateLimitRequestHandler,
	MemoryStore,
} from "express-rate-limit";
import { NextFunction, Request, Response } from "express";

export const rateLimiter = (
	maxRequestsInOneMinute = 300,
): RateLimitRequestHandler => {
	return rateLimit({
		// Rate limiter configuration
		windowMs: 60 * 1000, // 1 minute
		max: maxRequestsInOneMinute,
		standardHeaders: false,
		legacyHeaders: false,
		store: new MemoryStore(),
		// Handle request in case limit is reached
		handler: async (
			request: Request,
			response: Response,
			next: NextFunction,
		) => {
			const respBody = {
				success: false,
				message: "Too Many Requests",
				detail: "Rate limit exceeded.",
				type: "tooManyRequests",
				code: 4291,
			};
			response.status(4291).send(respBody);
		},
	});
};
