import { Request, Response, NextFunction } from "express";
import { JwtPayload } from "jsonwebtoken";
import { AuthenticationError, AuthorizationError, NotFoundError } from "../utils/error";
import { decodeToken } from "../utils/jsonwebtoken";
import { Database } from "../config";

// Extend Express Request interface to include currentUser
interface AuthenticatedRequest extends Request {
  currentUser?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    displayName: string;
    role: string;
  };
}

// Interface for the expected user row from the database
interface UserRow {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  role: string;
  isEnabled: boolean;
}

export const isAuthenticated = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header or cookie
    let token: string | undefined;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.split(" ")[1];
    } else if (req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }

    if (!token) {
      throw new AuthenticationError("No token provided");
    }

    // Decode token
    const decoded = await decodeToken(token);
    if (!decoded || typeof decoded === "string") {
      throw new AuthenticationError("Invalid or expired token");
    }

    // Extract userId from decoded payload
    const { userId } = decoded as JwtPayload & { userId: string };
    if (!userId) {
      throw new AuthenticationError("Invalid token payload");
    }

    // Query user from database
    const result = await Database.query<UserRow>(
      "SELECT id, email, firstName, lastName, displayName, role, isEnabled FROM users WHERE id = $1",
      [userId]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError("User not found");
    }

    const user = result.rows[0];
    if (!user.isEnabled) {
      throw new AuthorizationError("User account is disabled");
    }

    // Attach user data to req.currentUser
    req.currentUser = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      displayName: user.displayName,
      role: user.role,
    };

    next();
  } catch (error) {
    throw error;
  }
};