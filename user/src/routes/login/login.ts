import express, { NextFunction, Request } from "express";
import { loginValidation } from "./login.validation";
import { responseHand<PERSON>, validateRequest } from "../../middleware";
import { NotFoundError } from "../../utils/error";

const router = express.Router();

router.post(
	"/v1/login",
	responseHandler,
	loginValidation,
	validateRequest,
	async (req: Request, res: any, next: NextFunction) => {
		// throw new NotFoundError("User not found in the db");

		res.sendResponse(
			{
				token: "sjdhfkjdshfdskjfhdsf",
				user: {
					id: "12345",
					email: "<EMAIL>",
					displayName: "hariom patidar",
				},
			},
			200,
			{},
		);
	},
);

export { router as loginV1Router };
