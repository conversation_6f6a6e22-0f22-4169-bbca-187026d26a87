import { ExpressValidatorWrapper } from "../../utils/express-validator-wrapper";

export const loginValidation = [
	...ExpressValidatorWrapper.emailValidator([
		{
			name: "email",
			mandatory: true,
			message: "Email must be a valid email.",
		},
	]),
	...ExpressValidatorWrapper.stringValidator([
		{
			name: "password",
			mandatory: true,
			minLength: 1,
			maxLength: 500,
			message: "Password must be string and of max 500 characters long.",
		},
	]),
];
