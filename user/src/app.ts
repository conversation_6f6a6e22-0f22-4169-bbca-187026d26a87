import express, { json, urlencoded } from "express";
import { errorHand<PERSON>, rateLimiter } from "./middleware";
import helmet from "helmet";
import hpp from "hpp";
import cookieParser from "cookie-parser";
import cors from "cors";
import router from "./routes";

const app = express();

app.use(rateLimiter());
app.use(helmet());
app.use(
	json({
		limit: "50mb",
	}),
);
app.use(
	urlencoded({
		extended: true,
		limit: "50mb",
	}),
);
app.use(hpp());
app.use(cookieParser());
app.use(
	cors({
		origin: "http://localhost:3000",
		credentials: true,
		preflightContinue: true,
	}),
);

app.use("/api", router);

app.use(errorHandler);

export default app;
