const { Sequelize } = require("sequelize");
const { config } = require("dotenv");
const { Umzug, SequelizeStorage } = require("umzug");
const { join } = require("path");

config(); // Load .env file

const database = process.env.DB_NAME || "";
const username = process.env.DB_USER || "";
const password = process.env.DB_PASSWORD || "";
const host = process.env.DB_HOST || "";

if (!database || !username || !password || !host) {
  throw new Error(
    "Missing required environment variables: DB_NAME, DB_USER, DB_PASSWORD, or DB_HOST"
  );
}

console.log("DB Config:", { database, username, password, host });
console.log("Migration directory:", join(__dirname, "migrations/*.js"));

const sequelize = new Sequelize(database, username, password, {
  host,
  dialect: "postgres",
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : 5432,
  logging: (msg) => console.log(msg),
});

const umzug = new Umzug({
  migrations: {
    glob: join(__dirname, "migrations/*.js"),
    resolve: ({ name, path, context }) => {
      console.log("Loading migration:", path);
      const migration = require(path);
      return {
        name,
        up: async () => migration.up({ context }),
        down: async () => migration.down({ context }),
      };
    },
  },
  context: sequelize.getQueryInterface(),
  storage: new SequelizeStorage({ sequelize }),
  logger: console,
});

(async () => {
  try {
    await sequelize.authenticate();
    console.log("Database connection established successfully.");

    const pending = await umzug.pending();
    console.log("Pending migrations:", pending.map((m) => m.name));

    const executed = await umzug.up();
    console.log(
      "Migrations performed successfully:",
      executed.map((m) => m.name)
    );
  } catch (error) {
    console.error("Error running migrations:", error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
})();