import app from "./app";
import { Database } from "./config";

// (async () => {
// 	await Database.init();
// })();

declare global {
	namespace Express {
		interface Request {
			currentUser?: {
				id: string;
				email: string;
				firstName: string;
				lastName: string;
				displayName: string;
				role: string;
			};
		}
	}
}

app.listen(5000, () => console.log("Server running on port 5000"));

process.on("uncaughtException", (err) => {
	console.error(err && err.stack);
});
