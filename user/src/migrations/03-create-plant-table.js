module.exports = {
  up: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW."updatedAt" = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';

      CREATE TABLE plants (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        plantName VARCHAR(100) NOT NULL,
        latitude DECIMAL(18,15) NOT NULL CHECK (latitude >= -90 AND latitude <= 90),
        longitude DECIMAL(18,15) NOT NULL CHECK (longitude >= -180 AND longitude <= 180),
        capacityKw DECIMAL(12,2) NOT NULL CHECK (capacityKw >= 0),
        status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
        installationDate DATE,
        address TEXT,
        ownerId UUID,
        panelCount INTEGER CHECK (panelCount >= 0),
        createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_plants_owner FOREIGN KEY(ownerId) REFERENCES users(id)
      );

      CREATE TRIGGER update_plants_updated_at
        BEFORE UPDATE ON plants
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

      CREATE INDEX idx_plants_owner_id ON plants(ownerId);
      CREATE INDEX idx_plants_status ON plants(status);
    `);
  }
};