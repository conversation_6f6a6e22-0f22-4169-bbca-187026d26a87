module.exports = {
  up: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      CREATE OR REPLACE FUNCTION update_updated_at_column()
      R<PERSON><PERSON>NS TRIGGER AS $$
      BEGIN
        NEW."updatedAt" = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';

      CREATE TABLE users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        firstName VARCHAR(50) NOT NULL,
        lastName VARCHAR(50) NOT NULL,
        displayName VARCHAR(101) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phoneNumber VARCHAR(20),
        address TEXT,
        role VARCHAR(50) NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin', 'super_admin')),
        profilePicture VARCHAR(255),
        isEnabled BOOLEAN NOT NULL DEFAULT true,
        isEmailVerified BOOLEAN NOT NULL DEFAULT false,
        emailVerifiedAt TIMESTAMP WITH TIME ZONE,
        otp VARCHAR(6),
        otpExpiry TIMESTAMP WITH TIME ZONE,
        token VARCHAR(255),
        tokenExpiry TIMESTAMP WITH TIME ZONE,
        plantIds UUID[] DEFAULT ARRAY[]::UUID[],
        loggedInDevices JSONB DEFAULT '[]'::JSONB,
        createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

      CREATE INDEX idx_users_email ON users(email);
    `);
  },
};