module.exports = {
 up: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      CREATE TABLE audit_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        requestId VARCHAR NOT NULL,
        userId UUID,
        method VARCHAR NOT NULL,
        route VARCHAR NOT NULL,
        statusCode INTEGER NOT NULL,
        requestBody JSONB,
        responseBody JSONB,
        targetType VARCHAR,
        targetId UUID,
        action VARCHAR,
        oldData JSONB,
        newData JSONB,
        modifiedProperties JSONB,
        createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_audit_logs_user FOREIGN KEY(userId) REFERENCES users(id)
      );

      CREATE INDEX idx_audit_logs_request_id ON audit_logs(requestId);
      CREATE INDEX idx_audit_logs_user_id ON audit_logs(userId);
      CREATE INDEX idx_audit_logs_target ON audit_logs(targetType, targetId);
      CREATE INDEX idx_audit_logs_created_at ON audit_logs(createdAt);
    `);
  },
};