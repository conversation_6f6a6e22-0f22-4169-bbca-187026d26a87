// import { Pool, PoolConfig, Query<PERSON><PERSON><PERSON>, QueryResultRow } from "pg";
// import pino from "pino";
// import { config } from "dotenv";

// // Load environment variables
// config();

// // Initialize logger
// const logger = pino({
//   level: process.env.LOG_LEVEL || "info",
//   timestamp: pino.stdTimeFunctions.isoTime,
// });

// // Interface for database configuration
// interface DbConfig {
//   host: string;
//   port: number;
//   database: string;
//   user: string;
//   password: string;
//   max?: number;
//   idleTimeoutMillis?: number;
//   connectionTimeoutMillis?: number;
// }

// // Database configuration with defaults
// const dbConfig: DbConfig = {
//   host: process.env.PGHOST || "localhost",
//   port: parseInt(process.env.PGPORT || "5432", 10),
//   database: process.env.PGDATABASE || "your_database",
//   user: process.env.PGUSER || "your_user",
//   password: process.env.PGPASSWORD || "your_password",
//   max: 20, // Maximum number of clients in the pool
//   idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
//   connectionTimeoutMillis: 5000, // Timeout after 5 seconds
// };

// // Validate required environment variables
// const validateConfig = (config: DbConfig): void => {
//   const required = ["host", "port", "database", "user", "password"] as const;
//   for (const key of required) {
//     if (!config[key]) {
//       throw new Error(`Missing required environment variable: ${key.toUpperCase()}`);
//     }
//   }
// };

// // Retry mechanism with exponential backoff
// const connectWithRetry = async (
//   pool: Pool,
//   maxRetries: number = 5,
//   baseDelay: number = 1000,
// ): Promise<void> => {
//   for (let attempt = 1; attempt <= maxRetries; attempt++) {
//     try {
//       // Test connection with a simple query
//       const result: QueryResult = await pool.query("SELECT NOW()");
//       logger.info(`Database connected successfully at ${result.rows[0].now}`);
//       return;
//     } catch (error: unknown) {
//       const err = error as Error;
//       if (attempt === maxRetries) {
//         logger.error(`Failed to connect to database after ${maxRetries} attempts: ${err.message}`);
//         throw err;
//       }
//       const delay = baseDelay * 2 ** (attempt - 1); // Exponential backoff
//       logger.warn(`Connection attempt ${attempt} failed: ${err.message}. Retrying in ${delay}ms...`);
//       await new Promise((resolve) => setTimeout(resolve, delay));
//     }
//   }
// };

// // Initialize database pool
// export class Database {
//   private static pool: Pool;

//   static async init(): Promise<void> {
//     try {
//       validateConfig(dbConfig);

//       const poolConfig: PoolConfig = {
//         ...dbConfig,
//         ssl: process.env.PGSSL === "true" ? { rejectUnauthorized: false } : false,
//       };

//       this.pool = new Pool(poolConfig);

//       // Set up event listeners
//       this.pool.on("connect", () => logger.info("New client connected to pool"));
//       this.pool.on("error", (err: Error) => {
//         logger.error(`Unexpected pool error: ${err.message}`);
//       });
//       this.pool.on("remove", () => logger.info("Client removed from pool"));

//       // Test connection with retry
//       await connectWithRetry(this.pool);

//       logger.info(`Pool initialized with ${this.pool.totalCount} total clients`);
//     } catch (error: unknown) {
//       const err = error as Error;
//       logger.error(`Failed to initialize database: ${err.message}`);
//       throw err;
//     }
//   }

//   static getPool(): Pool {
//     if (!this.pool) {
//       throw new Error("Database pool not initialized. Call Database.init() first.");
//     }
//     return this.pool;
//   }

//   static async query<T extends QueryResultRow = any>(text: string, params: any[] = []): Promise<QueryResult<T>> {
//     const pool = this.getPool();
//     try {
//       const start = Date.now();
//       const result = await pool.query<T>(text, params);
//       logger.debug(`Query executed in ${Date.now() - start}ms: ${text}`);
//       return result;
//     } catch (error: unknown) {
//       const err = error as Error;
//       logger.error(`Query failed: ${text}, Error: ${err.message}`);
//       throw err;
//     }
//   }

//   static async shutdown(): Promise<void> {
//     if (this.pool) {
//       try {
//         await this.pool.end();
//         logger.info("Database pool closed");
//       } catch (error: unknown) {
//         const err = error as Error;
//         logger.error(`Failed to close pool: ${err.message}`);
//         throw err;
//       }
//     }
//   }
// }

// // Handle process termination
// process.on("SIGTERM", async () => {
//   logger.info("Received SIGTERM. Shutting down database pool...");
//   await Database.shutdown();
//   process.exit(0);
// });

// process.on("SIGINT", async () => {
//   logger.info("Received SIGINT. Shutting down database pool...");
//   await Database.shutdown();
//   process.exit(0);
// });

import { Pool, PoolConfig, QueryResult, QueryResultRow } from "pg";
import pino from "pino";
import { config } from "dotenv";

// Load environment variables
config();

// Initialize and export logger
export const logger = pino({
  level: process.env.LOG_LEVEL || "info",
  timestamp: pino.stdTimeFunctions.isoTime,
});

// Interface for database configuration
interface DbConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  max?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

// Database configuration with defaults
const dbConfig: DbConfig = {
  host: process.env.PGHOST || "localhost",
  port: parseInt(process.env.PGPORT || "5432", 10),
  database: process.env.PGDATABASE || "your_database",
  user: process.env.PGUSER || "your_user",
  password: process.env.PGPASSWORD || "your_password",
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 5000, // Timeout after 5 seconds
};

// Validate required environment variables
const validateConfig = (config: DbConfig): void => {
  const required = ["host", "port", "database", "user", "password"] as const;
  for (const key of required) {
    if (!config[key]) {
      throw new Error(`Missing required environment variable: ${key.toUpperCase()}`);
    }
  }
};

// Retry mechanism with exponential backoff
const connectWithRetry = async (
  pool: Pool,
  maxRetries: number = 5,
  baseDelay: number = 1000,
): Promise<void> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Test connection with a simple query
      const result: QueryResult = await pool.query("SELECT NOW()");
      logger.info(`Database connected successfully at ${result.rows[0].now}`);
      return;
    } catch (error: unknown) {
      const err = error as Error;
      if (attempt === maxRetries) {
        logger.error(`Failed to connect to database after ${maxRetries} attempts: ${err.message}`);
        throw err;
      }
      const delay = baseDelay * 2 ** (attempt - 1); // Exponential backoff
      logger.warn(`Connection attempt ${attempt} failed: ${err.message}. Retrying in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
};

// Initialize database pool
export class Database {
  private static pool: Pool;

  static async init(): Promise<void> {
    try {
      validateConfig(dbConfig);

      const poolConfig: PoolConfig = {
        ...dbConfig,
        ssl: process.env.PGSSL === "true" ? { rejectUnauthorized: false } : false,
      };

      this.pool = new Pool(poolConfig);

      // Set up event listeners
      this.pool.on("connect", () => logger.info("New client connected to pool"));
      this.pool.on("error", (err: Error) => {
        logger.error(`Unexpected pool error: ${err.message}`);
      });
      this.pool.on("remove", () => logger.info("Client removed from pool"));

      // Test connection with retry
      await connectWithRetry(this.pool);

      logger.info(`Pool initialized with ${this.pool.totalCount} total clients`);
    } catch (error: unknown) {
      const err = error as Error;
      logger.error(`Failed to initialize database: ${err.message}`);
      throw err;
    }
  }

  static getPool(): Pool {
    if (!this.pool) {
      throw new Error("Database pool not initialized. Call Database.init() first.");
    }
    return this.pool;
  }

  static async query<T extends QueryResultRow = any>(text: string, params: any[] = []): Promise<QueryResult<T>> {
    const pool = this.getPool();
    try {
      const start = Date.now();
      const result = await pool.query<T>(text, params);
      logger.debug(`Query executed in ${Date.now() - start}ms: ${text}`);
      return result;
    } catch (error: unknown) {
      const err = error as Error;
      logger.error(`Query failed: ${text}, Error: ${err.message}`);
      throw err;
    }
  }

  static async shutdown(): Promise<void> {
    if (this.pool) {
      try {
        await this.pool.end();
        logger.info("Database pool closed");
      } catch (error: unknown) {
        const err = error as Error;
        logger.error(`Failed to close pool: ${err.message}`);
        throw err;
      }
    }
  }
}

// Handle process termination
process.on("SIGTERM", async () => {
  logger.info("Received SIGTERM. Shutting down database pool...");
  await Database.shutdown();
  process.exit(0);
});

process.on("SIGINT", async () => {
  logger.info("Received SIGINT. Shutting down database pool...");
  await Database.shutdown();
  process.exit(0);
});