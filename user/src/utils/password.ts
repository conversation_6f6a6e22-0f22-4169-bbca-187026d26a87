import * as crypto from "node:crypto";

const salt: string = process.env.SALT || "saltforpassword";

export const hashPassword = async (password: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    crypto.pbkdf2(password, salt, 100, 16, "sha512", (err: Error | null, derivedKey: Buffer) => {
      if (err) {
        reject(err);
      } else {
        resolve(derivedKey.toString("hex"));
      }
    });
  });
};

export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    crypto.pbkdf2(password, salt, 100, 16, "sha512", (err: Error | null, derivedKey: Buffer) => {
      if (err) {
        reject(err);
      } else {
        resolve(derivedKey.toString("hex") === hashedPassword);
      }
    });
  });
};