import jwt from "jsonwebtoken";
import { JwtPayload } from "../interfaces";

export const getToken = (data: JwtPayload, flag: boolean): string => {
	const secret = process.env.JWT_SECRET || "hariom";
	const expiresIn: number = flag
		? Number(process.env.JWT_EXPIRE!)
		: Number(process.env.RESET_PASSWORD_EXPIRE!);

	if (!secret) {
		throw new Error("JWT_SECRET is not defined");
	}

	const token = jwt.sign(data, secret, { expiresIn });
	return token;
};

export const decodeToken = (token: string): jwt.JwtPayload | string | false => {
	try {
		const secret = process.env.JWT_SECRET;
		if (!secret) {
			throw new Error("JWT_SECRET is not defined");
		}
		const decoded = jwt.verify(token, secret);
		return decoded;
	} catch (error: unknown) {
		return false;
	}
};
