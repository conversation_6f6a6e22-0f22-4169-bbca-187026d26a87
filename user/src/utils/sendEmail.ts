import * as nodemailer from "nodemailer";
import { EmailOptions } from "../interfaces";

export const sendEmail = async (options: EmailOptions): Promise<void> => {
	try {
		const { SMTP_HOST, SMTP_PORT, SMTP_MAIL, SMTP_PASSWORD } = process.env;

		if (!SMTP_HOST || !SMTP_PORT || !SMTP_MAIL || !SMTP_PASSWORD) {
			throw new Error("Missing required SMTP environment variables");
		}

		const transporter = nodemailer.createTransport({
			host: SMTP_HOST,
			port: parseInt(SMTP_PORT, 10),
			secure: false,
			auth: {
				user: SMTP_MAIL,
				pass: SMTP_PASSWORD,
			},
			logger: true,
			tls: {
				minVersion: "TLSv1.2" as "TLSv1.2",
			},
		});

		const mailOptions: nodemailer.SendMailOptions = {
			from: SMTP_MAIL,
			to: options.email,
			subject: options.subject,
			html: options.message,
		};

		const res = await transporter.sendMail(mailOptions);
		console.log("Mail response: " + JSON.stringify(res));
		console.log("Mail sent to: " + options.email, options.subject);
	} catch (error: unknown) {
		throw error;
	}
};
