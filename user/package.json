{"name": "user", "version": "1.0.0", "main": "index.js", "scripts": {"start:windows": "set NODE_ENV=development && ts-node-dev --poll src/index.ts", "start:ubuntu": "NODE_ENV=development ts-node-dev --poll src/index.ts", "start": "NODE_OPTIONS=--max_old_space_size=4096 node build/index.js", "build": "tsc", "dev": "ts-node-dev --poll src/index.ts", "dev:local": "ts-node-dev --no-notify --transpile-only --poll src/index.ts", "migration": "ts-node-dev --poll src/migration.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/nodemailer": "^6.4.17", "@types/sequelize": "^4.28.20", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "pino": "^9.7.0", "sequelize": "^6.37.7", "ts-node-dev": "^2.0.0", "umzug": "^3.8.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/hpp": "^0.2.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.19", "@types/pg": "^8.15.2", "nodemon": "^3.1.10", "typescript": "^5.8.3"}}