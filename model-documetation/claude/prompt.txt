i have to make a python model that will do my a lot of work it will a solar generation consumption forecasting model and based on that generation and consumption forecasting data we have to analyze how much more energy will be required by a area on the upcoming days now now here their will be give the user forecasted data based on three scenarios such that if it will be a normal consumer then i can share us its generation data if it has solar plant(roof top) installed then based on the generation data we will take its weather data this will be done by some other service an it will store the data in the timescaleDB for that user by processing and then adding weather paramerter in that data now if the user has given us this data then based on that data we will ask the forecasting generation to the model and model will give us the generation data of the future and then if it has the new plant means if does not have older data of the generation then our model will give the data based on its weather and our other data we have based on that data it will give means it will give based on the common data to that user based on its plant capacity now the next thing is main thing if the user is a energy provider means the companies that provides energy to the user and also accept the energy from them so for that users it must provide the data of its customers consumption and generation and also the other service will store this data with the weather data also now based on that data we have to predict the next days generation consumption data and how much energy more you will be required for this perticular area or how much more energy you will get from this area now here we will get the daily generation consumption data or we can get hourly data also and also we can get 15 min interval data also so the model must be handle all those things and also while asking the forecasting data we will give the capacity if the user is a consumer and also weather data of the consumer or the area for which we have to do the forecasting now the paramerter we will have are:
GHI, DNI,DHI,solar radiation, colud cover, humidity,timestamp, temperature, wind speed, precipitation, plant id or provider id , generation, consumption etc 
now according to you what all things we should do how we will make this project give me all things and also give me the schema file to keep on the db for the data according to you analyze this story and give me which fields we should keep what other fields we should add that will be required for this and the most important thing of this model that it should be able to learn online means for perticular user it will prodict based on its data only and if user provides its data then based on its data and if not then our systems common data and give me complete approach how we will do all things in this properly in a scalable app

now for this application for the first phase like for making its demo what i am thinking to do is that we will make our website and at their we will make a page to see the application generation forecasting feature so that what we will do is that we will make our lstm and transformer layer model through the data we have i have 4 different plants 2-3 years data so based on that we will train the models and over that model we will make api's to predict the generation now here the steps will be right now we will use timescaleDB so in this what we will do is that when the user will come on our website then it will see this page now in this forecasting page their will be a form having these fields latitude, longitute, Capacity in KW, date of installation (this field is because every year the solar panel is degrade with 4% so thatswhy we will keep this date) now what we will do is that we have an weatherbit weather forecasting api, now from this api we will take the parameters like GHI, temperature, DNI, DHI, solar irriation, temperature, humidity, precipitation etc and we will ask the model with this data now based on this affecting parameter it will give us the forecasted data of the upcoming days and on the frontend we will send its weather data also and also the forecasted data and will draw its chart firstly of generation forecasted data and then weather data also so give me firstly for this first phase data here we will make the frontend using react tailwind css and for the backend we will create the model and make python api's that will be called by our frontend and then it will send the response to the users.

so for this first phase give me complete code i have preprocess data for the model training and have the all keys like timestamp, generation and these weather parameters now this whole data is in 15 minutes interval and we also have to predict the 15 min interval data and if we will not use the timescale db right now then also it is okay because right now i have data in 4 different files so we have to read that files and then have to train the model properly so give me complete frontend and backend code with proer folder structure code for this first phase their is a folder names first-phase so make frontend and backend folders in it and work in that folders