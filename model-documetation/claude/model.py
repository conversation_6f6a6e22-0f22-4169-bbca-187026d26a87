# Solar Generation & Consumption Forecasting System
# Main Implementation Components

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import asyncio
import asyncpg
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Union
import logging
from dataclasses import dataclass
from enum import Enum

# Configuration and Data Models
class UserType(Enum):
    CONSUMER = "consumer"
    PROSUMER = "prosumer"
    PROVIDER = "provider"

class ForecastType(Enum):
    GENERATION = "generation"
    CONSUMPTION = "consumption"
    NET_ENERGY = "net_energy"

@dataclass
class ForecastConfig:
    user_id: str
    user_type: UserType
    forecast_type: ForecastType
    horizon_hours: int
    resolution_minutes: int = 60  # 15, 60 minutes
    confidence_level: float = 0.95

@dataclass
class WeatherData:
    timestamp: datetime
    ghi: float
    dni: float
    dhi: float
    temperature: float
    humidity: float
    cloud_cover: float
    wind_speed: float
    precipitation: float

# Database Connection Manager
class DatabaseManager:
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.pool = None
    
    async def initialize(self):
        self.pool = await asyncpg.create_pool(self.connection_string)
    
    async def get_weather_data(self, lat: float, lng: float, 
                             start_time: datetime, end_time: datetime) -> pd.DataFrame:
        async with self.pool.acquire() as conn:
            query = """
            SELECT timestamp, ghi, dni, dhi, temperature, humidity, 
                   cloud_cover, wind_speed, precipitation
            FROM weather_data 
            WHERE location_lat = $1 AND location_lng = $2 
            AND timestamp BETWEEN $3 AND $4
            ORDER BY timestamp
            """
            rows = await conn.fetch(query, lat, lng, start_time, end_time)
            return pd.DataFrame(rows)
    
    async def get_generation_data(self, user_id: str, plant_id: str,
                                start_time: datetime, end_time: datetime) -> pd.DataFrame:
        async with self.pool.acquire() as conn:
            query = """
            SELECT timestamp, generation_kwh, capacity_factor
            FROM generation_data 
            WHERE user_id = $1 AND plant_id = $2
            AND timestamp BETWEEN $3 AND $4
            ORDER BY timestamp
            """
            rows = await conn.fetch(query, user_id, plant_id, start_time, end_time)
            return pd.DataFrame(rows)
    
    async def get_consumption_data(self, user_id: str,
                                 start_time: datetime, end_time: datetime) -> pd.DataFrame:
        async with self.pool.acquire() as conn:
            query = """
            SELECT timestamp, consumption_kwh
            FROM consumption_data 
            WHERE user_id = $1 AND timestamp BETWEEN $2 AND $3
            ORDER BY timestamp
            """
            rows = await conn.fetch(query, user_id, start_time, end_time)
            return pd.DataFrame(rows)
    
    async def save_forecast(self, forecast_data: Dict):
        async with self.pool.acquire() as conn:
            query = """
            INSERT INTO forecasts (timestamp, forecast_timestamp, user_id, plant_id,
                                 forecast_type, forecast_horizon_hours, predicted_value,
                                 confidence_interval_lower, confidence_interval_upper,
                                 model_version)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """
            await conn.execute(query, *forecast_data.values())

# Feature Engineering Pipeline
class FeatureEngineer:
    def __init__(self):
        self.scalers = {}
        self.feature_columns = []
    
    def create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create time-based features"""
        df = df.copy()
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        
        # Cyclical encoding for temporal features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        return df
    
    def create_lag_features(self, df: pd.DataFrame, target_col: str, 
                          lags: List[int] = [1, 24, 168]) -> pd.DataFrame:
        """Create lag features for time series"""
        df = df.copy()
        for lag in lags:
            df[f'{target_col}_lag_{lag}h'] = df[target_col].shift(lag)
        return df
    
    def create_rolling_features(self, df: pd.DataFrame, target_col: str,
                              windows: List[int] = [24, 168]) -> pd.DataFrame:
        """Create rolling statistics features"""
        df = df.copy()
        for window in windows:
            df[f'{target_col}_rolling_mean_{window}h'] = df[target_col].rolling(window).mean()
            df[f'{target_col}_rolling_std_{window}h'] = df[target_col].rolling(window).std()
            df[f'{target_col}_rolling_max_{window}h'] = df[target_col].rolling(window).max()
            df[f'{target_col}_rolling_min_{window}h'] = df[target_col].rolling(window).min()
        return df
    
    def create_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create weather-based features"""
        df = df.copy()
        
        # Solar elevation angle approximation
        df['solar_elevation'] = np.sin(2 * np.pi * df['day_of_year'] / 365) * 23.45
        
        # Clear sky index
        df['clear_sky_index'] = df['ghi'] / (df['ghi'].rolling(24).max() + 1e-6)
        
        # Weather interaction features
        df['temp_humidity_interaction'] = df['temperature'] * df['humidity']
        df['ghi_cloud_interaction'] = df['ghi'] * (1 - df['cloud_cover'] / 100)
        df['wind_cooling_effect'] = df['wind_speed'] * df['temperature']
        
        return df
    
    def prepare_features(self, df: pd.DataFrame, target_cols: List[str]) -> pd.DataFrame:
        """Complete feature engineering pipeline"""
        df = self.create_time_features(df)
        df = self.create_weather_features(df)
        
        for target_col in target_cols:
            if target_col in df.columns:
                df = self.create_lag_features(df, target_col)
                df = self.create_rolling_features(df, target_col)
        
        return df
    
    def fit_scalers(self, df: pd.DataFrame, feature_cols: List[str]):
        """Fit scalers on training data"""
        for col in feature_cols:
            if col in df.columns:
                scaler = StandardScaler()
                scaler.fit(df[[col]].dropna())
                self.scalers[col] = scaler
    
    def transform_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform features using fitted scalers"""
        df_scaled = df.copy()
        for col, scaler in self.scalers.items():
            if col in df_scaled.columns:
                df_scaled[col] = scaler.transform(df_scaled[[col]])
        return df_scaled

# LSTM Model for Time Series Forecasting
class LSTMForecastModel(nn.Module):
    def __init__(self, input_size: int, hidden_size: int = 128, 
                 num_layers: int = 2, output_size: int = 1, dropout: float = 0.2):
        super(LSTMForecastModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=dropout)
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, dropout=dropout)
        self.fc = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, output_size)
        )
        
    def forward(self, x):
        # LSTM forward pass
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # Attention mechanism
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # Take the last output
        output = self.fc(attn_out[:, -1, :])
        return output

# Multi-task Learning Model for Prosumers
class MultiTaskForecastModel(nn.Module):
    def __init__(self, input_size: int, hidden_size: int = 128):
        super(MultiTaskForecastModel, self).__init__()
        
        # Shared encoder
        self.shared_encoder = nn.LSTM(input_size, hidden_size, 2, 
                                    batch_first=True, dropout=0.2)
        
        # Task-specific heads
        self.generation_head = nn.Linear(hidden_size, 1)
        self.consumption_head = nn.Linear(hidden_size, 1)
        
    def forward(self, x):
        # Shared representation
        lstm_out, _ = self.shared_encoder(x)
        last_hidden = lstm_out[:, -1, :]
        
        # Task-specific predictions
        generation_pred = self.generation_head(last_hidden)
        consumption_pred = self.consumption_head(last_hidden)
        
        return generation_pred, consumption_pred

# Custom Dataset for Time Series
class TimeSeriesDataset(Dataset):
    def __init__(self, data: pd.DataFrame, feature_cols: List[str], 
                 target_cols: List[str], sequence_length: int = 24):
        self.data = data.dropna()
        self.feature_cols = feature_cols
        self.target_cols = target_cols
        self.sequence_length = sequence_length
        
        # Prepare sequences
        self.X, self.y = self._create_sequences()
    
    def _create_sequences(self):
        X, y = [], []
        for i in range(len(self.data) - self.sequence_length):
            # Feature sequence
            X_seq = self.data[self.feature_cols].iloc[i:i+self.sequence_length].values
            # Target (next value)
            y_seq = self.data[self.target_cols].iloc[i+self.sequence_length].values
            
            X.append(X_seq)
            y.append(y_seq)
        
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return torch.tensor(self.X[idx]), torch.tensor(self.y[idx])

# Online Learning Manager
class OnlineLearningManager:
    def __init__(self, model: nn.Module, learning_rate: float = 0.001):
        self.model = model
        self.optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        self.criterion = nn.MSELoss()
        self.performance_history = []
    
    def update_model(self, new_data: torch.Tensor, targets: torch.Tensor):
        """Perform one step of online learning"""
        self.model.train()
        self.optimizer.zero_grad()
        
        predictions = self.model(new_data)
        loss = self.criterion(predictions, targets)
        
        loss.backward()
        self.optimizer.step()
        
        return loss.item()
    
    def evaluate_and_adapt(self, validation_data: DataLoader):
        """Evaluate model and adapt learning rate if needed"""
        self.model.eval()
        total_loss = 0
        with torch.no_grad():
            for X, y in validation_data:
                pred = self.model(X)
                loss = self.criterion(pred, y)
                total_loss += loss.item()
        
        avg_loss = total_loss / len(validation_data)
        self.performance_history.append(avg_loss)
        
        # Adaptive learning rate
        if len(self.performance_history) > 5:
            recent_trend = np.mean(self.performance_history[-3:]) - np.mean(self.performance_history[-6:-3])
            if recent_trend > 0:  # Performance degrading
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] *= 0.9
        
        return avg_loss

# Model Factory and Manager
class ModelManager:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.models = {}
        self.feature_engineer = FeatureEngineer()
        self.online_learners = {}
    
    def create_model(self, user_type: UserType, input_size: int) -> nn.Module:
        """Factory method to create appropriate model based on user type"""
        if user_type == UserType.CONSUMER:
            return LSTMForecastModel(input_size, hidden_size=64, output_size=1)
        elif user_type == UserType.PROSUMER:
            return MultiTaskForecastModel(input_size, hidden_size=128)
        elif user_type == UserType.PROVIDER:
            return LSTMForecastModel(input_size, hidden_size=256, num_layers=3)
    
    async def get_or_create_model(self, user_id: str, user_type: UserType) -> nn.Module:
        """Get existing model or create new one for user"""
        if user_id not in self.models:
            # Load historical data to determine input size
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            # Get sample data to determine feature size
            sample_data = await self._prepare_sample_data(user_id, start_time, end_time)
            input_size = len(sample_data.columns) - 1  # Exclude target column
            
            # Create and initialize model
            model = self.create_model(user_type, input_size)
            self.models[user_id] = model
            
            # Initialize online learner
            self.online_learners[user_id] = OnlineLearningManager(model)
            
            # Train initial model if sufficient data exists
            if len(sample_data) > 168:  # At least 1 week of data
                await self._train_initial_model(user_id, sample_data, user_type)
        
        return self.models[user_id]
    
    async def _prepare_sample_data(self, user_id: str, start_time: datetime, 
                                 end_time: datetime) -> pd.DataFrame:
        """Prepare sample data for feature engineering"""
        # This is a simplified version - you'd combine weather, generation, consumption data
        consumption_data = await self.db_manager.get_consumption_data(user_id, start_time, end_time)
        # Add weather data, generation data as needed
        return consumption_data
    
    async def _train_initial_model(self, user_id: str, data: pd.DataFrame, user_type: UserType):
        """Train initial model on historical data"""
        model = self.models[user_id]
        online_learner = self.online_learners[user_id]
        
        # Prepare features
        feature_cols = [col for col in data.columns if col != 'timestamp']
        target_cols = ['consumption_kwh']  # Adjust based on user type
        
        # Create dataset
        dataset = TimeSeriesDataset(data, feature_cols, target_cols)
        dataloader = DataLoader(dataset, batch_size=32, shuffle=True)
        
        # Training loop
        model.train()
        for epoch in range(50):  # Initial training epochs
            for batch_X, batch_y in dataloader:
                loss = online_learner.update_model(batch_X, batch_y)
    
    async def predict(self, config: ForecastConfig) -> Dict:
        """Generate forecast based on configuration"""
        model = await self.get_or_create_model(config.user_id, config.user_type)
        
        # Prepare input data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=168)  # Last week of data
        
        input_data = await self._prepare_forecast_input(config, start_time, end_time)
        
        # Generate predictions
        model.eval()
        with torch.no_grad():
            # Convert to tensor and predict
            X = torch.tensor(input_data.values, dtype=torch.float32).unsqueeze(0)
            
            if config.user_type == UserType.PROSUMER:
                generation_pred, consumption_pred = model(X)
                predictions = {
                    'generation': generation_pred.item(),
                    'consumption': consumption_pred.item(),
                    'net_energy': generation_pred.item() - consumption_pred.item()
                }
            else:
                pred = model(X)
                predictions = {config.forecast_type.value: pred.item()}
        
        return {
            'user_id': config.user_id,
            'forecast_timestamp': datetime.now(),
            'predictions': predictions,
            'horizon_hours': config.horizon_hours,
            'confidence_level': config.confidence_level
        }
    
    async def _prepare_forecast_input(self, config: ForecastConfig, 
                                    start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Prepare input data for forecasting"""
        # Get historical data
        if config.user_type in [UserType.PROSUMER, UserType.PROVIDER]:
            generation_data = await self.db_manager.get_generation_data(
                config.user_id, None, start_time, end_time)
        
        consumption_data = await self.db_manager.get_consumption_data(
            config.user_id, start_time, end_time)
        
        # Get weather data (you'd need location info)
        # weather_data = await self.db_manager.get_weather_data(lat, lng, start_time, end_time)
        
        # Combine and engineer features
        combined_data = consumption_data  # Simplified - combine all data sources
        featured_data = self.feature_engineer.prepare_features(
            combined_data, ['consumption_kwh'])
        
        return featured_data.dropna().tail(24)  # Last 24 hours as input

# Forecast Service - Main API
class ForecastService:
    def __init__(self, db_connection_string: str):
        self.db_manager = DatabaseManager(db_connection_string)
        self.model_manager = ModelManager(self.db_manager)
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize the service"""
        await self.db_manager.initialize()
        self.logger.info("Forecast service initialized")
    
    async def generate_forecast(self, config: ForecastConfig) -> Dict:
        """Main entry point for generating forecasts"""
        try:
            # Validate configuration
            self._validate_config(config)
            
            # Generate forecast
            forecast_result = await self.model_manager.predict(config)
            
            # Save forecast to database
            await self._save_forecast_result(forecast_result, config)
            
            # Add confidence intervals (simplified)
            forecast_result = self._add_confidence_intervals(forecast_result, config)
            
            return forecast_result
            
        except Exception as e:
            self.logger.error(f"Forecast generation failed: {str(e)}")
            raise
    
    def _validate_config(self, config: ForecastConfig):
        """Validate forecast configuration"""
        if config.horizon_hours < 1 or config.horizon_hours > 168:
            raise ValueError("Forecast horizon must be between 1 and 168 hours")
        
        if config.resolution_minutes not in [15, 60]:
            raise ValueError("Resolution must be 15 or 60 minutes")
    
    async def _save_forecast_result(self, result: Dict, config: ForecastConfig):
        """Save forecast result to database"""
        forecast_data = {
            'timestamp': datetime.now() + timedelta(hours=config.horizon_hours),
            'forecast_timestamp': result['forecast_timestamp'],
            'user_id': config.user_id,
            'plant_id': None,  # Would be set for prosumers
            'forecast_type': config.forecast_type.value,
            'forecast_horizon_hours': config.horizon_hours,
            'predicted_value': list(result['predictions'].values())[0],
            'confidence_interval_lower': None,
            'confidence_interval_upper': None,
            'model_version': 'v1.0'
        }
        
        await self.db_manager.save_forecast(forecast_data)
    
    def _add_confidence_intervals(self, result: Dict, config: ForecastConfig) -> Dict:
        """Add confidence intervals to predictions (simplified)"""
        for key, value in result['predictions'].items():
            # Simple approach - use fixed percentage for confidence intervals
            uncertainty = abs(value) * 0.15  # 15% uncertainty
            result['predictions'][key] = {
                'value': value,
                'lower_bound': value - uncertainty,
                'upper_bound': value + uncertainty
            }
        
        return result
    
    async def update_model_online(self, user_id: str, actual_data: Dict):
        """Update model with new actual data for online learning"""
        if user_id in self.model_manager.online_learners:
            online_learner = self.model_manager.online_learners[user_id]
            
            # Convert actual data to tensor format
            # This is simplified - you'd need proper feature engineering
            new_data = torch.tensor([[actual_data['value']]], dtype=torch.float32)
            target = torch.tensor([[actual_data['target']]], dtype=torch.float32)
            
            # Update model
            loss = online_learner.update_model(new_data, target)
            self.logger.info(f"Model updated for user {user_id}, loss: {loss}")
    
    async def get_energy_balance_forecast(self, provider_id: str, 
                                        area_lat: float, area_lng: float,
                                        horizon_hours: int = 24) -> Dict:
        """Special forecast for energy providers to predict area energy balance"""
        # Get all customers in the area
        customers = await self._get_customers_in_area(provider_id, area_lat, area_lng)
        
        total_generation_forecast = 0
        total_consumption_forecast = 0
        
        # Aggregate forecasts for all customers
        for customer in customers:
            config = ForecastConfig(
                user_id=customer['user_id'],
                user_type=UserType(customer['user_type']),
                forecast_type=ForecastType.CONSUMPTION,
                horizon_hours=horizon_hours
            )
            
            customer_forecast = await self.generate_forecast(config)
            total_consumption_forecast += customer_forecast['predictions']['consumption']['value']
            
            if customer['user_type'] == 'prosumer':
                config.forecast_type = ForecastType.GENERATION
                generation_forecast = await self.generate_forecast(config)
                total_generation_forecast += generation_forecast['predictions']['generation']['value']
        
        energy_balance = total_generation_forecast - total_consumption_forecast
        
        return {
            'provider_id': provider_id,
            'area_lat': area_lat,
            'area_lng': area_lng,
            'forecast_timestamp': datetime.now(),
            'horizon_hours': horizon_hours,
            'total_generation_forecast': total_generation_forecast,
            'total_consumption_forecast': total_consumption_forecast,
            'energy_balance': energy_balance,
            'additional_energy_needed': max(0, -energy_balance),
            'excess_energy_available': max(0, energy_balance)
        }
    
    async def _get_customers_in_area(self, provider_id: str, 
                                   area_lat: float, area_lng: float) -> List[Dict]:
        """Get all customers in a specific geographical area"""
        # This would query the database for customers within a certain radius
        # Simplified implementation
        return [
            {'user_id': 'customer1', 'user_type': 'consumer'},
            {'user_id': 'customer2', 'user_type': 'prosumer'}
        ]

# Weather Integration Service
class WeatherService:
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    async def fetch_weather_data(self, lat: float, lng: float, 
                               start_time: datetime, end_time: datetime) -> List[WeatherData]:
        """Fetch weather data from external API"""
        # This would integrate with actual weather APIs like OpenWeatherMap, etc.
        # Simplified mock implementation
        weather_data = []
        current_time = start_time
        
        while current_time <= end_time:
            # Mock weather data
            weather_point = WeatherData(
                timestamp=current_time,
                ghi=np.random.normal(400, 100),
                dni=np.random.normal(300, 80),
                dhi=np.random.normal(100, 30),
                temperature=np.random.normal(25, 5),
                humidity=np.random.normal(60, 15),
                cloud_cover=np.random.normal(30, 20),
                wind_speed=np.random.normal(5, 2),
                precipitation=np.random.exponential(0.1)
            )
            weather_data.append(weather_point)
            current_time += timedelta(hours=1)
        
        return weather_data

# Performance Monitor
class PerformanceMonitor:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.metrics_history = {}
    
    async def evaluate_forecast_accuracy(self, user_id: str, 
                                       forecast_type: ForecastType,
                                       evaluation_period_hours: int = 24) -> Dict:
        """Evaluate forecast accuracy against actual data"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=evaluation_period_hours)
        
        # Get forecasts made for this period
        forecasts = await self._get_forecasts(user_id, forecast_type, start_time, end_time)
        
        # Get actual data for comparison
        if forecast_type == ForecastType.GENERATION:
            actual_data = await self.db_manager.get_generation_data(
                user_id, None, start_time, end_time)
            actual_values = actual_data['generation_kwh'].values
        else:
            actual_data = await self.db_manager.get_consumption_data(
                user_id, start_time, end_time)
            actual_values = actual_data['consumption_kwh'].values
        
        # Calculate metrics
        if len(forecasts) > 0 and len(actual_values) > 0:
            predicted_values = [f['predicted_value'] for f in forecasts]
            
            mae = mean_absolute_error(actual_values[:len(predicted_values)], predicted_values)
            mse = mean_squared_error(actual_values[:len(predicted_values)], predicted_values)
            mape = np.mean(np.abs((actual_values[:len(predicted_values)] - predicted_values) / 
                                actual_values[:len(predicted_values)])) * 100
            
            metrics = {
                'user_id': user_id,
                'forecast_type': forecast_type.value,
                'evaluation_period': evaluation_period_hours,
                'mae': mae,
                'mse': mse,
                'rmse': np.sqrt(mse),
                'mape': mape,
                'num_predictions': len(predicted_values)
            }
            
            # Store metrics history
            if user_id not in self.metrics_history:
                self.metrics_history[user_id] = []
            self.metrics_history[user_id].append(metrics)
            
            return metrics
        
        return {'error': 'Insufficient data for evaluation'}
    
    async def _get_forecasts(self, user_id: str, forecast_type: ForecastType,
                           start_time: datetime, end_time: datetime) -> List[Dict]:
        """Get forecast data from database"""
        # Simplified - would query forecasts table
        return []

# Main Application Class
class SolarForecastingApp:
    def __init__(self, config: Dict):
        self.config = config
        self.forecast_service = ForecastService(config['database_url'])
        self.weather_service = WeatherService(config['weather_api_key'])
        self.performance_monitor = PerformanceMonitor(self.forecast_service.db_manager)
        
    async def initialize(self):
        """Initialize the application"""
        await self.forecast_service.initialize()
        logging.info("Solar Forecasting Application initialized")
    
    async def run_forecast_job(self, user_id: str, user_type: str, 
                             forecast_type: str, horizon_hours: int = 24):
        """Run a forecast job"""
        config = ForecastConfig(
            user_id=user_id,
            user_type=UserType(user_type),
            forecast_type=ForecastType(forecast_type),
            horizon_hours=horizon_hours
        )
        
        result = await self.forecast_service.generate_forecast(config)
        return result
    
    async def run_batch_forecasts(self, user_configs: List[Dict]):
        """Run batch forecasts for multiple users"""
        tasks = []
        for user_config in user_configs:
            task = self.run_forecast_job(**user_config)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results

# Example Usage
async def main():
    """Example usage of the Solar Forecasting System"""
    
    # Configuration
    config = {
        'database_url': 'postgresql://user:password@localhost/solar_db',
        'weather_api_key': 'your_weather_api_key'
    }
    
    # Initialize application
    app = SolarForecastingApp(config)
    await app.initialize()
    
    # Example 1: Consumer forecast
    consumer_result = await app.run_forecast_job(
        user_id='consumer_123',
        user_type='consumer',
        forecast_type='consumption',
        horizon_hours=24
    )
    print("Consumer Forecast:", consumer_result)
    
    # Example 2: Prosumer forecast
    prosumer_result = await app.run_forecast_job(
        user_id='prosumer_456',
        user_type='prosumer',
        forecast_type='net_energy',
        horizon_hours=48
    )
    print("Prosumer Forecast:", prosumer_result)
    
    # Example 3: Energy provider area forecast
    area_forecast = await app.forecast_service.get_energy_balance_forecast(
        provider_id='provider_789',
        area_lat=40.7128,
        area_lng=-74.0060,
        horizon_hours=24
    )
    print("Area Energy Balance:", area_forecast)
    
    # Example 4: Model performance evaluation
    performance = await app.performance_monitor.evaluate_forecast_accuracy(
        user_id='prosumer_456',
        forecast_type=ForecastType.GENERATION,
        evaluation_period_hours=168
    )
    print("Model Performance:", performance)

# Run the example
if __name__ == "__main__":
    asyncio.run(main())