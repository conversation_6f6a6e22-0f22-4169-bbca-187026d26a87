# Solar Forecasting System - API Layer & Deployment Configuration

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import uvicorn
import redis
import json
from celery import Celery
import os
from prometheus_client import Counter, Histogram, generate_latest
import time

# Pydantic Models for API
class ForecastRequest(BaseModel):
    user_id: str = Field(..., description="Unique user identifier")
    user_type: str = Field(..., regex="^(consumer|prosumer|provider)$")
    forecast_type: str = Field(..., regex="^(generation|consumption|net_energy)$")
    horizon_hours: int = Field(24, ge=1, le=168, description="Forecast horizon in hours")
    resolution_minutes: int = Field(60, regex="^(15|60)$", description="Data resolution")
    confidence_level: float = Field(0.95, ge=0.8, le=0.99)
    scenarios: List[str] = Field(["normal"], description="Weather scenarios")

class ForecastResponse(BaseModel):
    user_id: str
    forecast_timestamp: datetime
    horizon_hours: int
    predictions: Dict[str, Any]
    confidence_intervals: Dict[str, Dict[str, float]]
    model_version: str
    accuracy_score: Optional[float] = None

class BatchForecastRequest(BaseModel):
    forecasts: List[ForecastRequest]
    priority: str = Field("normal", regex="^(low|normal|high)$")

class WeatherData(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    timestamp: datetime
    ghi: float = Field(..., ge=0)
    dni: float = Field(..., ge=0)
    dhi: float = Field(..., ge=0)
    temperature: float
    humidity: float = Field(..., ge=0, le=100)
    cloud_cover: float = Field(..., ge=0, le=100)
    wind_speed: float = Field(..., ge=0)
    precipitation: float = Field(..., ge=0)

class ModelUpdateRequest(BaseModel):
    user_id: str
    actual_values: Dict[str, float]
    timestamp: datetime

class AreaForecastRequest(BaseModel):
    provider_id: str
    area_lat: float = Field(..., ge=-90, le=90)
    area_lng: float = Field(..., ge=-180, le=180)
    radius_km: float = Field(10, ge=1, le=100)
    horizon_hours: int = Field(24, ge=1, le=168)

# Redis Cache Manager
class CacheManager:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1 hour
    
    def get_forecast_cache_key(self, user_id: str, forecast_type: str, horizon: int) -> str:
        return f"forecast:{user_id}:{forecast_type}:{horizon}"
    
    async def get_cached_forecast(self, cache_key: str) -> Optional[Dict]:
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception:
            pass
        return None
    
    async def cache_forecast(self, cache_key: str, forecast_data: Dict, ttl: int = None):
        try:
            ttl = ttl or self.default_ttl
            self.redis_client.setex(cache_key, ttl, json.dumps(forecast_data, default=str))
        except Exception:
            pass  # Don't fail if caching fails
    
    async def invalidate_user_cache(self, user_id: str):
        try:
            keys = self.redis_client.keys(f"forecast:{user_id}:*")
            if keys:
                self.redis_client.delete(*keys)
        except Exception:
            pass

# Celery Configuration for Background Tasks
celery_app = Celery(
    'solar_forecasting',
    broker='redis://localhost:6379',
    backend='redis://localhost:6379'
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_routes={
        'solar_forecasting.batch_forecast': {'queue': 'batch'},
        'solar_forecasting.model_training': {'queue': 'training'},
        'solar_forecasting.data_processing': {'queue': 'processing'}
    }
)

# Background Tasks
@celery_app.task
def batch_forecast_task(forecast_requests: List[Dict]):
    """Process batch forecasts in background"""
    # This would use the SolarForecastingApp
    results = []
    for request in forecast_requests:
        # Process each forecast
        result = {"user_id": request["user_id"], "status": "completed"}
        results.append(result)
    return results

@celery_app.task
def model_retraining_task(user_id: str):
    """Retrain model for specific user"""
    # This would trigger model retraining
    return {"user_id": user_id, "status": "retrained"}

@celery_app.task
def data_quality_check_task():
    """Check data quality and alert if issues found"""
    # Implement data quality checks
    return {"status": "completed", "issues_found": 0}

# Metrics for Monitoring
REQUEST_COUNT = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('api_request_duration_seconds', 'Request duration')
FORECAST_ACCURACY = Histogram('forecast_accuracy', 'Forecast accuracy metrics')

# FastAPI Application
app = FastAPI(
    title="Solar Generation & Consumption Forecasting API",
    description="ML-powered forecasting system for solar energy generation and consumption",
    version="1.0.0"
)

# CORS Configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
cache_manager = CacheManager()
solar_app = None  # Will be initialized on startup

# Dependency for rate limiting
class RateLimiter:
    def __init__(self, calls: int = 100, period: int = 3600):
        self.calls = calls
        self.period = period
        self.redis_client = redis.Redis()
    
    def __call__(self, request):
        client_ip = request.client.host
        key = f"rate_limit:{client_ip}"
        
        current = self.redis_client.get(key)
        if current is None:
            self.redis_client.setex(key, self.period, 1)
            return True
        
        if int(current) >= self.calls:
            raise HTTPException(status_code=429, detail="Rate limit exceeded")
        
        self.redis_client.incr(key)
        return True

rate_limiter = RateLimiter()

# Startup and Shutdown Events
@app.on_event("startup")
async def startup_event():
    global solar_app
    
    config = {
        'database_url': os.getenv('DATABASE_URL', 'postgresql://user:password@localhost/solar_db'),
        'weather_api_key': os.getenv('WEATHER_API_KEY', 'your_api_key')
    }
    
    from solar_forecasting_system import SolarForecastingApp
    solar_app = SolarForecastingApp(config)
    await solar_app.initialize()

@app.on_event("shutdown")
async def shutdown_event():
    # Cleanup resources
    pass

# API Endpoints

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "1.0.0"
    }

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest()

@app.post("/forecast", response_model=ForecastResponse)
async def generate_forecast(
    request: ForecastRequest,
    background_tasks: BackgroundTasks,
    _: bool = Depends(rate_limiter)
):
    """Generate forecast for a single user"""
    start_time = time.time()
    REQUEST_COUNT.labels(method="POST", endpoint="/forecast").inc()
    
    try:
        # Check cache first
        cache_key = cache_manager.get_forecast_cache_key(
            request.user_id, request.forecast_type, request.horizon_hours
        )
        cached_result = await cache_manager.get_cached_forecast(cache_key)
        
        if cached_result:
            return ForecastResponse(**cached_result)
        
        # Generate new forecast
        forecast_result = await solar_app.run_forecast_job(
            user_id=request.user_id,
            user_type=request.user_type,
            forecast_type=request.forecast_type,
            horizon_hours=request.horizon_hours
        )
        
        # Cache the result
        await cache_manager.cache_forecast(cache_key, forecast_result)
        
        # Schedule background model update if needed
        background_tasks.add_task(check_model_performance, request.user_id)
        
        response = ForecastResponse(
            user_id=forecast_result['user_id'],
            forecast_timestamp=forecast_result['forecast_timestamp'],
            horizon_hours=forecast_result.get('horizon_hours', request.horizon_hours),
            predictions=forecast_result['predictions'],
            confidence_intervals=forecast_result.get('confidence_intervals', {}),
            model_version="v1.0"
        )
        
        REQUEST_DURATION.observe(time.time() - start_time)
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Forecast generation failed: {str(e)}")

@app.post("/forecast/batch")
async def generate_batch_forecasts(request: BatchForecastRequest):
    """Generate forecasts for multiple users"""
    REQUEST_COUNT.labels(method="POST", endpoint="/forecast/batch").inc()
    
    try:
        # Submit to Celery for background processing
        task = batch_forecast_task.delay([req.dict() for req in request.forecasts])
        
        return {
            "task_id": task.id,
            "status": "submitted",
            "estimated_completion": datetime.now() + timedelta(minutes=5),
            "num_forecasts": len(request.forecasts)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch forecast submission failed: {str(e)}")

@app.get("/forecast/batch/{task_id}")
async def get_batch_forecast_status(task_id: str):
    """Get status of batch forecast task"""
    try:
        task = celery_app.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {'status': 'pending'}
        elif task.state == 'SUCCESS':
            response = {'status': 'completed', 'result': task.result}
        else:
            response = {'status': task.state, 'error': str(task.info)}
            
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task status check failed: {str(e)}")

@app.post("/forecast/area")
async def generate_area_forecast(request: AreaForecastRequest):
    """Generate area-wide energy balance forecast for providers"""
    REQUEST_COUNT.labels(method="POST", endpoint="/forecast/area").inc()
    
    try:
        area_forecast = await solar_app.forecast_service.get_energy_balance_forecast(
            provider_id=request.provider_id,
            area_lat=request.area_lat,
            area_lng=request.area_lng,
            horizon_hours=request.horizon_hours
        )
        
        return area_forecast
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Area forecast failed: {str(e)}")

@app.post("/model/update")
async def update_model_online(request: ModelUpdateRequest):
    """Update model with new actual data for online learning"""
    REQUEST_COUNT.labels(method="POST", endpoint="/model/update").inc()
    
    try:
        await solar_app.forecast_service.update_model_online(
            user_id=request.user_id,
            actual_data=request.actual_values
        )
        
        # Invalidate cache for this